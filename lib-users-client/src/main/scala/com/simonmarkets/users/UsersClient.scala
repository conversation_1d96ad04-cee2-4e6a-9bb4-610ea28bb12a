package com.simonmarkets.users

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.User
import com.goldmansachs.marquee.pipg.service.user.UsersMsg.{Insert, Update}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.{FutureHttpClient, HttpClientConfig}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.resteasy.authn.config.SourceConfig
import com.typesafe.config.Config
import io.circe.{Encoder, Json}
import pureconfig.generic.FieldCoproductHint
import pureconfig.generic.auto._

import scala.concurrent.{ExecutionContext, Future}

case class UserClientConfig(
    httpClient: HttpClientConfig,
    apiPrefix: String
)

object UserClientConfig {

  implicit val sourceConfigHint: FieldCoproductHint[SourceConfig] = new FieldCoproductHint[SourceConfig](key = "type")

  def apply(config: Config): UserClientConfig = pureconfig.loadConfigOrThrow[UserClientConfig](config)
}

object HttpUsersClient {
  def apply(config: UserClientConfig)
    (implicit ec: ExecutionContext, mat: Materializer, as: ActorSystem): HttpUsersClient = {
    val client = new FutureHttpClient(Http(), config.httpClient)
    new HttpUsersClient(client, config.apiPrefix)
  }
}

trait UsersClient {
  def findUserByDistributorId(distributorId: String)(implicit traceId: TraceId): Future[Iterable[User.Secured]]

  def findByEmail(email: String)(implicit traceId: TraceId): Future[Iterable[User.Secured]]

  def findByOmsAlias(oms: String)(implicit traceId: TraceId): Future[Iterable[User.Secured]]

  def create(user: Insert)(implicit traceId: TraceId): Future[User.Secured]

  def update(user: Update)(implicit traceId: TraceId): Future[User.Secured]

  def expire(userId: String)(implicit traceId: TraceId): Future[Map[String, String]]

}

class HttpUsersClient(client: FutureHttpClient, path: String)(implicit ec: ExecutionContext, mat: Materializer)
  extends UsersClient with JsonCodecs with TraceLogging {

  implicit val encodeAny: Encoder[Map[String, AnyRef]] = _ => Json.Null

  override def findUserByDistributorId(distributorId: String)
    (implicit traceId: TraceId): Future[Iterable[User.Secured]] = {
    val url = s"$path/v1/users?distributorId=$distributorId"
    client.get[Iterable[User.Secured]](url)
  }

  override def create(user: Insert)(implicit traceId: TraceId): Future[User.Secured] = {
    val url = s"$path/v1/users"
    client.post[Insert, User.Secured](url, user)
  }

  override def update(user: Update)(implicit traceId: TraceId): Future[User.Secured] = {
    val url = s"$path/v1/users/${user.id}"
    client.put[Update, User.Secured](url, user)
  }

  override def findByEmail(email: String)(implicit traceId: TraceId): Future[Iterable[User.Secured]] = {
    val url = s"$path/v1/users?email=$email"
    client.get[Iterable[User.Secured]](url)
  }

  override def findByOmsAlias(oms: String)(implicit traceId: TraceId): Future[Iterable[User.Secured]] = {
    val url = s"$path/v1/users?omsId=$oms"
    client.get[Iterable[User.Secured]](url)
  }

  override def expire(userId: String)(implicit traceId: TraceId): Future[Map[String, String]] = {
    val url = s"$path/v1/users/$userId/expire"
    client.post[Map[String, String], Map[String, String]](url, Map.empty)
  }

}