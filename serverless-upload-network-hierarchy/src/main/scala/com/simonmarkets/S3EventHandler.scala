package com.simonmarkets

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.amazonaws.services.lambda.runtime.{Context, RequestHandler}
import com.amazonaws.services.s3.event.S3EventNotification
import com.amazonaws.services.s3.{AmazonS3, AmazonS3ClientBuilder}
import com.simonmarkets.Utils.{objectMapper, using}
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.utils.config.Resolvers._
import com.typesafe.config.ConfigFactory

import scala.collection.JavaConverters._
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext}
import scala.util.{Failure, Try}

trait S3EventHandlerModule extends RequestHandler[S3EventNotification, Unit] with TraceLogging {

  implicit lazy val system: ActorSystem = ActorSystem("S3EventHandlerModule")
  implicit lazy val mat: Materializer = Materializer(system)
  implicit lazy val executionContext: ExecutionContext = system.dispatcher

  private val timeout = 300.seconds

  def s3Client: AmazonS3

  def uploadService(implicit tid: TraceId): UploadNetworkHierarchy

  def parsingService: LocationParser

  override def handleRequest(input: S3EventNotification, context: Context): Unit = {
    implicit val traceId: TraceId = TraceId(context.getAwsRequestId)

    log.debug(s"""handle S3 Event records=${objectMapper.writeValueAsString(input)}""")
    require(input.getRecords.size() == 1, "S3 must send a single event per notification message")
    val record = input.getRecords.asScala.head
    val bucket = record.getS3.getBucket
    val key = record.getS3.getObject.getKey

    log.info(s"handle ${record.getEventName} for ${bucket.getName}:$key")
    using(s3Client.getObject(bucket.getName, key)) { s3Object =>
      for {
        locations <- parsingService.parse(s3Object.getObjectContent)
        result <- Try(Await.result(uploadService.upload(locations), timeout))
      } yield result
    } match {
      case Failure(e) => throw e
      case _ => log.info(s"Uploading new locations is completed")
    }
  }
}

class S3EventHandler extends S3EventHandlerModule {
  override def s3Client: AmazonS3 = AmazonS3ClientBuilder.standard.build

  override def uploadService(implicit tid: TraceId): UploadNetworkHierarchy = {
    log.info("Resolving secrets")
    val rawConfig = ConfigFactory.load().resolveSecrets()
    log.info("Getting configs")
    val config = UploadNetworkHierarchyConfig(rawConfig)
    log.info("Setting up client")
    val futureHttpClient = new FutureHttpClient(Http(), config.httpClientConfig)
    log.info("Initializing UploadNetworkHierarchyService")
    new UploadNetworkHierarchyService(path = config.apiPrefix, networkId = config.networkId, httpClient = futureHttpClient)
  }

  override def parsingService: LocationParser = new CsvLocationParser
}
