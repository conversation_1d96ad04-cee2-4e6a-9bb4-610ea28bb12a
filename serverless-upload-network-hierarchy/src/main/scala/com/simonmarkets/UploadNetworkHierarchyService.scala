package com.simonmarkets

import akka.stream.Materializer
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.NetworkView

import scala.annotation.nowarn
import scala.concurrent.{ExecutionContext, Future}

trait UploadNetworkHierarchy {
  def upload(locations: Seq[Location])(implicit tid: TraceId): Future[NetworkView]
}

@nowarn("cat=deprecation")
class UploadNetworkHierarchyService(
    private val path: String,
    private val networkId: String,
    private val httpClient: FutureHttpClient
)(implicit ec: ExecutionContext, mat: Materializer) extends UploadNetworkHierarchy with JsonCodecs with TraceLogging {

  def upload(locations: Seq[Location])(implicit tid: TraceId): Future[NetworkView] = {
    val url = s"$path/v2/networks/$networkId/locations"
    log.info(s"Calling PUT on: $url")
    httpClient.put[UpdateLocationRequest, NetworkView](url, UpdateLocationRequest(locations))
  }

}
