package com.simonmarkets

import java.io.InputStream

import scala.io.Source
import scala.util.Try

trait LocationParser {
  def parse(data: InputStream): Try[Seq[Location]]
}

class CsvLocationParser extends LocationParser {
  override def parse(data: InputStream): Try[Seq[Location]] = Try {
    Source.fromInputStream(data).getLines.map { row =>
      val Array(name, parent, children) = row.split(",", -1)
      Location(
        name = name,
        parent = Option(parent).filter(_.trim.nonEmpty),
        children = Option(children).filter(_.trim.nonEmpty)
          .map(_.split("\\|").toSeq)
          .getOrElse(Seq.empty))
    }.toSeq
  }
}
