default:
  tags:
    - $RUNNER_TAG

include:
  - project: 'simonmarkets/services/sdlc-gitlab-templatization'
    ref: $PIPELINE_TAG
    file:
      - 'templates/simon-services/stages/build_and_test.yml'
      - 'templates/simon-services/stages/post_deploy.yml'
      - '/templates/simon-services/templates/deploy-all-before-after-scripts.yml'
      - '/templates/simon-services/templates/common-before-after-scripts.yml'
      - '/templates/simon-services/templates/template-serverless-beforescript.yml'
      - '/templates/template-uploadkong-beforescript.yml'

stages:
  - build/test
  - deploy_alpha
  - postdeploy_alpha
  - deploy_uat
  - postdeploy_uat
  - deploy_qa
  - postdeploy_qa
  - predeploy_prod
  - deploy_prod
  - postdeploy_prod

.dind_docker_service: &docker_service
  - name: $ARTIFACTORY_URL/docker:dind
    alias: docker
    entrypoint: ["env", "-u", "DOCKER_HOST"]
    command: ["dockerd-entrypoint.sh"]

.deploy: &deploy
  image: $DOCKER_DEPLOY_IMG
  allow_failure: false
  dependencies: []

.deploy_docker: &deploy_docker
  <<: *deploy
  services:
    *docker_service

.deploy_eks_services:
  <<: *deploy_docker
  extends:
    - .template-deploy-all-before-after-scripts
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/release_manager_check.py
    - $CI_PROJECT_DIR/sdlc-tools/utilities/simon_argocd_deploy.sh ${ONDEMAND} ${app_bld_id}

.deploy_srvrless:
  <<: *deploy
  extends:
    - .template-serverless-beforescript
  script:
    - mv /src/* -t $CI_PROJECT_DIR
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/release_manager_check.py
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/build_managed_serverless.sh ${ONDEMAND} ${app_bld_id}

run_dbmigration_alpha:
  stage: deploy_alpha
  image: $SIMON_SERVERLESS_IMAGE
  extends:
    - .template-serverless-beforescript
  variables:
    ONDEMAND: 'db-migration'
  script:
    - mv /src/* -t $CI_PROJECT_DIR
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/build_managed_serverless.sh ${ONDEMAND} ${app_bld_id}
    - cd ${CI_PROJECT_DIR}/${ONDEMAND}
    - aws configure set aws_access_key_id ${AWS_ACCESS_KEY_ID} --profile ${CI_ENVIRONMENT_NAME}
    - aws configure set aws_secret_access_key ${AWS_SECRET_ACCESS_KEY} --profile ${CI_ENVIRONMENT_NAME}
    - aws configure set region us-east-1 --profile ${CI_ENVIRONMENT_NAME}
    - |
      if [ "$RUNNER_EXECUTOR_TYPE" = "eks" ]; then
        $SET_SERVERLESS_RC_PERMISSION
        aws configure set aws_session_token ${AWS_SESSION_TOKEN} --profile ${CI_ENVIRONMENT_NAME}
        $SET_AWS_FILE_PERMISSION
        npx serverless config credentials --key ${AWS_ACCESS_KEY_ID} --secret ${AWS_SECRET_ACCESS_KEY} -p aws --profile ${CI_ENVIRONMENT_NAME} -o
      fi
    - SLS_DEBUG=1 $CI_PROJECT_DIR/node_modules/.bin/serverless invoke --function api --data '{"action":"dbMigration"}' -s ${CI_ENVIRONMENT_NAME} > result.json
    - export db_migration_result="$(cat result.json | grep status | sed 's/[\", ]//g' | awk -F':' '{print $2}')"
    - cat result.json
    - $CI_PROJECT_DIR/node_modules/.bin/serverless logs --function api -s ${CI_ENVIRONMENT_NAME}
    - echo "Migration status is $db_migration_result"
    - test "$db_migration_result" == "Success" && exit 0 || exit 1
  environment:
    name: alpha
    deployment_tier: testing
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
  needs:
    - job: generate-release-notes
      artifacts: false
  dependencies: []

deploy_all_srvrless_alpha:
  stage: deploy_alpha
  image: $SIMON_SERVERLESS_IMAGE
  extends:
    - .deploy_srvrless
  variables:
    ONDEMAND: "All"
  environment:
    name: alpha
    deployment_tier: testing
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
  needs:
    - job: run_dbmigration_alpha
      artifacts: false
  dependencies: []

deploy_all_eks_alpha:
  stage: deploy_alpha
  image: $DOCKER_DEPLOY_IMG
  extends:
    - .deploy_eks_services
  variables:
    EKS_CLUSTER: $ALPHA_EKS_CLUSTER_NAME
    ONDEMAND: "All"
  environment:
    name: alpha
    deployment_tier: testing
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
  needs:
    - job: run_dbmigration_alpha
      artifacts: false
  dependencies: []

upload_openapi_alpha:
  stage: deploy_alpha
  image: $DOCKER_DEPLOY_IMG
  services:
    *docker_service
  extends:
    - .template-upload-kong-beforescript
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-networks $version ${CI_PIPELINE_ID}  https://localhost:443 alpha
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh serverless-sales-fee-rules $version ${CI_PIPELINE_ID}  https://localhost:443 alpha
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-tasks $version ${CI_PIPELINE_ID}  https://localhost:443 alpha
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-product-feature-sets $version ${CI_PIPELINE_ID}  https://localhost:443 alpha
  environment:
    name: alpha
    deployment_tier: testing
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
  needs:
    - job: deploy_all_srvrless_alpha
      artifacts: false
    - job: deploy_all_eks_alpha
      artifacts: false
  dependencies: []

alpha-rename-tag-verisonfile:
  stage: deploy_alpha
  image: $DOCKER_DEPLOY_IMG
  environment:
    name: alpha
    deployment_tier: testing
  resource_group: testing
  extends:
    - .template-common-before-after-scripts
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/rename_tag_versionfile.py
  needs:
    - job: upload_openapi_alpha
      artifacts: false
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
  dependencies: []

run_dbmigration_uat_us:
  stage: deploy_uat
  image: $SIMON_SERVERLESS_IMAGE
  extends:
    - .template-serverless-beforescript
  variables:
    ONDEMAND: 'db-migration'
  script:
    - mv /src/* -t $CI_PROJECT_DIR
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/build_managed_serverless.sh ${ONDEMAND} ${app_bld_id}
    - cd ${CI_PROJECT_DIR}/${ONDEMAND}
    - aws configure set aws_access_key_id ${AWS_ACCESS_KEY_ID} --profile ${CI_ENVIRONMENT_NAME}
    - aws configure set aws_secret_access_key ${AWS_SECRET_ACCESS_KEY} --profile ${CI_ENVIRONMENT_NAME}
    - aws configure set region us-east-1 --profile ${CI_ENVIRONMENT_NAME}
    - |
      if [ "$RUNNER_EXECUTOR_TYPE" = "eks" ]; then
        $SET_SERVERLESS_RC_PERMISSION
        aws configure set aws_session_token ${AWS_SESSION_TOKEN} --profile ${CI_ENVIRONMENT_NAME}
        $SET_AWS_FILE_PERMISSION
        npx serverless config credentials --key ${AWS_ACCESS_KEY_ID} --secret ${AWS_SECRET_ACCESS_KEY} -p aws --profile ${CI_ENVIRONMENT_NAME} -o
      fi
    - SLS_DEBUG=1 $CI_PROJECT_DIR/node_modules/.bin/serverless invoke --function api --data '{"action":"dbMigration"}' -s ${CI_ENVIRONMENT_NAME} > result.json
    - export db_migration_result="$(cat result.json | grep status | sed 's/[\", ]//g' | awk -F':' '{print $2}')"
    - cat result.json
    - $CI_PROJECT_DIR/node_modules/.bin/serverless logs --function api -s ${CI_ENVIRONMENT_NAME}
    - echo "Migration status is $db_migration_result"
    - test "$db_migration_result" == "Success" && exit 0 || exit 1
  environment:
    name: uat-us
    deployment_tier: testing
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
  needs:
    - job: generate-release-notes
      artifacts: false
  dependencies: []

deploy_all_srvrless_uat_us:
  stage: deploy_uat
  image: $SIMON_SERVERLESS_IMAGE
  extends:
    - .deploy_srvrless
  variables:
    ONDEMAND: "All"
  environment:
    name: uat-us
    deployment_tier: testing
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
  needs:
    - job: run_dbmigration_uat_us
      artifacts: false
  dependencies: []

deploy_all_eks_uat_us:
  stage: deploy_uat
  image: $DOCKER_DEPLOY_IMG
  extends:
    - .deploy_eks_services
  variables:
    EKS_CLUSTER: $UAT_US_EKS_CLUSTER_NAME
    ONDEMAND: "All"
  environment:
    name: uat-us
    deployment_tier: testing
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
#  needs:
#    - job: run_dbmigration_uat_us
#      artifacts: false
  dependencies: []

upload_openapi_uat_us:
  stage: deploy_uat
  image: $DOCKER_DEPLOY_IMG
  services:
    *docker_service
  extends:
    - .template-upload-kong-beforescript
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-networks $version ${CI_PIPELINE_ID}  https://localhost:443 uat_us
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh serverless-sales-fee-rules $version ${CI_PIPELINE_ID}  https://localhost:443 uat_us
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-tasks $version ${CI_PIPELINE_ID}  https://localhost:443 uat_us
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-product-feature-sets $version ${CI_PIPELINE_ID}  https://localhost:443 uat_us
  environment:
    name: uat-us
    deployment_tier: testing
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
  needs:
    - job: deploy_all_srvrless_uat_us
      artifacts: false
    - job: deploy_all_eks_uat_us
      artifacts: false
  dependencies: []

uat_us-rename-tag-verisonfile:
  stage: deploy_uat
  image: $DOCKER_DEPLOY_IMG
  environment:
    name: uat-us
    deployment_tier: testing
  resource_group: testing
  extends:
    - .template-common-before-after-scripts
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/rename_tag_versionfile.py
  needs:
    - job: upload_openapi_uat_us
      artifacts: false
  rules:
    - !reference [.stagestodeploy_rules_master_auto, rules]
  dependencies: []

qa-deploy-start:
  stage: deploy_qa
  image: $DOCKER_DEPLOY_IMG
  environment:
    name: qa
    deployment_tier: staging
  extends:
    - .template-common-before-after-scripts
  script:
    - pip install -r $CI_PROJECT_DIR/sdlc-tools/requirements.txt
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/send_teams_alert.py Initiated ${NONPROD_DEPLOY_WH}
  needs:
    - job: generate-release-notes
      artifacts: false
  rules:
    - !reference [.stagestodeploy_rules_release_manual, rules]
  dependencies: []

run_dbmigration_qa:
  stage: deploy_qa
  image: $SIMON_SERVERLESS_IMAGE
  extends:
    - .template-serverless-beforescript
  variables:
    ONDEMAND: 'db-migration'
  script:
    - mv /src/* -t $CI_PROJECT_DIR
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/build_managed_serverless.sh ${ONDEMAND} ${app_bld_id}
    - cd ${CI_PROJECT_DIR}/${ONDEMAND}
    - aws configure set aws_access_key_id ${AWS_ACCESS_KEY_ID} --profile ${CI_ENVIRONMENT_NAME}
    - aws configure set aws_secret_access_key ${AWS_SECRET_ACCESS_KEY} --profile ${CI_ENVIRONMENT_NAME}
    - aws configure set region us-east-1 --profile ${CI_ENVIRONMENT_NAME}
    - |
      if [ "$RUNNER_EXECUTOR_TYPE" = "eks" ]; then
        $SET_SERVERLESS_RC_PERMISSION
        aws configure set aws_session_token ${AWS_SESSION_TOKEN} --profile ${CI_ENVIRONMENT_NAME}
        $SET_AWS_FILE_PERMISSION
        npx serverless config credentials --key ${AWS_ACCESS_KEY_ID} --secret ${AWS_SECRET_ACCESS_KEY} -p aws --profile ${CI_ENVIRONMENT_NAME} -o
      fi
    - SLS_DEBUG=1 $CI_PROJECT_DIR/node_modules/.bin/serverless invoke --function api --data '{"action":"dbMigration"}' -s ${CI_ENVIRONMENT_NAME} > result.json
    - export db_migration_result="$(cat result.json | grep status | sed 's/[\", ]//g' | awk -F':' '{print $2}')"
    - cat result.json
    - $CI_PROJECT_DIR/node_modules/.bin/serverless logs --function api -s ${CI_ENVIRONMENT_NAME}
    - echo "Migration status is $db_migration_result"
    - test "$db_migration_result" == "Success" && exit 0 || exit 1
  environment:
    name: qa
    deployment_tier: staging
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  needs:
    - job: qa-deploy-start
      artifacts: false
  dependencies: []

deploy_all_srvrless_qa:
  stage: deploy_qa
  image: $SIMON_SERVERLESS_IMAGE
  extends:
    - .deploy_srvrless
  variables:
    ONDEMAND: "All"
  environment:
    name: qa
    deployment_tier: staging
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  needs:
    - job: run_dbmigration_qa
      artifacts: false
  dependencies: []

deploy_all_eks_qa:
  stage: deploy_qa
  extends:
    - .deploy_eks_services
  variables:
    EKS_CLUSTER: $QA_EKS_CLUSTER_NAME
    ONDEMAND: "All"
  environment:
    name: qa
    deployment_tier: staging
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  needs:
    - job: run_dbmigration_qa
      artifacts: false
  dependencies: []

upload_openapi_qa:
  stage: deploy_qa
  image: $DOCKER_DEPLOY_IMG
  services:
    *docker_service
  extends:
    - .template-upload-kong-beforescript
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-networks $version ${CI_PIPELINE_ID}  https://localhost:443 qa
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh serverless-sales-fee-rules $version ${CI_PIPELINE_ID}  https://localhost:443 qa
    #    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-sales-fee-rules-engine $version ${CI_PIPELINE_ID}  https://localhost:443 qa
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-tasks $version ${CI_PIPELINE_ID}  https://localhost:443 qa
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-product-feature-sets $version ${CI_PIPELINE_ID}  https://localhost:443 qa
  environment:
    name: qa
    deployment_tier: staging
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  needs:
    - job: deploy_all_srvrless_qa
      artifacts: false
    - job: deploy_all_eks_qa
      artifacts: false
  dependencies: []

qa-rename-tag-verisonfile:
  stage: deploy_qa
  image: $DOCKER_DEPLOY_IMG
  environment:
    name: qa
    deployment_tier: staging
  extends:
    - .template-common-before-after-scripts
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/rename_tag_versionfile.py
  needs:
    - job: upload_openapi_qa
      artifacts: false
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  dependencies: []

prod-change-management-generate-ticket:
  stage: predeploy_prod
  image: $DOCKER_DEPLOY_IMG
  extends:
    - .template-common-before-after-scripts
  environment:
    name: prod
    deployment_tier: production
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/release_manager_check.py
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/download_release_notes.py
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/change_management.py
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/check_ready_for_prod.py
  needs:
    - job: qa-jira-transition
      artifacts: false
  rules:
    - !reference [.stagestodeploy_rules_release_manual, rules]
  dependencies: []

prod-deploy-start:
  stage: deploy_prod
  image: $DOCKER_DEPLOY_IMG
  environment:
    name: prod
    deployment_tier: production
  extends:
    - .template-common-before-after-scripts
  script:
    - pip install -r $CI_PROJECT_DIR/sdlc-tools/requirements.txt
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/release_manager_check.py
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/send_teams_alert.py Initiated ${PROD_DEPLOY_WH}
  needs:
    - job: prod-change-management-generate-ticket
      artifacts: false
  rules:
    - !reference [.stagestodeploy_rules_release_manual, rules]
  dependencies: []

run_dbmigration_prod:
  stage: deploy_prod
  image: $SIMON_SERVERLESS_IMAGE
  extends:
    - .template-serverless-beforescript
  variables:
    ONDEMAND: 'db-migration'
  script:
    - mv /src/* -t $CI_PROJECT_DIR
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/release_manager_check.py
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/build_managed_serverless.sh ${ONDEMAND} ${app_bld_id}
    - cd ${CI_PROJECT_DIR}/${ONDEMAND}
    - aws configure set aws_access_key_id ${AWS_ACCESS_KEY_ID} --profile ${CI_ENVIRONMENT_NAME}
    - aws configure set aws_secret_access_key ${AWS_SECRET_ACCESS_KEY} --profile ${CI_ENVIRONMENT_NAME}
    - aws configure set region us-east-1 --profile ${CI_ENVIRONMENT_NAME}
    - |
      if [ "$RUNNER_EXECUTOR_TYPE" = "eks" ]; then
        $SET_SERVERLESS_RC_PERMISSION
        aws configure set aws_session_token ${AWS_SESSION_TOKEN} --profile ${CI_ENVIRONMENT_NAME}
        $SET_AWS_FILE_PERMISSION
        npx serverless config credentials --key ${AWS_ACCESS_KEY_ID} --secret ${AWS_SECRET_ACCESS_KEY} -p aws --profile ${CI_ENVIRONMENT_NAME} -o
      fi
    - SLS_DEBUG=1 $CI_PROJECT_DIR/node_modules/.bin/serverless invoke --function api --data '{"action":"dbMigration"}' -s ${CI_ENVIRONMENT_NAME} > result.json
    - export db_migration_result="$(cat result.json | grep status | sed 's/[\", ]//g' | awk -F':' '{print $2}')"
    - cat result.json
    - $CI_PROJECT_DIR/node_modules/.bin/serverless logs --function api -s ${CI_ENVIRONMENT_NAME}
    - echo "Migration status is $db_migration_result"
    - test "$db_migration_result" == "Success" && exit 0 || exit 1
  environment:
    name: prod
    deployment_tier: production
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  needs:
    - job: prod-deploy-start
      artifacts: false
  dependencies: []

deploy_all_srvrless_prod:
  stage: deploy_prod
  image: $SIMON_SERVERLESS_IMAGE
  extends:
    - .deploy_srvrless
  variables:
    ONDEMAND: "All"
  environment:
    name: prod
    deployment_tier: production
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  needs:
    - job: run_dbmigration_prod
      artifacts: false
  dependencies: []

deploy_all_eks_prod:
  stage: deploy_prod
  image: $DOCKER_DEPLOY_IMG
  extends:
    - .deploy_eks_services
  variables:
    EKS_CLUSTER: $PROD_EKS_CLUSTER_NAME
    ONDEMAND: "All"
  environment:
    name: prod
    deployment_tier: production
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  needs:
    - job: run_dbmigration_prod
      artifacts: false
  dependencies: []

upload_openapi_prod:
  stage: deploy_prod
  image: $DOCKER_DEPLOY_IMG
  services:
    *docker_service
  extends:
    - .template-upload-kong-beforescript
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-networks $version ${CI_PIPELINE_ID}  https://localhost:443 prod
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh serverless-sales-fee-rules $version ${CI_PIPELINE_ID}  https://localhost:443 prod
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-tasks $version ${CI_PIPELINE_ID}  https://localhost:443 prod
    - $CI_PROJECT_DIR/sdlc-tools/utilities/upload_openapispec_kong.sh service-product-feature-sets $version ${CI_PIPELINE_ID}  https://localhost:443 prod
  environment:
    name: prod
    deployment_tier: production
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  needs:
    - job: deploy_all_srvrless_prod
      artifacts: false
    - job: deploy_all_eks_prod
      artifacts: false
  dependencies: []

prod-rename-tag-verisonfile:
  stage: deploy_prod
  image: $DOCKER_DEPLOY_IMG
  environment:
    name: prod
    deployment_tier: production
  extends:
    - .template-common-before-after-scripts
  script:
    - $CI_PROJECT_DIR/sdlc-tools/utilities/pipeline_jobs/rename_tag_versionfile.py
  needs:
    - job: upload_openapi_prod
      artifacts: false
  rules:
    - !reference [.stagestodeploy_rules_release_auto, rules]
  dependencies: []
