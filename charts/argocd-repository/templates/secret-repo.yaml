##
# This file is creaded by HELM, please Don't edit it manually
##
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "repo.name" . }}
  namespace: argocd
  labels:
    argocd.argoproj.io/secret-type: repository
stringData:
  type: git
  url: {{ template "repo.url" . }}
  insecure: {{ default "true" .Values.skipCertVerify | quote }}
  {{if .Values.enabledProxy -}}
  {{ include "repo.proxy" . }}
  {{- end -}}
