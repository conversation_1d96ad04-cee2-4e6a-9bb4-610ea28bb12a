{{- define "repo.name" -}}
  {{ default "demo-app" .Values.name }}
{{- end -}}

{{- define "repo.proxy" -}}
{{- $env := .Values.env | lower -}}
{{- if eq $env "prod" -}}
proxy: {{ printf "HTTPS_PROXY=%s" "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com:3128" }}
{{- else if eq $env "qa" -}}
proxy: {{ printf "HTTPS_PROXY=%s" "internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com:3128" }}
{{- else -}}
proxy: {{ printf "HTTPS_PROXY=%s" "internal-alpha-squid-proxy-elb-1264059358.us-east-1.elb.amazonaws.com:3128" }}
{{- end -}}
{{- end -}}

{{- define "repo.url" -}}
{{- $env := .Values.env | lower -}}
{{- if eq $env "prod" -}}
{{ default "demo-app" .Values.name | printf "git@localhost:simonmarkets/services/%s.git" }} 
{{- else if eq $env "qa" -}}
{{ default "demo-app" .Values.name | printf "git@localhost:simonmarkets/services/%s.git" }} 
{{- else -}}
{{ default "demo-app" .Values.name | printf "**************:simonmarkets/services/%s.git" }} 
{{- end -}}
{{- end -}}

