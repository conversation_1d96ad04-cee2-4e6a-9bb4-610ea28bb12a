{{- define "project.name" -}}
    {{ default "default" .Values.project }}
{{- end -}}

{{- define "service.namespace" -}}
    {{ default "default" .Values.namespace }}
{{- end -}}

{{- define "repo.url" -}}
{{- $env := .Values.env | lower -}}
{{- if eq $env "prod" -}}
git@localhost:simonmarkets
{{- else if eq $env "qa" -}}
git@localhost:simonmarkets
{{- else -}}
**************:simonmarkets
{{- end -}}
{{- end -}}
