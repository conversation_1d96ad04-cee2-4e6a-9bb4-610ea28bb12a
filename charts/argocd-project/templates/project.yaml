##
# This file is creaded by HELM, please Don't edit it manually
##

apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: {{ template "project.name" . }}
  namespace: argocd
  # Finalizer that ensures that project will not be deleted until it is not referenced by any applications
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  description: {{ include "project.name" . | printf "%s project" }}
  # Allow manifests to deploy from any Git repos
  sourceRepos:
    - {{ printf "%s/services/%s.git" (include "repo.url" .) (include "project.name" .) }}
  # Only permit applications to deploy to the demo-app namespace in the same cluster
  destinations:
  - namespace: {{ template "service.namespace" . }}
    server: https://kubernetes.default.svc
  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
  namespaceResourceBlacklist:
  - group: ''
    kind: ResourceQuota
  - group: ''
    kind: LimitRange
  - group: ''
    kind: NetworkPolicy
  # Deny all namespaced-scoped resources from being created, except below
  namespaceResourceWhitelist:
  - group: 'apps'
    kind: Deployment
  - group: 'apps'
    kind: StatefulSet
  - group: ''
    kind: Service
  - group: ''
    kind: ServiceAccount
  - group: batch
    kind: CronJob
  roles: []
