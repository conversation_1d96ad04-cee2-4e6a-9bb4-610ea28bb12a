{{- define "app.name" -}}
  {{ default "default" .Values.service.name }}
{{- end -}}

{{- define "app.env" -}}
  {{ default "alpha" .Values.eks.env | lower }}
{{- end -}}

{{- define "cluster.color" -}}
  {{ default "green" .Values.eks.color }}
{{- end -}}

{{- define "app.serviceName" -}}
{{- printf "%s-service" (default "default" .Values.service.name) -}}
{{- end -}}

{{- define "app.serviceaccount" -}}
{{- .Values.serviceAccount.name | default (printf "%s-service-account" (default "default" .Values.service.name)) -}}
{{- end -}} 

{{- define "app.dns.domain" -}}
{{- $env := .Values.eks.env | lower -}}
{{- if eq $env "alpha" -}}
  {{ default "scratch.internal.simon.io" .Values.service.dns.domain }}
{{- else -}}
  {{ default "internal.simon.io" .Values.service.dns.domain }}
{{- end -}}
{{- end -}}

{{- define "app.dns.ndots" -}}
  {{ default 3 .Values.service.dns.ndots }}
{{- end -}}

{{- define "app.scheme" -}}
  {{ default "https" .Values.service.scheme }}
{{- end -}}

{{- define "app.port" -}}
  {{ default 443 .Values.service.port }}
{{- end -}}

{{- define "app.healthCheck.path" -}}
  {{ default "/simon/api/v1/healthcheck" .Values.service.healthCheck.path }}
{{- end -}}

{{- define "app.healthCheck.initialDelay" -}}
  {{ default 30 .Values.service.healthCheck.initialDelay }}
{{- end -}}

{{- define "app.healthCheck.interval" -}}
  {{ default 5 .Values.service.healthCheck.interval }}
{{- end -}}

{{- define "app.healthCheck.timeout" -}}
  {{ default 15 .Values.service.healthCheck.timeout }}
{{- end -}}

{{- define "app.healthCheck.successThreshold" -}}
  {{ default 3 .Values.service.healthCheck.successThreshold }}
{{- end -}}

{{- define "app.healthCheck.failureThreshold" -}}
  {{ default 3 .Values.service.healthCheck.failureThreshold }}
{{- end -}}

{{- define "app.resources" -}}
limits:
  memory: {{ default "" .Values.service.memory | quote }}
requests:
  memory: {{ default "" .Values.service.memory | quote }}
  cpu: {{ default "" .Values.service.cpu | quote }}
{{- end -}}

{{- define "deployment.replicas" -}}
  {{ default 1 .Values.deploy.replicas }} 
{{- end -}}

{{- define "deployment.image.pullPolicy" -}}
  {{ default "IfNotPresent" .Values.deploy.imagePullPolicy }} 
{{- end -}}

{{- define "lb.internal.enabled" -}}
   {{ default "true" .Values.loadbalancer.internalEnabled }}
{{- end -}}

{{- define "lb.crosszone.enabled" -}}
   {{ default "true" .Values.loadbalancer.crossZone }}
{{- end -}}

{{- define "lb.backend.protocol" -}}
  {{ default "https" .Values.service.scheme}}
{{- end -}}

{{- define "lb.ssl.cert" -}}
{{- $env := .Values.eks.env | lower -}}
{{- if eq $env "prod" -}}
arn:aws:acm:us-east-1:090020729433:certificate/a45fa8cf-aa89-4570-8a9e-4fe8ea74506b
{{- else if eq $env "qa" -}}
arn:aws:acm:us-east-1:586465018333:certificate/368a1de0-350d-4f7a-9e11-89a7afdd77f9
{{- else -}}
arn:aws:acm:us-east-1:350128884696:certificate/4c6deb56-2bb1-4d78-86e0-02d3104663d2
{{- end -}}
{{- end -}}

{{- define "lb.listener.scheme" -}}
  {{ default "https" .Values.loadbalancer.listenerScheme }}
{{- end -}}

{{- define "lb.listener.port" -}}
  {{ default 443 .Values.loadbalancer.listenerPort }}
{{- end -}}

{{- define "lb.connection.idleTimeout" -}}
  {{ default 300 .Values.loadbalancer.connectionIdleTimeout }}
{{- end -}}

{{- define "lb.draining.enabled" -}}
  {{ default "true" .Values.loadbalancer.drainingEnabled | quote }}
{{- end -}}

{{- define "lb.draining.timeout" -}}
  {{ default "60" .Values.loadbalancer.drainingTimeout | quote }}
{{- end -}}
  
{{- define "lb.healthcheck.healthy.threshold" -}}
  {{ default "3" .Values.loadbalancer.healthyThreshold | quote }}
{{- end -}}

{{- define "lb.healthcheck.unhealthy.threshold" -}}
  {{ default "5" .Values.loadbalancer.unhealthyThreshold | quote }}
{{- end -}}

{{- define "lb.healthcheck.interval" -}}
  {{ default "20" .Values.loadbalancer.healthCheckInterval | quote }}
{{- end -}}

{{- define "lb.healthcheck.timeout" -}}
  {{ default "5" .Values.loadbalancer.healthCheckTimeout | quote }}
{{- end -}}

{{- define "lb.security.groups" -}}
  {{ default "" .Values.loadbalancer.securityGroups | quote }}
{{- end -}}

{{- define "lb.log.enabled" -}}
  {{ default "false" .Values.loadbalancer.logEnabled }}
{{- end -}}

{{- define "apm.env.variables" -}}
- name: simon_apmagent
  value: {{ .Values.apmEnabled | quote }}
- name: DD_LOGS_INJECTION
  value: "true"
- name: DD_ENV
  valueFrom:
    fieldRef:
      fieldPath: metadata.labels['tags.datadoghq.com/env']
- name: DD_SERVICE
  valueFrom:
    fieldRef:
      fieldPath: metadata.labels['tags.datadoghq.com/service']
- name: DD_VERSION
  valueFrom:
    fieldRef:
      fieldPath: metadata.labels['tags.datadoghq.com/version']
- name: NEW_RELIC_APP_NAME
  value: {{ template "app.serviceName" . }}
- name: NEW_RELIC_ENVIRONMENT
  value: {{ template "app.env" . }}
{{- end -}}

{{- define "apm.metadata.labels" -}}
tags.datadoghq.com/env: {{ template "app.env" . }}
tags.datadoghq.com/service: {{ template "app.serviceName" . }}
tags.datadoghq.com/version: {{ .Values.eks.version }}
{{- end -}}

{{- define "apm.volumes" -}}
volumes:
  - hostPath:
      path: /var/run/datadog/
    name: apmsocketpath
{{- end -}}

{{- define "apm.volumeMounts" -}}
volumeMounts:
  - name: apmsocketpath
    mountPath: /var/run/datadog
{{- end -}}
