{{- if .Values.serviceAccount.created -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: {{ default "default" .Values.eks.namespace }}
  name: {{ default (printf "%s-service-account" .Values.service.name ) .Values.serviceAccount.name }}
  annotations:
  {{- if .Values.serviceAccount.iamARN }}
    eks.amazonaws.com/role-arn: {{ printf .Values.serviceAccount.iamARN }}
  {{- end -}}
{{- end -}}
