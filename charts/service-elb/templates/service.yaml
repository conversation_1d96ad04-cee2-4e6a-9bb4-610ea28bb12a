apiVersion: v1
kind: Service
metadata:
  namespace: {{ default "default" .Values.eks.namespace }}
  name: {{ template "app.serviceName" . }}
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-internal: {{ include "lb.internal.enabled" . | quote }}
    # Specifies whether cross-zone load balancing is enabled for the load balancer
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: {{ template "lb.backend.protocol" . }}
    service.beta.kubernetes.io/aws-load-balancer-access-log-enabled: {{ include "lb.log.enabled" . | quote }}
    # Fill in with the ARN of your certificate.
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: {{ default  ( include "lb.ssl.cert" .) .Values.loadbalancer.sslCert }}
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: {{ include "lb.listener.port" . | quote }}
    external-dns.alpha.kubernetes.io/hostname: {{ template "app.name" . }}.{{ template "cluster.color" . }}.{{ template "app.env" . }}.{{ template "app.dns.domain" . }}
    # Loadbalancer drainning settings hardcoded here
    service.beta.kubernetes.io/aws-load-balancer-connection-draining-enabled: {{ template "lb.draining.enabled" . }}
    service.beta.kubernetes.io/aws-load-balancer-connection-draining-timeout: {{ template "lb.draining.timeout" . }}
    service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: {{ include "lb.connection.idleTimeout" . | quote }}
    # A comma-separated list of key-value pairs which will be recorded as
    # additional tags in the ELB.
    service.beta.kubernetes.io/aws-load-balancer-additional-resource-tags: {{ printf "environment=%s,owner=devops,creator=argocd,cluster_color=%s" (include "app.env" .) (include "cluster.color" .) }}
    # The number of successive successful health checks required for a backend to
    # be considered healthy for traffic. Defaults to 2, must be between 2 and 10
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-healthy-threshold: {{ template "lb.healthcheck.healthy.threshold" . }}
    # The number of unsuccessful health checks required for a backend to be
    # considered unhealthy for traffic. Defaults to 6, must be between 2 and 10
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-unhealthy-threshold: {{ template "lb.healthcheck.unhealthy.threshold" .}}
    # The approximate interval, in seconds, between health checks of an
    # individual instance. Defaults to 10, must be between 5 and 300
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-interval: {{ template "lb.healthcheck.interval" . }}
    # The amount of time, in seconds, during which no response means a failed
    # health check. This value must be less than the service.beta.kubernetes.io/aws-load-balancer-healthcheck-interval
    # value. Defaults to 5, must be between 2 and 60
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-timeout: {{ template "lb.healthcheck.timeout" . }}
    # A list of existing security groups to be configured on the ELB created
    service.beta.kubernetes.io/aws-load-balancer-security-groups: {{ template "lb.security.groups" . }}
spec:
  selector:
    app: {{ template "app.name" . }}
  ports:
    - name: {{ template "lb.listener.scheme" . }}
      port: {{ template "lb.listener.port" . }} 
      targetPort: {{ template "app.port" . }}
  type: LoadBalancer
