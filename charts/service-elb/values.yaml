##
# SIMON eks service and cluster settings
##
eks:
  env: alpha
  color: blue
  namespace: ""
  version: ""

# AWS eks IRSA settings  
serviceAccount:
  created: true
  name: ""
  iamARN: ""

# SIMON service settings
service:
  name: ""
  scheme: https
  port: 443
  cpu: "0.5"
  memory: "5G" 
  healthCheck:
    path: ""
    initialDelay: 30
    interval: 5
    timeout: 15
    # Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. 
    # Must be 1 for liveness and startup Probes. Minimum value is 1.
    successThreshold: 1
    failureThreshold: 3
  dns:
    domain: ""
    ndots: 3

# AWS eks deployment settings
deploy:
  image: ""
  imagePullPolicy: ""
  replicas: 1

# Exposed service's loadbalancer settings (will be deprecated in future)
loadbalancer:
  sslCert: ""
  listenerPort: 443
  listenerScheme: https
  crossZone: "true"
  connectionIdleTimeout: 300
  drainingEnabled: "true"
  # Defaults to 60s
  drainingTimeout: "60"
  internalEnabled: "true"
  securityGroups: ""
  # Defaults to 3s, must be between 2 and 10
  healthyThreshold: 3
  # Defaults to 5s, must be between 2 and 10
  unhealthyThreshold: 5
  # Defaults to 20s, must be between 5 and 300
  healthCheckInterval: 20
  # Defaults to 5s, must be between 2 and 60
  healthCheckTimeout: 5
  logEnabled: "false"

# Enable APM (default: true)  
apmEnabled: true
