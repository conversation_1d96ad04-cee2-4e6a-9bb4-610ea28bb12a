##
# SIMON eks service and cluster settings
##
eks:
  env: alpha
  color: blue
  namespace: ""
  version: ""

# AWS eks IRSA settings  
serviceAccount:
  created: true
  name: ""
  iamARN: ""

# SIMON service settings
service:
  name: ""
  scheme: https
  port: 443
  cpu: "0.5"
  memory: "5G" 
  healthCheck:
    path: ""
    initialDelay: 30
    interval: 5
    timeout: 15
    # Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. 
    # Must be 1 for liveness and startup Probes. Minimum value is 1.
    successThreshold: 1
    failureThreshold: 3
  dns:
    domain: ""
    ndots: 3

# AWS eks deployment settings
deploy:
  image: ""
  imagePullPolicy: ""
  replicas: 1

# Exposed service's loadbalancer settings (will be deprecated in future)
loadbalancer:
  sslCert: ""
  scheme: ""
  targetType: ""
  listenerPort: 443
  listenerScheme: https
  securityGroups: ""
  # Defaults to 15s
  healthCheckInterval: 15
  # Defaults to 5s
  healthCheckTimeout: 5
  # Defaults to 3
  healthyThresholdCount: 3
  # Defaults to 5
  unhealthyThresholdCount: 5
  logEnabled: "true"

# Enable APM (default: true)  
apmEnabled: true
