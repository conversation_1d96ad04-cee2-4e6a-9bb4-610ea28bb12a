apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  namespace: {{ default "default" .Values.eks.namespace }}
  name: {{ include "app.serviceName" . | printf "%s-alb" }}
  labels:
    app: {{ template "app.name" . }}
  annotations:
    # trigger the alb-ingress-controller
    kubernetes.io/ingress.class: alb
    # set ALB parameters
    alb.ingress.kubernetes.io/scheme: {{ template "lb.scheme" .}}
    alb.ingress.kubernetes.io/target-type: {{ template "lb.target.type" . }}
    alb.ingress.kubernetes.io/security-groups: {{ template "lb.security.groups" .}}
    # Defaults to auto discovery
    # Uncomment below if you want to hardcode the subnets for alb
    # alb.ingress.kubernetes.io/subnets: subnet-61ba8d4e,subnet-48665215,subnet-4066c14f
    alb.ingress.kubernetes.io/backend-protocol : {{ template "lb.listener.scheme" . }}
    alb.ingress.kubernetes.io/certificate-arn: {{ default  ( include "lb.ssl.cert" .) .Values.loadbalancer.sslCert }}
    alb.ingress.kubernetes.io/listen-ports: {{ include "lb.listener.port" . | printf "'[{\"HTTPS\": %s}]'" }}
    alb.ingress.kubernetes.io/healthcheck-protocol: {{ template "lb.listener.scheme" . }}
    alb.ingress.kubernetes.io/healthcheck-path: {{ template "app.healthCheck.path" . }}
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: {{ template "lb.healthcheck.interval" . }}
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: {{ template "lb.healthcheck.timeout" . }}
    alb.ingress.kubernetes.io/healthy-threshold-count: {{ template "lb.healthy.threshold.count" .}}
    alb.ingress.kubernetes.io/unhealthy-threshold-count: {{ template "lb.unhealthy.threshold.count" .}}
    # allow 404s on the health check
    # alb.ingress.kubernetes.io/success-codes: 200,404
    alb.ingress.kubernetes.io/load-balancer-attributes: {{ printf "access_logs.s3.enabled=%s,access_logs.s3.bucket=simon-alb-monolith-log-temp-deleteme" (include "lb.log.enabled" .) }}
    # Target group attributes
    alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=1200
    alb.ingress.kubernetes.io/tags: {{ printf "environment=%s,owner=devops,creator=argocd,cluster_color=%s" (include "app.env" .) (include "cluster.color" .) }}
    external-dns.alpha.kubernetes.io/hostname: {{ template "app.name" . }}.{{ template "cluster.color" . }}.{{ template "app.env" . }}.{{ template "app.dns.domain" . }}
spec:
  # forward all requests to nginx-ingress-controller
  rules:
  - http:
      paths:
      - path: /*
        backend:
          serviceName: {{ template "app.serviceName" . }}
          servicePort: {{ template "lb.listener.port" . }}
