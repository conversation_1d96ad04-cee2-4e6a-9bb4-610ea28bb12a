apiVersion: v1
kind: Service
metadata:
  namespace: {{ default "default" .Values.eks.namespace }}
  name: {{ template "app.serviceName" . }}
spec:
  selector:
    app: {{ template "app.name" . }}
  ports:
  - name: {{ template "lb.listener.scheme" . }}
    port: {{ template "lb.listener.port" . }} 
    targetPort: {{ template "app.port" . }}
  # Hazelcasst port setings are hardcoded here  
  - name: hazelcast
    port: 5701
    targetPort: 5701
  type: NodePort
