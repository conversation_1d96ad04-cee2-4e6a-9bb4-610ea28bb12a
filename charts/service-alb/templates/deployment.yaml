apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ default "default" .Values.eks.namespace }}
  name: {{ include "app.name" . | printf "%s-argocd" }}
  labels:
    app: {{ template "app.name" .}}
    {{- if .Values.apmEnabled -}}
    {{ include "apm.metadata.labels" . | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ template "deployment.replicas" . }}
  selector:
    matchLabels:
      app: {{ template "app.name" . }} 
  template:
    metadata:
      labels:
        app: {{ template "app.name" .}}
        {{- if .Values.apmEnabled -}}
        {{ include "apm.metadata.labels" . | nindent 8 }}
        {{- end }}
    spec:
      {{ if .Values.serviceAccount.created -}}
      serviceAccountName: {{ template "app.serviceaccount" . }}
      {{ end -}}
      {{- if .Values.apmEnabled -}}
      {{ include "apm.volumes" . | nindent 6 }}
      {{- end }}
      containers:
        - name: {{ template "app.name" . }}
          image: {{ .Values.deploy.image | quote }}
          resources: {{ include "app.resources" . | nindent 12 }}
          {{- if .Values.apmEnabled -}}
          {{ include "apm.volumeMounts" . | nindent 10 }}
          {{- end }}
          envFrom:
            - configMapRef:
                name: env-configmap
          env:
            {{- if .Values.apmEnabled -}}
            {{ include "apm.env.variables" . | nindent 12 }}
            {{- end }}
          imagePullPolicy: {{ template "deployment.image.pullPolicy" . }}
          ports:
            - containerPort: {{ template "app.port" . }}
          livenessProbe:
            httpGet:
               path: {{ template "app.healthCheck.path" . }}
               port: {{ template "app.port" . }}
               scheme: {{ include "app.scheme" . | upper }}
            initialDelaySeconds: {{ template "app.healthCheck.initialDelay" . }}
            periodSeconds: {{ template "app.healthCheck.interval" . }}
            timeoutSeconds: {{ template "app.healthCheck.timeout" . }} 
            successThreshold: {{ template "app.healthCheck.successThreshold" . }}
            failureThreshold: {{ template "app.healthCheck.failureThreshold" . }}
      dnsConfig:
        options:
        - name: ndots
          value: {{ include "app.dns.ndots" . | quote }}
