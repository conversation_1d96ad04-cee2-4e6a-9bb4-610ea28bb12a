{{- define "app.name" -}}
  {{ default "default" .Values.service.name }}
{{- end -}}

{{- define "app.env" -}}
  {{ default "alpha" .Values.eks.env | lower }}
{{- end -}}

{{- define "cluster.color" -}}
  {{ default "green" .Values.eks.color }}
{{- end -}}

{{- define "app.serviceName" -}}
{{- printf "%s-service" (default "default" .Values.service.name) -}}
{{- end -}}

{{- define "app.serviceaccount" -}}
{{- .Values.serviceAccount.name | default (printf "%s-service-account" (default "default" .Values.service.name)) -}}
{{- end -}} 

{{- define "app.dns.domain" -}}
{{- $env := .Values.eks.env | lower -}}
{{- if eq $env "alpha" -}}
  {{ default "scratch.internal.simon.io" .Values.service.dns.domain }}
{{- else -}}
  {{ default "internal.simon.io" .Values.service.dns.domain }}
{{- end -}}
{{- end -}}

{{- define "app.dns.ndots" -}}
  {{ default 3 .Values.service.dns.ndots }}
{{- end -}}

{{- define "app.scheme" -}}
  {{ default "https" .Values.service.scheme }}
{{- end -}}

{{- define "app.port" -}}
  {{ default 443 .Values.service.port }}
{{- end -}}

{{- define "app.healthCheck.path" -}}
  {{ default "/simon/api/v1/healthcheck" .Values.service.healthCheck.path }}
{{- end -}}

{{- define "app.healthCheck.initialDelay" -}}
  {{ default 30 .Values.service.healthCheck.initialDelay }}
{{- end -}}

{{- define "app.healthCheck.interval" -}}
  {{ default 5 .Values.service.healthCheck.interval }}
{{- end -}}

{{- define "app.healthCheck.timeout" -}}
  {{ default 15 .Values.service.healthCheck.timeout }}
{{- end -}}

{{- define "app.healthCheck.successThreshold" -}}
  {{ default 3 .Values.service.healthCheck.successThreshold }}
{{- end -}}

{{- define "app.healthCheck.failureThreshold" -}}
  {{ default 3 .Values.service.healthCheck.failureThreshold }}
{{- end -}}

{{- define "app.resources" -}}
limits:
  memory: {{ default "" .Values.service.memory | quote }}
requests:
  memory: {{ default "" .Values.service.memory | quote }}
  cpu: {{ default "" .Values.service.cpu | quote }}
{{- end -}}

{{- define "deployment.replicas" -}}
  {{ default 1 .Values.deploy.replicas }} 
{{- end -}}

{{- define "deployment.image.pullPolicy" -}}
  {{ default "IfNotPresent" .Values.deploy.imagePullPolicy }} 
{{- end -}}

{{- define "lb.scheme" -}}
   {{ default "internal" .Values.loadbalancer.scheme }}
{{- end -}}

{{- define "lb.target.type" -}}
   {{ default "instance" .Values.loadbalancer.targetType }}
{{- end -}}

{{- define "lb.security.groups" -}}
{{- if .Values.loadbalancer.securityGroups -}}
{{- .Values.loadbalancer.securityGroups | quote -}}
{{- else -}}
{{- $env := .Values.eks.env | lower -}}
{{- if eq $env "prod" -}}
"sg-0c4aa0e1e0dd2b5cd"
{{- else if eq $env "qa" -}}
"sg-038e5489e0ec0e332"
{{- else -}}
"sg-08eda440648ecbd9c"
{{- end -}}
{{- end -}}
{{- end -}}

{{- define "lb.backend.protocol" -}}
  {{ default "https" .Values.service.scheme}}
{{- end -}}

{{- define "lb.ssl.cert" -}}
{{- $env := .Values.eks.env | lower -}}
{{- if eq $env "prod" -}}
arn:aws:acm:us-east-1:090020729433:certificate/a45fa8cf-aa89-4570-8a9e-4fe8ea74506b
{{- else if eq $env "qa" -}}
arn:aws:acm:us-east-1:586465018333:certificate/368a1de0-350d-4f7a-9e11-89a7afdd77f9
{{- else -}}
arn:aws:acm:us-east-1:350128884696:certificate/4c6deb56-2bb1-4d78-86e0-02d3104663d2
{{- end -}}
{{- end -}}

{{- define "lb.listener.scheme" -}}
  {{ default "https" .Values.loadbalancer.listenerScheme }}
{{- end -}}

{{- define "lb.listener.port" -}}
  {{ default 443 .Values.loadbalancer.listenerPort }}
{{- end -}}

{{- define "lb.healthcheck.interval" -}}
  {{ default "15" .Values.loadbalancer.healthCheckInterval | quote }}
{{- end -}}

{{- define "lb.healthcheck.timeout" -}}
  {{ default "5" .Values.loadbalancer.healthCheckTimeout | quote }}
{{- end -}}

{{- define "lb.healthy.threshold.count" -}}
  {{ default "3" .Values.loadbalancer.healthThresholdCount | quote }}
{{- end -}}

{{- define "lb.unhealthy.threshold.count" -}}
  {{ default "5" .Values.loadbalancer.unhealthyThresholdCount | quote }}
{{- end -}}

{{- define "lb.log.enabled" -}}
  {{ default "true" .Values.loadbalancer.logEnabled }}
{{- end -}}

{{- define "apm.env.variables" -}}
- name: simon_apmagent
  value: {{ .Values.apmEnabled | quote }}
- name: DD_LOGS_INJECTION
  value: "true"
- name: DD_ENV
  valueFrom:
    fieldRef:
      fieldPath: metadata.labels['tags.datadoghq.com/env']
- name: DD_SERVICE
  valueFrom:
    fieldRef:
      fieldPath: metadata.labels['tags.datadoghq.com/service']
- name: DD_VERSION
  valueFrom:
    fieldRef:
      fieldPath: metadata.labels['tags.datadoghq.com/version']
- name: NEW_RELIC_APP_NAME
  value: {{ template "app.serviceName" . }}
- name: NEW_RELIC_ENVIRONMENT
  value: {{ template "app.env" . }}
{{- end -}}

{{- define "apm.metadata.labels" -}}
tags.datadoghq.com/env: {{ template "app.env" . }}
tags.datadoghq.com/service: {{ template "app.serviceName" . }}
tags.datadoghq.com/version: {{ .Values.eks.version }}
{{- end -}}

{{- define "apm.volumes" -}}
volumes:
  - hostPath:
      path: /var/run/datadog/
    name: apmsocketpath
{{- end -}}

{{- define "apm.volumeMounts" -}}
volumeMounts:
  - name: apmsocketpath
    mountPath: /var/run/datadog
{{- end -}}
