##
# This file is creaded by HELM, please Don't edit it manually
##
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ template "app.name" . }}
  namespace: argocd
spec:
  project: {{ template "project.name" .}}
  source:
    repoURL: {{ printf "%s/services/%s.git" (include "repo.url" .) (include "project.name" .) }}
    targetRevision: {{ template "git.revision" .}}
    {{- $type := include "service.type" . -}}
    {{- if eq $type "alb" }}
    path: .argocd/simon-argocd/charts/service-alb
    {{- else if eq $type "cron" }}
    path: .argocd/simon-argocd/charts/service-cron
    {{- else }}
    path: .argocd/simon-argocd/charts/service-elb
    {{- end }}
    helm:
      releaseName: {{ template "app.name" . }}
      valueFiles:
        {{- if eq $type "cron" }}
        - ./../../../../eks-config/cron-values.yaml
        {{- else }}
        - ./../../../../eks-config/values.yaml
        {{- end -}}
        {{- if .Values.service_values_file }}
        - {{ printf "./../../../../%s" .Values.service_values_file }}
        {{- end -}}
        {{- if .Values.env_values_file }}
        - {{ printf "./../../../../%s" .Values.env_values_file }}
        {{- end }}
  destination:
    server: https://kubernetes.default.svc
    namespace: {{ template "service.namespace" . }}
