{{- define "project.name" -}}
    {{ default "default" .Values.project }}
{{- end -}}

{{- define "app.name" -}}
    {{ default "default" (default .Values.project .Values.app_name) }}
{{- end -}}

{{- define "service.namespace" -}}
    {{ default "default" .Values.namespace }}
{{- end -}}

{{- define "service.type" -}}
    {{ default "elb" .Values.service_type | lower}}
{{- end -}}

{{- define "repo.url" -}}
{{- $env := .Values.env | lower -}}
{{- if eq $env "prod" -}}
git@localhost:simonmarkets
{{- else if eq $env "qa" -}}
git@localhost:simonmarkets
{{- else -}}
**************:simonmarkets
{{- end -}}
{{- end -}}

{{- define "git.revision" -}}
    {{ default "master" .Values.git_revision }}
{{- end -}}
