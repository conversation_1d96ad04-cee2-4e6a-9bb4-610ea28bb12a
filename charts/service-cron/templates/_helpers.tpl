{{- define "job.env" -}}
  {{ default "alpha" .Values.eks.env | lower }}
{{- end -}}

{{- define "cluster.color" -}}
  {{ default "green" .Values.eks.color }}
{{- end -}}

{{- define "job.name" -}}
{{- printf "%s-job" (default "default" .Values.service.name) -}}
{{- end -}}

{{- define "job.serviceaccount" -}}
{{- .Values.serviceAccount.name | default (printf "%s-service-account" (default "default" .Values.service.name)) -}}
{{- end -}} 

{{- define "app.dns.domain" -}}
{{- $env := .Values.eks.env | lower -}}
{{- if eq $env "alpha" -}}
  {{ default "scratch.internal.simon.io" .Values.service.dns.domain }}
{{- else -}}
  {{ default "internal.simon.io" .Values.service.dns.domain }}
{{- end -}}
{{- end -}}

{{- define "app.dns.ndots" -}}
  {{ default 3 .Values.service.dns.ndots }}
{{- end -}}

{{- define "job.schedule" -}}
  {{ default "0 0 ? * MON-FRI" .Values.deploy.schedule }} 
{{- end -}}

{{- define "job.suspend" -}}
  {{ default "false" .Values.deploy.suspend }} 
{{- end -}}

{{- define "job.resources" -}}
limits:
  memory: {{ default "" .Values.service.memory | quote }}
requests:
  memory: {{ default "" .Values.service.memory | quote }}
  cpu: {{ default "" .Values.service.cpu | quote }}
{{- end -}}

{{- define "job.restart.policy" -}}
  {{ default "Never" .Values.deploy.restart }} 
{{- end -}}

{{- define "job.concurrency.policy" -}}
  {{ default "Forbid" .Values.deploy.concurrency }} 
{{- end -}}

{{- define "job.active.deadline" -}}
  {{ default 3600 .Values.deploy.activeDeadline }} 
{{- end -}}

{{- define "apm.env.variables" -}}
- name: simon_apmagent
  value: {{ .Values.apmEnabled | quote }}
- name: DD_LOGS_INJECTION
  value: "true"
- name: DD_ENV
  valueFrom:
    fieldRef:
      fieldPath: metadata.labels['tags.datadoghq.com/env']
- name: DD_SERVICE
  valueFrom:
    fieldRef:
      fieldPath: metadata.labels['tags.datadoghq.com/service']
- name: DD_VERSION
  valueFrom:
    fieldRef:
      fieldPath: metadata.labels['tags.datadoghq.com/version']
- name: NEW_RELIC_APP_NAME
  value: {{ template "job.name" . }}
- name: NEW_RELIC_ENVIRONMENT
  value: {{ template "job.env" . }}
{{- end -}}

{{- define "apm.metadata.labels" -}}
tags.datadoghq.com/env: {{ template "job.env" . }}
tags.datadoghq.com/service: {{ template "job.name" . }}
tags.datadoghq.com/version: {{ .Values.eks.version }}
{{- end -}}

{{- define "apm.volumes" -}}
volumes:
  - hostPath:
      path: /var/run/datadog/
    name: apmsocketpath
{{- end -}}

{{- define "apm.volumeMounts" -}}
volumeMounts:
  - name: apmsocketpath
    mountPath: /var/run/datadog
{{- end -}}
