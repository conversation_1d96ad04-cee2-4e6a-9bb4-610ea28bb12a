apiVersion: batch/v1
kind: CronJob
metadata:
  namespace: {{ default "default" .Values.eks.namespace }}
  name: {{ template "job.name" . }}
  labels:
    app: {{ template "job.name" . }}
    {{- if .Values.apmEnabled -}}
    {{ include "apm.metadata.labels" . | nindent 4 }}
    {{- end }}
spec:
  schedule: {{ template "job.schedule" . }}
  suspend: {{ template "job.suspend" . }}
  concurrencyPolicy: {{ template "job.concurrency.policy" . }}
  jobTemplate:
    spec:
      activeDeadlineSeconds: {{ template "job.active.deadline" . }}
      template:
        metadata:
          labels:
            app: {{ template "job.name" . }}
            {{- if .Values.apmEnabled -}}
            {{ include "apm.metadata.labels" . | nindent 12 }}
            {{- end }}
        spec:
          {{ if .Values.serviceAccount.created -}}
        	serviceAccountName: {{ template "job.serviceaccount" . }}
          {{ end -}}
          {{- if .Values.apmEnabled -}}
          {{ include "apm.volumes" . | nindent 10 }}
          {{- end }}
          containers:
          - name: {{ template "job.name" . }}
            image: {{ .Values.deploy.image | quote }}
            resources: {{ include "job.resources" . | nindent 14 }}
            {{- if .Values.apmEnabled -}}
            {{ include "apm.volumeMounts" . | nindent 12 }}
            {{- end }}
            envFrom:
              - configMapRef:
                  name: env-configmap    
            env:
              - name: class_file
                value: {{ .Values.java_class }}
              - name: function_name
                value: {{ .Values.python_main_function }}
							{{- if .Values.apmEnabled -}}
							  {{ include "apm.env.variables" . | nindent 14 }}
							{{- end }}
          restartPolicy: {{ template "job.restart.policy" . }}
