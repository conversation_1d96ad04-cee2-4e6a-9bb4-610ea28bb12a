##
# SIMON eks cronjob and cluster settings
##
eks:
  env: alpha
  color: blue
  namespace: ""
  version: ""

# AWS eks IRSA settings  
serviceAccount:
  created: true
  name: ""
  iamARN: ""

# SIMON service settings
service:
  name: ""
  cpu: "0.5"
  memory: "5G" 
  dns:
    domain: ""
    ndots: 3

# AWS eks deployment settings
deploy:
  image: ""
  # Default schedule "0 0 ? * MON-FRI"
  schedule: ""
  # Default false
  suspend: ""
  # Default Never
  restart: ""
  # Default Forbid
  concurrency: ""
  # Default 3600 secs
  activeDeadline: 3600
  # Java main class
  java_class: ""

# Enable APM (default: true)  
apmEnabled: true
