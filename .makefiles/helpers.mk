##
# Helpers and data for output colors and functions
##

# usage example (announcing the name of the current target on entry):
#
#    my-target:
#         $(call _announce_target, $@)
#
# class bcolors:
#     HEADER = '\033[95m'
#     OKBLUE = '\033[94m'
#     OKGREEN = '\033[92m'
#     WARNING = '\033[93m'
#     FAIL = '\033[91m'
#     ENDC = '\033[0m'
#     BOLD = '\033[1m'
#     UNDERLINE = '\033[4m'
# To use code like this, you can do something like
# print bcolors.WARNING + "\033[93mWarning:\033[0m" + bcolors.ENDC

# Define colors
NO_COLOR:=\033[0m
COLOR_GREEN:=\033[92m
COLOR_RED:=\033[91m
COLOR_CYAN:=\e[36m
COLOR_MAGENTA:=\e[35m
COLOR_YELLOW:=\033[93m

OK_COLOR=${COLOR_GREEN}
ERROR_COLOR=${COLOR_RED}
WARN_COLOR=${COLOR_YELLOW}

# Define colored strings
OK_STRING=$(COLOR_GREEN)[OK]$(NO_COLOR)
ERROR_STRING=$(ERROR_COLOR)[ERROR]$(NO_COLOR)
WARN_STRING=$(WARN_COLOR)[WARNING]$(NO_COLOR)

# Colors
ESC_SEQ="\x1b["
COL_RESET=$ESC_SEQ"39;49;00m"
COL_RED=$ESC_SEQ"31;01m"
COL_GREEN=$ESC_SEQ"32;01m"
COL_YELLOW=$ESC_SEQ"33;01m"
COL_BLUE=$ESC_SEQ"34;01m"
COL_MAGENTA=$ESC_SEQ"35;01m"
COL_CYAN=$ESC_SEQ"36;01m"

# Define glyphs
GLYPH_DEL=${COLOR_RED}⌦  ${NO_COLOR}

# Define functions and targets

define _announce_target
        @export tmp="$${2:-}"; printf "$(COLOR_YELLOW)(`hostname`)$(NO_COLOR) [target=$(COLOR_GREEN)$@$(NO_COLOR)]:$(COLOR_CYAN)$${tmp}$(NO_COLOR)\n" 1>&2
endef

define _stage
        @printf "$(COLOR_YELLOW)(`hostname`)$(NO_COLOR) $(COLOR_GREEN)[stage]:$(NO_COLOR) ${1}\n " 1>&2;
endef

# really, don't mess with this.  it looks like there's two
# newlines here, but there's actually magic.  see also the docs here
# https://www.cmcrossroads.com/article/gnu-make-escaping-walk-wild-side
define newline


endef

define _fail
        @INDENTION="  "; \
        printf "$(COLOR_RED)(`hostname`) [FAIL]:$(NO_COLOR)\n$${INDENTION}${1}\n" 1>&2;
        exit 1
endef

fail-and-exit:
	$(call _fail, $${MSG})

tmp-clean:
	$(call _announce_target, $@)
	rm -vf .tmp.*

