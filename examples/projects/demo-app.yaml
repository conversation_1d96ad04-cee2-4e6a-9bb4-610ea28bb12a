apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: demo-app
  namespace: argocd
  # Finalizer that ensures that project will not be deleted until it is not referenced by any applications
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  description: Demo-app project
  # Allow manifests to deploy from any Git repos
  sourceRepos:
    - "**************:simonmarkets/services/demo-app.git"
  # Only permit applications to deploy to the demo-app namespace in the same cluster
  destinations:
  - namespace: demo-app
    server: https://kubernetes.default.svc
  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  # Allow all namespaced-scoped resources to be created, except for ResourceQuota, LimitRange, NetworkPolicy
  namespaceResourceBlacklist:
  - group: ''
    kind: ResourceQuota
  - group: ''
    kind: LimitRange
  - group: ''
    kind: NetworkPolicy
  # Deny all namespaced-scoped resources from being created, except below
  namespaceResourceWhitelist:
  - group: 'apps'
    kind: Deployment
  - group: 'apps'
    kind: StatefulSet
  - group: ''
    kind: Service
  roles:
  # A role which provides read-only access to all applications in the project
  - name: read-only
    description: Read-only privileges to Preferences project
    policies:
    - p, proj:demo-app:read-only, applications, get, demo-app/*, allow
    groups:
    - demo-app-oidc-group
  # A role which provides sync privileges to a CI system
  - name: ci-role
    description: Sync privileges for demo-app
    policies:
    - p, proj:demo-app:ci-role, applications, sync, demo-app/*, allow
    # NOTE: JWT tokens can only be generated by the API server and the token is not persisted
    # anywhere by Argo CD. It can be prematurely revoked by removing the entry from this list.
    jwtTokens:
    - iat: 1535390316
