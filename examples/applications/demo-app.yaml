apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: demo-app
  namespace: argocd
spec:
  project: demo-app
  source:
    repoURL: **************:simonmarkets/services/demo-app.git
    targetRevision: master
    path: .argocd/simon-argocd/charts/service-elb
    helm:
      releaseName: demo-app
      valueFiles:
        - ./../../../../eks-config/values.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: demo-app
