package com.simonmarkets.networks

import akka.NotUsed
import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.http.scaladsl.model.Uri.Query
import akka.http.scaladsl.model.{<PERSON>tt<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>}
import akka.stream.Materializer
import akka.stream.scaladsl.Source
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.{FutureHttpClient, HttpClientConfig}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.api.Page
import com.simonmarkets.resteasy.authn.config.SourceConfig
import com.typesafe.config.Config
import pureconfig.generic.FieldCoproductHint
import simon.Id.NetworkId
import pureconfig.generic.auto._

import java.net.URLEncoder
import java.nio.charset.StandardCharsets

import scala.concurrent.{ExecutionContext, Future}

case class NetworkClientConfig(
    httpClient: HttpClientConfig,
    apiPrefix: String
)

object NetworkClientConfig {
  implicit val sourceConfigHint: FieldCoproductHint[SourceConfig] = new FieldCoproductHint[SourceConfig](key = "type")

  def apply(config: Config): NetworkClientConfig = pureconfig.loadConfigOrThrow[NetworkClientConfig](config)
}

trait NetworksClient {

  /**
   * To be used as warmup to establish connection to client
   *
   * @param traceId trace
   * @return Unit
   */
  def ping(implicit traceId: TraceId): Future[Unit]

  def getIdAndName(ids: Set[NetworkId])(implicit traceId: TraceId): Future[Map[String, String]]

  def getById(id: NetworkId)(implicit traceId: TraceId): Future[NetworkDTO]

  def getByExternalId(externalId: String, subject: String)(implicit traceId: TraceId): Future[NetworkDTO]

  def expire(id: NetworkId)(implicit traceId: TraceId): Future[Map[String, String]]

  def getPage(limit: Option[String], next: Option[String], ids: Option[Seq[String]], subject: Option[String])
    (implicit traceId: TraceId): Future[Page[NetworkDTO]]

  def streamAll(pageSize: Int = 30, ids: Option[Seq[NetworkId]] = None)(implicit traceId: TraceId): Source[Seq[NetworkDTO], NotUsed]

}

class HttpNetworksClient(client: FutureHttpClient, path: String)
  (implicit ec: ExecutionContext, mat: Materializer) extends NetworksClient with JsonCodecs with TraceLogging {

  private implicit val tag: FutureHttpClient.Tag = FutureHttpClient.Tag("networks")

  private val urlEncoder =
    (str: String) => URLEncoder.encode(str, StandardCharsets.UTF_8.toString).replaceAll("\\+", "%20")

  private def encodeNetworkId(id: NetworkId): String = urlEncoder(NetworkId.unwrap(id))

  override def getIdAndName(ids: Set[NetworkId])(implicit traceId: TraceId): Future[Map[String, String]] = {
    val query = ids.map(encodeNetworkId).mkString("&id=")
    val url = s"$path/v2/networks/names?id=$query"
    client.get[Map[String, String]](url)
  }

  override def getById(id: NetworkId)(implicit traceId: TraceId): Future[NetworkDTO] = {
    val url = s"$path/v2/networks/${encodeNetworkId(id)}"
    client.get[NetworkDTO](url)
  }

  override def getByExternalId(externalId: String, subject: String)
    (implicit traceId: TraceId): Future[NetworkDTO] =
    getByExternalId(externalId, subject, Nil)

  override def expire(id: NetworkId)(implicit traceId: TraceId): Future[Map[String, String]] = {
    val url = s"$path/v1/networks/${encodeNetworkId(id)}/expire"
    client.post[Map[String, String], Map[String, String]](url, Map.empty)
  }

  override def ping(implicit traceId: TraceId): Future[Unit] =
    client.get[String](s"$path/v2/networks/healthcheck").map(_ => ())

  //to be used if http specific functionality is needed
  def getByExternalId(externalId: String, subject: String, requestHeaders: List[HttpHeader] = List.empty)
    (implicit traceId: TraceId): Future[NetworkDTO] = {
    val url = s"$path/v2/networks/${urlEncoder(externalId)}?external=true&subject=${urlEncoder(subject)}"
    client.get[NetworkDTO](url, requestHeaders)
  }

  override def getPage(limit: Option[String], next: Option[String], idsOpt: Option[Seq[String]],
      subject: Option[String])
    (implicit traceId: TraceId): Future[Page[NetworkDTO]] = {

    val params = Seq(
      limit.map(p => s"limit=$p"),
      next.map(p => s"next=$p"),
      idsOpt.map(p => s"idsOpt=${p.map(urlEncoder).mkString(",")}"),
      subject.map(p => s"subject=$p"),
    ).flatten.mkString("&")

    val queryParams = if (params.nonEmpty) {
      s"?$params"
    }
    else
      ""
    val url = s"$path/v2/networks$queryParams"
    client.get[Page[NetworkDTO]](url)
  }

  override def streamAll(
      pageSize: Int,
      ids: Option[Seq[NetworkId]],
  )(implicit traceId: TraceId): Source[Seq[NetworkDTO], NotUsed] = {
    val baseUri = Uri(s"$path/v2/networks")
    val baseQuery = Query(
      Map("limit" -> pageSize.toString) ++
      ids.map(ids => "ids" -> ids.mkString(","))
    )

    Source.unfoldAsync[Option[Option[String]], List[NetworkDTO]](Some[Option[String]](Option.empty)) {
      case None => Future.successful(None)
      case Some(nextOpt) =>
        val uri = nextOpt match {
          case None => baseUri.withQuery(baseQuery)
          case Some(value) => baseUri.withQuery(("next" -> value) +: baseQuery)
        }

        client
          .get[Page[NetworkDTO]](uri)
          .map { response =>
            if (response.next.isEmpty) Some((None, response.result))
            else Some((Some(response.next), response.result))
          }
    }
  }
}

object HttpNetworksClient {
  def apply(networksClientConfig: NetworkClientConfig)
    (implicit ec: ExecutionContext, mat: Materializer, as: ActorSystem): HttpNetworksClient =
    new HttpNetworksClient(new FutureHttpClient(Http(), networksClientConfig.httpClient), networksClientConfig.apiPrefix)(ec, mat)
}
