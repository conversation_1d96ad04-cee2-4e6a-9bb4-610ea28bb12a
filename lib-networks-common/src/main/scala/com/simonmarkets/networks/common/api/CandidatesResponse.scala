package com.simonmarkets.networks.common.api

import com.simonmarkets.networks.common.domain.PassportUserCandidate
import io.circe.Decoder
import io.circe.generic.extras.Configuration
import io.circe.generic.extras.semiauto.deriveConfiguredDecoder

case class CandidatesResponse(
    users: Seq[PassportUserCandidate]
)

object CandidatesResponse {
  implicit val config: Configuration = Configuration.default.withSnakeCaseMemberNames
  implicit val decoder: Decoder[CandidatesResponse] = deriveConfiguredDecoder[CandidatesResponse]
  implicit val decoderCandidate: Decoder[PassportUserCandidate] = deriveConfiguredDecoder[PassportUserCandidate]
}