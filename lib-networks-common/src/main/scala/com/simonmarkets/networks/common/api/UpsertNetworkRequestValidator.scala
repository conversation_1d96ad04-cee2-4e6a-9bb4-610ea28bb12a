package com.simonmarkets.networks.common.api

import com.goldmansachs.marquee.pipg.Network.LocationValidationException
import com.goldmansachs.marquee.pipg.{CertificationAlternativesForProduct, CustomRoleDefinition, LocationNode, NetworkLocation, OmsAlias}
import com.simonmarkets.capabilities.{Capabilities, CustomRoleCapabilities}
import com.simonmarkets.http.HttpError
import com.simonmarkets.networks.NetworkIds.AdminNetworkId
import simon.Id.NetworkId

import scala.concurrent.Future

object UpsertNetworkRequestValidator {

  def validate(request: UpsertNetworkRequest, mergedCustomRoles: Set[CustomRoleDefinition]): Future[Unit] = {
    val valid = for {
      _ <- validateNetworkName(request.networkName)
      _ <- validateMultiPayoffCertificationRequirementsList(request.siCertificationRequirements)
      _ <- validateMultiPayoffCertificationRequirementsList(request.annuityCertificationRequirements)
      _ <- validateMultiPayoffCertificationRequirementsList(request.definedOutcomeETFCertificationRequirements)
      _ <- validateMultiPayoffCertificationRequirementsList (request.altCertificationRequirements.getOrElse(Seq.empty))
      _ <- validateCustomRolesConfig(mergedCustomRoles)
      _ <- validateLocations(request.locations)
      _ <- validateLearnContent(request.endUserShareableContent, request.learnContent)
      _ <- validateAdminCapability(request.id, request.customRolesConfig)
    } yield this
    valid match {
      case Left(error) => Future.failed(HttpError.badRequest(error))
      case Right(_) => Future.unit
    }
  }

  def validateNetworkName(networkName: String): Either[String, Unit] = {
    networkName match {
      case name if name == null || name.trim.isEmpty => Left("Network Name can't be empty")
      case _ => Right(None)
    }
  }

  def validateMultiPayoffCertificationRequirementsList(
      requirements: Seq[CertificationAlternativesForProduct]): Either[String, Unit] = {

    val requirementKeys: Seq[String] = requirements.flatMap { req =>
      val productCompensationString = req.compensationType.fold(req.productId)(ct => req.productId + "_" + ct.productPrefix)
      val underliersStrings = req.underliers.fold(Seq.empty[String])(identity)

      if (underliersStrings.isEmpty) {
        Seq(productCompensationString)
      } else {
        underliersStrings.map(underlierString => productCompensationString + "_" + underlierString)
      }
    }

    val uniqueRequirementKeys = requirementKeys.distinct
    val diff = requirementKeys.diff(uniqueRequirementKeys)

    if (diff.isEmpty) {
      Right(None)
    } else {
      Left("Payoff certification requirements contains multiple definitions for a single productId. Duplicated " +
        s"productId_compensationType_underlier: ${diff.mkString(",")}")
    }
  }

  def validateOmsAlias(omsAlias: Option[String]): Either[String, Option[String]] = {
    omsAlias match {
      case Some(name) =>
        OmsAlias.getByName(name)
          .toRight(s"unsupported oms alias: $name, valid aliases: ${OmsAlias.aliasesNames.mkString(", ")}")
          .map(v => Some(v.name))
      case None => Right(None)
    }
  }

  def validateCustomRolesConfig(customRolesConfig: Set[CustomRoleDefinition]): Either[String, Unit] = {
    if (customRolesConfig.map(_.role).size == customRolesConfig.size) {
      val unrecognizedCapabilities = customRolesConfig.flatMap(customRoleDefinition => customRoleDefinition.capabilities).filter(unknownCapability)

      if (unrecognizedCapabilities.nonEmpty) {
        Left(s"Unrecognized capability specified in customRolesConfig: $unrecognizedCapabilities")
      }
      else if (customRolesConfig.map(_.role).exists( _.contains(" "))) {
        Left("Custom role must not contain spaces")
      }
      else {
        Right(None)
      }
    }
    else {
      Left("Duplicate definitions for a custom role")
    }
  }

  def validateLearnContent(endUserShareableContent: Seq[String], learnContent: Seq[String]): Either[String, Unit] = {
    if (!endUserShareableContent.toSet.subsetOf(learnContent.toSet)) {
      Left("EndUserShareableContent should be subset of learnContent")
    } else {
      Right(None)
    }
  }

  def validateAdminCapability(networkId: Option[String], customRoles: Set[CustomRoleDefinition]): Either[String, Unit] = {
    val isNotAdminNetwork = !networkId.contains(NetworkId.unwrap(AdminNetworkId))
    val containsAdminCapability = customRoles.flatMap(_.capabilities).contains(Capabilities.Admin)

    if (isNotAdminNetwork && containsAdminCapability) {
      Left("This network can not be assigned roles with admin capabilities")
    } else {
      Right(None)
    }
  }

  def validateLocations(locations: Set[NetworkLocation]): Either[String, Option[LocationNode]] = buildLocationHierarchy(locations)

  private def buildLocationHierarchy(locations: Set[NetworkLocation]): Either[String, Option[LocationNode]] = {
    if (locations.isEmpty) {
      Right(None)
    } else {
      try {
        val namesToLocations = locations.map(location => location.name -> location).toMap
        for {
          _ <- if (namesToLocations.size < locations.size) Left("Conflicting location definitions") else Right(())
          roots = locations.filter(_.parent.isEmpty)
          _ <- if (roots.size != 1) Left("Exactly one root location is required") else Right(())
          (rootNode, visitedLocs) = buildLocationHierarchyHelper(roots.head, namesToLocations, Set.empty[String])
          _ <- if (visitedLocs.size != namesToLocations.size) Left(s"Number of locations processed: ${visitedLocs.size} does not match number of inputs: ${namesToLocations.size}") else Right(())
          res <- Right(Some(rootNode))
        } yield res
      } catch {
        case _: StackOverflowError => Left("Stack overflow - most likely due to tree depth")
        case e: LocationValidationException => Left(e.message)
      }
    }
  }

  private def buildLocationHierarchyHelper(currentLocation: NetworkLocation, namesToLocations: Map[String, NetworkLocation], visited: Set[String]): (LocationNode, Set[String]) = {
    if (visited.contains(currentLocation.name)) throw LocationValidationException(s"Location ${currentLocation.name} was already visited")
    if (currentLocation.parent.nonEmpty && !visited.contains(currentLocation.parent.get)) throw LocationValidationException(s"Location ${currentLocation.name}'s parent ${currentLocation.parent.get} does not exist")
    if (currentLocation.parent.nonEmpty && !namesToLocations(currentLocation.parent.get).children.contains(currentLocation.name)) throw LocationValidationException(s"Location ${currentLocation.name}'s parent ${currentLocation.parent.get} does not claim it as a child")

    val visitedPlusCurrent = visited + currentLocation.name
    val (childNodes, allVisited) = currentLocation.children.foldLeft((Set.empty[LocationNode], visitedPlusCurrent)) {
      case ((nodeAccumulator, visitedLocAccumulator), childName) =>
        val childData = namesToLocations.getOrElse(childName, throw LocationValidationException(s"No location data presented for ${currentLocation.name}'s child: $childName"))
        val (childNode, visitedPlusCurrentPlusChildren) = buildLocationHierarchyHelper(childData, namesToLocations, visitedLocAccumulator)
        (nodeAccumulator + childNode, visitedPlusCurrentPlusChildren)
    }
    (LocationNode(currentLocation.name, childNodes), allVisited)
  }

  private def unknownCapability(capability: String): Boolean = !CustomRoleCapabilities.toSet.contains(capability)

}
