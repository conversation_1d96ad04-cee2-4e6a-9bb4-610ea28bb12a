package com.simonmarkets.networks.common

import com.goldmansachs.marquee.pipg.Network.Action
import com.goldmansachs.marquee.pipg.{DeadlineConfig => LegacyDeadlineConfig, Network => LegacyNetwork, ProspectusDeadlineConfig => LegacyProspectusDeadlineConfig, _}
import com.simonmarkets.networks.enums.{CrmProvider, DomicileCode}
import com.simonmarkets.networks.{ExternalId, SmaStrategyAndUnderliers}
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import io.simon.openapi.annotation.Field.Type
import io.simon.openapi.annotation.OpenApiType
import simon.Id.{ExternalNetworkId, NetworkId}

case class Network(
    @Type(OpenApiType.String)
    id: NetworkId,
    name: String,
    idHubOrganization: IdHubOrganization, //TODO remove
    purviews: Option[Set[Purview]] = None,
    approverSet: Map[String, List[List[String]]] = Map.empty,
    ioiApproverSet: Map[String, List[List[String]]] = Map.empty,
    accountMappings: Option[List[String]] = None,
    networkTypes: Option[List[NetworkType]] = None,
    to: Option[List[String]] = None,
    cc: Option[List[String]] = None,
    purviewNetworks: Option[Set[IssuerPurview]] = None,
    salesFeeRuleIds: List[String] = Nil,
    capabilities: Map[String, List[String]] = Map.empty,
    // issuerKey -> contractType -> actions
    payoffEntitlements: Map[String, Map[String, List[String]]] = Map.empty,
    payoffEntitlementsV2: Map[String, Map[String, Set[Action]]] = Map.empty,
    dynamicRoles: Set[String] = Set.empty,
    distributorAlias: Option[ExternalAlias] = None,
    omsAlias: Option[String] = None,
    version: Int = 0,
    customRolesConfig: Set[CustomRoleDefinition] = Set.empty,
    maskedIds: Set[MaskedId] = Set.empty,
    booksCloseConfig: List[BooksCloseConfig] = List.empty,
    booksCloseCustomConfig: List[BooksCloseConfig] = List.empty,
    booksSendConfig: Option[List[BooksCloseConfig]] = None,
    prospectusDeadlineConfig: Option[List[ProspectusDeadlineConfig]] = None,
    dtccId: Option[String] = None,
    locations: Set[NetworkLocation] = Set.empty,
    entitlements: Set[String] = Set.empty,
    annuityEAppProvider: Option[AnnuityEAppProvider] = None,
    ssoPrefix: Option[SSOPrefix] = None,
    contactInfo: Option[ContactInfo] = None,
    learnTracksV2: Seq[LearnTrack] = Seq.empty,
    learnContent: Seq[String] = Seq.empty,
    endUserShareableContent: Seq[String] = Seq.empty,
    idpId: Option[String] = None,
    siCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    annuityCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    definedOutcomeETFCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    certificationProducts: Option[Seq[String]] = None,
    group: Option[String] = None,
    externalId: Option[ExternalNetworkId] = None,
    externalIds: Set[ExternalId] = Set.empty,
    partnerUrls: Set[PartnerUrl] = Set.empty,
    // networkCode is a unique id, used to prefix okta login names, we want to keep it as short as possible, because okta login is max 100 characters
    networkCode: String,
    crmProviders: List[CrmProvider] = List.empty[CrmProvider],
    loginMode: LoginMode,
    embeddingInfo: Option[EmbeddingInfo] = None,
    eventInfo: EventInfo,
    smaStrategiesAndUnderliers: Set[SmaStrategyAndUnderliers] = Set.empty,
    smaRestrictedIssuers: Set[String] = Set.empty,
    contactInfo2: Option[List[ContactInfo2]] = None,
    contactInfo2Name: Option[String] = None,
    altCertificationRequirements: Option[Seq[CertificationAlternativesForProduct]] = None,
    uiViewCardOverrides: Map[String, List[String]] = Map.empty,
    uiViewCardOverridesExpiryDate: Map[String, String] = Map.empty,
    historicHoldingsStartFrom: Option[String] = None,
    cobrandingCustomDisclosure: Option[String] = None,
    encryptionClientKey: Option[String] = None,
    sessionInactivityTimeout: Option[Int] = None,
    landingPage: Option[LandingPage] = None,
    domiciles: Option[Set[DomicileCode]] = None,
    productTypeCertificationRequirements: Option[Seq[ProductCertificationRequirements]] = None,
    maxSystemUsers: Option[Int] = None,
    wlpUrl: Option[String] = None
) {

  val learnTracks: Seq[String] = learnTracksV2.map(_.trackId)

  def withIdpId(id: Option[String]): Network = this.copy(idpId = id)

  def withDynamicRoles: Network = this.copy(
    dynamicRoles = com.goldmansachs.marquee.pipg.Network.DynamicRoles(
      networkId = id,
      purviewNetworks = purviewNetworks getOrElse Set.empty,
      networkCapabilities = capabilities.mapValues(_.toSet),
      payoffEntitlements = payoffEntitlementsV2
    )
  )

  def withEntitlements(entitlements: Set[String]): Network = this.copy(entitlements = entitlements)

  def increaseVersion(): Network = this.copy(version = version + 1)

  def asLegacyNetwork: LegacyNetwork = {
    def convertProspectusDeadLine(
        deadlineConfig: Option[List[ProspectusDeadlineConfig]]): Option[List[LegacyProspectusDeadlineConfig]] = {
      deadlineConfig.map(configList => configList.map(config =>
        LegacyProspectusDeadlineConfig(config.tier,
          LegacyDeadlineConfig(config.deadlineConfig.dayOffset, config.deadlineConfig.hourOffset, config.deadlineConfig.minuteOffset))))
    }

    LegacyNetwork(
      id = id,
      networkName = name,
      idHubOrganization = idHubOrganization,
      purviews = purviews,
      approverSet = approverSet,
      ioiApproverSet = ioiApproverSet,
      accountMappings = accountMappings,
      networkTypes = networkTypes,
      to = to,
      cc = cc,
      purviewNetworks = purviewNetworks,
      salesFeeRuleIds = salesFeeRuleIds,
      capabilities = capabilities,
      payoffEntitlements = payoffEntitlements,
      payoffEntitlementsV2 = payoffEntitlementsV2,
      dynamicRoles = dynamicRoles,
      distributorAlias = distributorAlias,
      omsAlias = omsAlias,
      version = version,
      customRolesConfig = customRolesConfig,
      maskedIds = maskedIds,
      booksCloseConfig = booksCloseConfig,
      booksCloseCustomConfig = booksCloseCustomConfig,
      booksSendConfig = booksSendConfig,
      prospectusDeadlineConfig = convertProspectusDeadLine(prospectusDeadlineConfig),
      dtccId = dtccId,
      locations = locations,
      entitlements = entitlements,
      annuityEAppProvider = annuityEAppProvider,
      ssoPrefix = ssoPrefix,
      contactInfo = contactInfo,
      learnTracksV2 = learnTracksV2,
      learnContent = learnContent,
      endUserShareableContent = endUserShareableContent,
      idpId = idpId,
      siCertificationRequirements = siCertificationRequirements,
      annuityCertificationRequirements = annuityCertificationRequirements,
      definedOutcomeETFCertificationRequirements = definedOutcomeETFCertificationRequirements,
      certificationProducts = certificationProducts,
      crmProviders = crmProviders,
      smaStrategiesAndUnderliers = smaStrategiesAndUnderliers,
      smaRestrictedIssuers = smaRestrictedIssuers,
      altCertificationRequirements = altCertificationRequirements,
      group = group,
      productTypeCertificationRequirements = productTypeCertificationRequirements,
    )
  }
}
