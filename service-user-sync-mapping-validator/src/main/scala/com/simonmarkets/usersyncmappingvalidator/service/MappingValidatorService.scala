package com.simonmarkets.usersyncmappingvalidator.service

import akka.http.scaladsl.model.ContentTypes
import akka.stream.Materializer
import akka.stream.scaladsl.{Sink, Source}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.clients.emailsender._
import com.simonmarkets.networks.common.clients.usersync._
import com.simonmarkets.networks.common.clients.whitelabel.IcnWhiteLabelPartnerClient
import com.simonmarkets.networks.{NetworkDTO, NetworksClient}
import com.simonmarkets.usersyncmappingvalidator.model.{LookupKey, LookupKeyNames, WhiteLabelPartnerWithFirms}
import io.circe.Json

import java.time.LocalDate
import java.util.{Base64, UUID}

import scala.concurrent.{ExecutionContext, Future}

trait MappingValidatorService {

  def findMissingMappings(implicit traceId: TraceId): Future[Seq[LookupKey]]

}

class MappingValidatorServiceImpl(
    mappingClient: UserSyncMappingClient,
    networksClient: NetworksClient,
    wlpClient: IcnWhiteLabelPartnerClient,
    emailClient: EmailSenderClient,
    emailRecipients: Set[String]
)(implicit ec: ExecutionContext, mat: Materializer) extends MappingValidatorService with TraceLogging {

  def findMissingMappings(implicit traceId: TraceId): Future[Seq[LookupKey]] = {

    log.info("Initializing mapping validation")

    val allMappingsFut = mappingClient.streamAll.runReduce(_ ++ _)
    val allNetworksFut = networksClient.streamAll().runReduce(_ ++ _)
    val allWlpsFut = getAllWlps

    for {
      allMappings <- allMappingsFut
      _ = log.info("Retrieved all mappings")

      allNetworks <- allNetworksFut
      _ = log.info("Retrieved all networks")

      allWlps <- allWlpsFut
      _ = log.info("Retrieved all white labels and firms")

      //build keyed map for quick lookup
      mappingsMap = allMappings
        .map(mapping => LookupKey(primary = mapping.source_primary, secondary = mapping.source_secondary) -> mapping)
        .toMap

      //check all network + location
      missingNetworks = allNetworks.flatMap(getMissingMappingForNetwork(_, mappingsMap))

      //check all wlp + firm
      missingWlps = allWlps.flatMap(getMissingMappingForWlpFirm(_, mappingsMap))

      asCsv = toCsv(allNetworks, allWlps, missingNetworks, missingWlps)
      _ = log.info("Mapping validation complete, emailing results")
      _ <- sendEmail(asCsv, emailRecipients)
    } yield missingNetworks ++ missingWlps
  }

  private def getMissingMappingForNetwork(
      network: NetworkDTO,
      mappings: Map[LookupKey, UserNetworkMappingResponse]
  ): Set[LookupKey] = {

    val globalLookupKey = LookupKey(
      primary = SourceDestinationPrimary(network.id.toString, SourceDestinationPrimaryIdKind.`SIMON_NETWORK`),
      secondary = None
    )

    if (mappings.contains(globalLookupKey)) Set.empty
    else {
      val lookupKeys =
        if (network.locations.isEmpty) Set(globalLookupKey)
        else
          network.locations.map { location =>
            globalLookupKey.copy(
              secondary = Some(SourceDestinationSecondary(location.name, SourceDestinationSecondaryIdKind.`SIMON_LOCATION`))
            )
          }

      lookupKeys.flatMap { key =>
        if (mappings.contains(key)) None
        else Some(key)
      }
    }
  }

  private def getMissingMappingForWlpFirm(
      wlp: WhiteLabelPartnerWithFirms,
      mappings: Map[LookupKey, UserNetworkMappingResponse]
  ): Set[LookupKey] = {

    val globalLookupKey = LookupKey(
      primary = SourceDestinationPrimary(wlp.id, SourceDestinationPrimaryIdKind.`ICN_WHITE_LABEL`),
      secondary = None
    )

    if (mappings.contains(globalLookupKey)) Set.empty
    else {
      val lookupKeys = {
        if (wlp.firms.isEmpty) Set(globalLookupKey)
        else
          wlp.firms.map { firm =>
            globalLookupKey.copy(
              secondary = Some(SourceDestinationSecondary(firm.id.toString, SourceDestinationSecondaryIdKind.`ICN_FIRM`))
            )
          }
      }

      lookupKeys.flatMap { key =>
        if (mappings.contains(key)) None
        else Some(key)
      }
    }
  }

  private def getAllWlps(implicit traceId: TraceId) = {

    log.info("Getting all WLPs and firms")

    for {
      allWlps <- wlpClient.getAll
      allWlpsWithFirms <-
        Source(allWlps)
          .mapAsync(10) { wlp =>
            val wlpId = wlp.white_label_partner.id.toString
            wlpClient
              .getAllFirmsForWhiteLabelPartner(wlpId)
              .map(firms => WhiteLabelPartnerWithFirms(wlpId, wlp.white_label_partner.name, firms))
          }
          .runWith(Sink.seq)
    } yield allWlpsWithFirms

  }

  private def toCsv(
      allNetworks: Seq[NetworkDTO],
      allWlps: Seq[WhiteLabelPartnerWithFirms],
      missingNetworks: Seq[LookupKey],
      missingWlps: Seq[LookupKey]
  ): String = {

    //prettify, consolidate, and sort items
    val networkMap = allNetworks
      .flatMap { n =>
        n.locations.toList.map { l =>
          (n.id.toString, Some(l.name)) -> LookupKeyNames(
            n.networkName,
            Some(l.name)
          )
        } :+ (n.id.toString, Option.empty[String]) -> LookupKeyNames(n.networkName, None)
      }
      .toMap

    val wlpMap = allWlps
      .flatMap { w =>
        w.firms.toList.map { f =>
          (w.id, Some(f.id.toString)) -> LookupKeyNames(
            w.name,
            Some(f.label)
          )
        } :+ (w.id, Option.empty[String]) -> LookupKeyNames(w.name, None)
      }
      .toMap

    val prettyNetworksStr = missingNetworks
      .sortBy(_.defaultSortKey)
      .map { key =>
        val names = networkMap((key.primary.external_id, key.secondary.map(_.external_id)))
        key.csv(names.primaryName, names.secondaryName)
      }
    val pretyWlpsStr = missingWlps
      .sortBy(_.defaultSortKey)
      .map { key =>
        val names = wlpMap((key.primary.external_id, key.secondary.map(_.external_id)))
        key.csv(names.primaryName, names.secondaryName)
      }

    LookupKey.csvHeader + (prettyNetworksStr ++ pretyWlpsStr).mkString("\n")

  }

  private def sendEmail(csv: String, emails: Set[String])(implicit traceId: TraceId) = {
    val subject = s"${TemplateId.`user-sync-mapping`.productPrefix}: ${LocalDate.now.toString}"
    val request = EmailRequest(
      eventId = UUID.randomUUID().toString,
      eventType = TemplateId.`user-sync-mapping`.productPrefix,
      recipients = Recipients(
        userIds = Set.empty,
        emails = emails.map(e => Recipient(e, None, None)),
      ),
      templateId = TemplateId.`user-sync-mapping`,
      content = Json.Null,
      subject = subject,
      attachments = Set(
        EmailAttachment(
          fileName = s"$subject.csv",
          fileType = ContentTypes.`text/csv(UTF-8)`.toString,
          data = Base64.getEncoder.encodeToString(csv.getBytes)
        )
      )
    )

    emailClient.sendEmail(request)

  }

}
