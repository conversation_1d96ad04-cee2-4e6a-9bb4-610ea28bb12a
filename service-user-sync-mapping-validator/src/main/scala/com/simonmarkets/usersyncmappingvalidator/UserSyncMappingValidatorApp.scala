package com.simonmarkets.usersyncmappingvalidator

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.usersyncmappingvalidator.config.AppConfiguration
import com.simonmarkets.usersyncmappingvalidator.di.ServiceLocator
import com.simonmarkets.usersyncmappingvalidator.model.LookupKey
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.typesafe.config.ConfigFactory
import pureconfig.loadConfigOrThrow

import scala.concurrent.ExecutionContext
import pureconfig.generic.auto._

import scala.concurrent.duration.DurationInt

object UserSyncMappingValidatorApp extends TraceLogging {

  private implicit val traceId: TraceId = TraceId("user-sync-mapping-validator")

  log.info("Starting actor system")
  private final implicit val system: ActorSystem = ActorSystem("UserSyncMappingValidator")
  log.info("Creating materializer")
  private implicit val mat: Materializer = Materializer(system)
  log.info("Setting up execution context")
  private implicit val executionContext: ExecutionContext = system.dispatcher
  private val rawConfig = ConfigFactory.load().resolveSecrets()
  private val config = loadConfigOrThrow[AppConfiguration](rawConfig)
  private val locator = new ServiceLocator(config)

  def findAllMissingMappings: Seq[LookupKey] =
    if (config.enabled) locator.mappingValidatorService.findMissingMappings.await(15.minutes)
    else {
      log.info("Validator disabled")
      Nil
    }
}
