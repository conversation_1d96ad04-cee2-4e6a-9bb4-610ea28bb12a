package com.simonmarkets.usersyncmappingvalidator.model

import com.simonmarkets.networks.common.clients.usersync.{SourceDestinationPrimary, SourceDestinationSecondary}
import com.simonmarkets.networks.common.clients.whitelabel.FirmResponse


case class WhiteLabelPartnerWithFirms(
    id: String,
    name: String,
    firms: Set[FirmResponse] = Set.empty
)

case class LookupKey(
    primary: SourceDestinationPrimary,
    secondary: Option[SourceDestinationSecondary]
){

  def csv(primaryName: String, secondaryName: Option[String]): String =
    s"${primary.kind},${primary.external_id},$primaryName" + secondary.map(s => s",${s.kind},${s.external_id},${secondaryName.get}").getOrElse("")

  def defaultSortKey: String = s"${primary.kind}${primary.external_id}${secondary.map(_.external_id).getOrElse("")}"

}

case class LookupKeyNames(
    primaryName: String,
    secondaryName: Option[String] = None
)

object LookupKey {

  val csvHeader = "sourceKind,sourceId,sourceName,secondaryKind,secondaryId,secondaryName\n"

}
