package com.simonmarkets.usersyncmappingvalidator.service

import akka.actor.ActorSystem
import akka.stream.Materializer
import akka.stream.scaladsl.Source
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.clients.emailsender.{EmailRequest, EmailSenderClient}
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationPrimaryIdKind.{`ICN_WHITE_LABEL`, `SIMON_NETWORK`}
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationSecondaryIdKind.{`ICN_FIRM`, `SIMON_LOCATION`}
import com.simonmarkets.networks.common.clients.usersync.SourceDestinationSystemName.{ICN, SIMON}
import com.simonmarkets.networks.common.clients.usersync.{MatchByPropertyName, SourceDestinationMatchByProperty, SourceDestinationPrimary, SourceDestinationSecondary, SourceDestinationSecondaryIdKind, SourceDestinationSystem, UserNetworkMappingResponse, UserSyncMappingClient}
import com.simonmarkets.networks.common.clients.whitelabel.{FirmResponse, IcnWhiteLabelPartnerClient, WhiteLabelPartner, WhiteLabelPartnerResponse}
import com.simonmarkets.networks.{HttpNetworksClient, NetworkDTO, NetworkLocationDTO}
import com.simonmarkets.syntax.{anyOpsConversion, futureOpsConversion}
import com.simonmarkets.usersyncmappingvalidator.model.LookupKey
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future


class MappingValidatorServiceSpec extends WordSpec with MockitoSugar with Matchers with BeforeAndAfterEach {

  //networks
  private val loc1 = "loc1"
  private val loc2 = "loc2"

  private val network1 = NetworkDTO(
    id = NetworkId("network1"),
    networkName = "network1Name",
    dtccId = None,
    locations = Set(NetworkLocationDTO(loc1, None, Set.empty), NetworkLocationDTO(loc2, None, Set.empty))
  )
  private val network2 = NetworkDTO(
    id = NetworkId("network2"),
    networkName = "network2Name",
    dtccId = None,
    locations = Set.empty
  )

  //wlps
  private val wlp1 = WhiteLabelPartner(
    id = 1,
    name = "wlp1"
  )
  private val wlp2 = WhiteLabelPartner(
    id = 2,
    name = "wlp2"
  )

  private val wlpResp1 = WhiteLabelPartnerResponse(wlp1)
  private val wlpResp2 = WhiteLabelPartnerResponse(wlp2)

  private val firm1 = FirmResponse(
    id = 1,
    label = "firm1"
  )
  private val firm2 = FirmResponse(
    id = 2,
    label = "firm2"
  )


  private val wlpMap1_1 = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(SIMON),
    destination_primary = SourceDestinationPrimary(network1.id.toString, `SIMON_NETWORK`),
    destination_secondary = SourceDestinationSecondary(loc1, SourceDestinationSecondaryIdKind.`SIMON_LOCATION`).some,
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(ICN),
    source_primary = SourceDestinationPrimary(wlp1.id.toString, `ICN_WHITE_LABEL`),
    source_secondary = SourceDestinationSecondary(firm1.id.toString, `ICN_FIRM`).some,
    entitlements = Set.empty,
    fields = Set.empty
  )
  private val wlpMap1_2 = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(SIMON),
    destination_primary = SourceDestinationPrimary(network1.id.toString, `SIMON_NETWORK`),
    destination_secondary = SourceDestinationSecondary(loc2, SourceDestinationSecondaryIdKind.`SIMON_LOCATION`).some,
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(ICN),
    source_primary = SourceDestinationPrimary(wlp1.id.toString, `ICN_WHITE_LABEL`),
    source_secondary = SourceDestinationSecondary(firm2.id.toString, `ICN_FIRM`).some,
    entitlements = Set.empty,
    fields = Set.empty
  )
  private val wlpMap2 = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(SIMON),
    destination_primary = SourceDestinationPrimary(network2.id.toString, `SIMON_NETWORK`),
    destination_secondary = None,
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(ICN),
    source_primary = SourceDestinationPrimary(wlp2.id.toString, `ICN_WHITE_LABEL`),
    source_secondary = None,
    entitlements = Set.empty,
    fields = Set.empty
  )
  private val netMap1_1 = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(ICN),
    destination_primary = SourceDestinationPrimary(wlp1.id.toString, `ICN_WHITE_LABEL`),
    destination_secondary = SourceDestinationSecondary(firm1.id.toString, `ICN_FIRM`).some,
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(SIMON),
    source_primary = SourceDestinationPrimary(network1.id.toString, `SIMON_NETWORK`),
    source_secondary = SourceDestinationSecondary(loc1, SourceDestinationSecondaryIdKind.`SIMON_LOCATION`).some,
    entitlements = Set.empty,
    fields = Set.empty
  )
  private val netMap1_2 = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(ICN),
    destination_primary = SourceDestinationPrimary(wlp1.id.toString, `ICN_WHITE_LABEL`),
    destination_secondary = SourceDestinationSecondary(firm2.id.toString, `ICN_FIRM`).some,
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(SIMON),
    source_primary = SourceDestinationPrimary(network1.id.toString, `SIMON_NETWORK`),
    source_secondary = SourceDestinationSecondary(loc2, SourceDestinationSecondaryIdKind.`SIMON_LOCATION`).some,
    entitlements = Set.empty,
    fields = Set.empty
  )
  private val netMap2 = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(ICN),
    destination_primary = SourceDestinationPrimary(wlp2.id.toString, `ICN_WHITE_LABEL`),
    destination_secondary = None,
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(SIMON),
    source_primary = SourceDestinationPrimary(network2.id.toString, `SIMON_NETWORK`),
    source_secondary = None,
    entitlements = Set.empty,
    fields = Set.empty
  )
  private val wlp1Fallback = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(SIMON),
    destination_primary = SourceDestinationPrimary(network1.id.toString, `SIMON_NETWORK`),
    destination_secondary = None,
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(ICN),
    source_primary = SourceDestinationPrimary(wlp1.id.toString, `ICN_WHITE_LABEL`),
    source_secondary = None,
    entitlements = Set.empty,
    fields = Set.empty
  )
  private val net1Fallback = UserNetworkMappingResponse(
    destination_system = SourceDestinationSystem(ICN),
    destination_primary = SourceDestinationPrimary(wlp1.id.toString, `ICN_WHITE_LABEL`),
    destination_secondary = None,
    destination_property_matcher = SourceDestinationMatchByProperty(MatchByPropertyName.email),
    source_property_matcher = None,
    source_system = SourceDestinationSystem(SIMON),
    source_primary = SourceDestinationPrimary(network1.id.toString, `SIMON_NETWORK`),
    source_secondary = None,
    entitlements = Set.empty,
    fields = Set.empty
  )

  "UserSyncMappingValidator" can {
    "findMissingMappings" should {
      "when a network is missing" in new TestData {
        override def mappings: Set[UserNetworkMappingResponse] = Set(
          netMap1_1,
          netMap1_2,
          wlpMap1_1,
          wlpMap1_2,
          wlpMap2
        )

        mappingService.findMissingMappings.await shouldBe Seq(
          LookupKey(SourceDestinationPrimary(network2.id.toString, `SIMON_NETWORK`), None)
        )
      }

      "when a whitelabel is missing" in new TestData {
        override def mappings: Set[UserNetworkMappingResponse] = Set(
          netMap1_1,
          netMap1_2,
          netMap2,
          wlpMap1_1,
          wlpMap1_2,
        )

        mappingService.findMissingMappings.await shouldBe Seq(
          LookupKey(SourceDestinationPrimary(wlp2.id.toString, `ICN_WHITE_LABEL`), None)
        )
      }

      "when a location is missing" in new TestData {
        override def mappings: Set[UserNetworkMappingResponse] = Set(
          netMap1_2,
          netMap2,
          wlpMap1_1,
          wlpMap1_2,
          wlpMap2
        )

        mappingService.findMissingMappings.await shouldBe Seq(
          LookupKey(
            SourceDestinationPrimary(network1.id.toString, `SIMON_NETWORK`),
            SourceDestinationSecondary(loc1, `SIMON_LOCATION`).some
          )
        )
      }

      "when a firm is missing" in new TestData {
        override def mappings: Set[UserNetworkMappingResponse] = Set(
          netMap1_1,
          netMap1_2,
          netMap2,
          wlpMap1_2,
          wlpMap2
        )

        mappingService.findMissingMappings.await shouldBe Seq(
          LookupKey(
            SourceDestinationPrimary(wlp1.id.toString, `ICN_WHITE_LABEL`),
            SourceDestinationSecondary(firm1.id.toString, `ICN_FIRM`).some
          )
        )
      }

      "find nothing when a fallback network is enabled" in new TestData {
        override def mappings: Set[UserNetworkMappingResponse] = Set(
          net1Fallback,
          netMap2,
          wlpMap1_1,
          wlpMap1_2,
          wlpMap2
        )

        mappingService.findMissingMappings.await shouldBe Seq.empty
      }

      "find nothing when a fallback whitelabel is enabled" in new TestData {
        override def mappings: Set[UserNetworkMappingResponse] = Set(
          netMap1_1,
          netMap1_2,
          netMap2,
          wlpMap2,
          wlp1Fallback
        )

        mappingService.findMissingMappings.await shouldBe Seq.empty
      }

      "find nothing when all networks/whitelabels are mapping" in new TestData {
        mappingService.findMissingMappings.await shouldBe Seq.empty
      }

    }

  }

  private trait TestData {

    implicit val mat: Materializer = Materializer.matFromSystem(ActorSystem("test"))
    implicit val traceId: TraceId = TraceId.randomize

    private val mappingClient = mock[UserSyncMappingClient]
    private val networkClient = mock[HttpNetworksClient]
    private val wlpClient = mock[IcnWhiteLabelPartnerClient]
    private val emailClient = mock[EmailSenderClient]
    val mappingService = new MappingValidatorServiceImpl(
      mappingClient = mappingClient,
      networksClient = networkClient,
      wlpClient = wlpClient,
      emailClient = emailClient,
      emailRecipients = Set.empty[String]
    )

    //firm mocks
    when(wlpClient.getAllFirmsForWhiteLabelPartner(wlp1.id.toString))
      .thenReturn(Future.successful(Set(firm1, firm2)))
    when(wlpClient.getAllFirmsForWhiteLabelPartner(wlp2.id.toString))
      .thenReturn(Future.successful(Set.empty[FirmResponse]))

    def mappings: Set[UserNetworkMappingResponse] = Set(
      netMap1_1,
      netMap1_2,
      netMap2,
      wlpMap1_1,
      wlpMap1_2,
      wlpMap2
    )

    def wlps: Set[WhiteLabelPartnerResponse] = Set(wlpResp1, wlpResp2)

    def networks: Seq[NetworkDTO] = Seq(network1, network2)

    when(mappingClient.streamAll).thenReturn(Source.single(mappings))
    when(wlpClient.getAll).thenReturn(Future.successful(wlps))
    when(networkClient.streamAll()).thenReturn(Source.single(networks))
    when(emailClient.sendEmail(any[EmailRequest])(meq(traceId))).thenReturn(Future.successful(()))

  }

}
