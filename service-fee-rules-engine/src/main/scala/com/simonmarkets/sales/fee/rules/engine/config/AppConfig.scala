package com.simonmarkets.sales.fee.rules.engine.config

import com.simonmarkets.api.users.config.AclClientConfig
import com.simonmarkets.mongodb.config.MongoClientConfig
import com.simonmarkets.resteasy.framework.RunMode
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration.SystemRoutesConfig
import com.simonmarkets.resteasy.framework.config.authn.AuthenticationConfiguration
import com.simonmarkets.resteasy.framework.info.InfoConfig
import com.typesafe.config.Config
import pureconfig.{CamelCase, ConfigFieldMapping}
import pureconfig.generic.ProductHint
import pureconfig.generic.auto._


case class AppConfig(
    systemRoutes: Option[SystemRoutesConfig],
    authentication: Option[AuthenticationConfiguration],
    aclClientConfig: AclClientConfig,
    mongoDB: MongoDBConfig,
    apiPrefix: String,
    runMode: RunMode,
    info: Option[InfoConfig]
) extends RestEasyAppConfiguration

case class DbConfiguration(
    collection: String,
    database: String
)
case class MongoDBConfig(
    client: MongoClientConfig,
    networks: DbConfiguration,
    networksSnapshots: DbConfiguration,
    salesFeeRulesSnapshots: DbConfiguration,
    salesFeeRules: DbConfiguration
)
object AppConfig {
  implicit def hint[T]: ProductHint[T] = ProductHint[T](ConfigFieldMapping(CamelCase, CamelCase))

  def apply(config: Config): AppConfig = {
    pureconfig.loadConfigOrThrow[AppConfig](config)
  }
}