package com.simonmarkets.sales.fee.rules.engine

import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.mongodb.Client
import com.simonmarkets.networks.common.repository.MongoNetworkRepository
import com.simonmarkets.networks.common.service.networkservice.BasicNetworkService
import com.simonmarkets.resteasy.framework.RestEasyModule.RestEasyLambda
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import com.simonmarkets.sales.fee.rules.common.repository.MongoSalesFeeRuleRepository
import com.simonmarkets.sales.fee.rules.engine.config.AppConfig
import com.simonmarkets.sales.fee.rules.engine.routes.ServiceFeeRulesEngineRoutes
import com.simonmarkets.sales.fee.rules.engine.services.SalesFeeRulesEngineService
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import com.simonmarkets.sales.fee.rules.engine.config.AppConfig._
import io.simon.openapi.generator.OpenApiGenerator
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.{Document, MongoCollection}
import pureconfig.generic.auto._

object SalesFeeRulesEngineApp extends RestEasyModule[AppConfig] with App with TraceLogging {

  override def serviceName: String = "SalesFeeRulesEngine"

  override def servicePath: String = "simon/api/v1/sales-fee-rules-engine"

  OpenApiGenerator.generateOpenApiDocumentation

  override def init(environment: Environment): Unit = {

    val mongoClient = Client.create(config.mongoDB.client)
    val userACLDirective = UserAclAuthorizedDirective(config.aclClientConfig)

    val salesFeeRulesCollection: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.salesFeeRules.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.salesFeeRules.collection)

    val salesFeeRulesSnapshots: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.salesFeeRulesSnapshots.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.salesFeeRulesSnapshots.collection)

    val networkCollection: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.networks.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.networks.collection)

    val networkSnapshotsCollection: MongoCollection[Document] = mongoClient
      .getDatabase(config.mongoDB.networksSnapshots.database)
      .withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      .getCollection[Document](config.mongoDB.networksSnapshots.collection)

    val networkRepository = new MongoNetworkRepository(networkCollection, networkSnapshotsCollection, mongoClient)
    val salesFeeRulesRepository = new MongoSalesFeeRuleRepository(salesFeeRulesCollection, salesFeeRulesSnapshots, mongoClient)
    val networksService = new BasicNetworkService(networkRepository)
    val service = new SalesFeeRulesEngineService(networksService, salesFeeRulesRepository)
    val routes = ServiceFeeRulesEngineRoutes(userACLDirective, service).routes
    environment.addRoutes(routes)
  }
  RestEasy.start()
  object EntryPoint extends RestEasyLambda
}
