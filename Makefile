##
# Makefile to generate argocd manifets and templates
##

SHELL := bash -euo pipefail -c
MAKE_FLAGS += --warn-undefined-variables

THIS_MAKEFILE = $(abspath $(firstword $(MAKEFILE_LIST)))
SRC_ROOT = $(shell dirname ${THIS_MAKEFILE})
INC_MAKEFILES_DIR := ${SRC_ROOT}/.makefiles

ARGOCD_CHARTS_DIR := ${SRC_ROOT}/charts
ARGOCD_REPO_PROXY ?= "false"

##
# Makefile.asserts.mk: check prerequisites
# Makefile.helpers.mk: helper scripts
##


.PHONY: all argocd-repo-creds argocd-repo argocd-proj argocd-app argocd-sync

all: argocd-proj argocd-app-sync

include ${INC_MAKEFILES_DIR}/helpers.mk
include ${INC_MAKEFILES_DIR}/asserts.mk

argocd-proj: argocd-repo
	@helm template ${ARGOCD_CHARTS_DIR}/argocd-project --set project=$${project} --set namespace=$${namespace} | \
	kubectl apply -f -

argocd-repo: argocd-repo-creds 
	@helm template ${ARGOCD_CHARTS_DIR}/argocd-repository --set env=$${env} --set project=$${project} --set enabledProxy=${ARGOCD_REPO_PROXY} | \
	kubectl apply -f -

#--set sshPrivateKey=$${git_ssh_key}
argocd-repo-creds:
	@helm template ${ARGOCD_CHARTS_DIR}/argocd-repo-creds | \
	kubectl apply -f -

argocd-app-sync: argocd-app
	@pkill -9 -f "kubectl port-forward" || true
	@kubectl port-forward svc/argocd-server -n argocd 8443:443 &
	@sleep 5 
	@argocd login localhost:8443 --insecure --username admin --password $${password}
	@argocd app set $${app_name} --helm-set-string deploy.image=$${deploy_image}
	@argocd app sync $${app_name}
	@pkill -9 -f "kubectl port-forward"        

argocd-app:
	@helm template ${ARGOCD_CHARTS_DIR}/argocd-application --set project=$${project} --set app_name=$${app_name} --set namespace=$${namespace} --set service_type=$${service_type} | \
	kubectl apply -f -
