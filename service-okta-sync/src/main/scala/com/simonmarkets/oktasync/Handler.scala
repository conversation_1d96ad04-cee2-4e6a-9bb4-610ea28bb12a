package com.simonmarkets.oktasync

import akka.actor.ActorSystem
import com.amazonaws.services.lambda.runtime.{Context, RequestStreamHandler}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.ExternalIdType
import com.simonmarkets.okta.client.api.HttpOktaClient
import com.simonmarkets.okta.client.sdk.OktaClientFactory
import com.simonmarkets.okta.domain.UserProfileProperty
import com.simonmarkets.okta.service.{OktaRepository, OktaService}
import com.simonmarkets.resteasy.utils.FutureOps.FutureOptionOps
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.typesafe.config.ConfigFactory
import io.circe.jawn

import java.io.{InputStream, OutputStream}
import pureconfig.generic.auto._

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success}

trait Handler extends RequestStreamHandler with TraceLogging with JsonCodecs {

  //dependency injection this way to play nice with lambda infra.  See below class implementation
  implicit val ec: ExecutionContext
  val oktaService: OktaService

  case class MongoEvent2[T](
      id: String,
      detail: Payload2[T]
  )

  case class Payload2[T](
      operationType: String,
      fullDocument: Option[T],
  )

  override def handleRequest(inputStream: InputStream, outputStream: OutputStream, context: Context): Unit = {

    implicit val traceId: TraceId = TraceId(context.getAwsRequestId)

    val source = scala.io.Source.fromInputStream(inputStream)
    val rawEvent = source.mkString
    source.close()
    val response = insertAttribute(rawEvent)

    Await.result(response, Duration.Inf)
  }

  private[oktasync] def insertAttribute(rawEvent: String)(implicit traceId: TraceId): Future[Unit] = {

    val event = jawn.decode[MongoEvent2[ExternalIdType]](rawEvent) match {
      case Left(error) => Failure(new Exception(s"Unable to parse event: $rawEvent \n$error"))
      case Right(value) => Success(value)
    }

    for {
      newAttribute <-
        Future.fromTry(event)
          .map(e => e.detail.fullDocument.map(_.idpFieldName))
          .flattenOption("Document missing idpFieldName")
          .map(attribute => UserProfileProperty(
            name = attribute,
            title = attribute,
            description = s"External Id Type $attribute")
          )
      response <- oktaService.addBaseProfileAttribute(newAttribute)
    } yield response

  }
}

class HandlerImpl extends Handler {

  implicit val ec: ExecutionContext = ExecutionContext.global
  implicit val ac: ActorSystem = ActorSystem.apply
  val config: OktaSyncConfig = pureconfig.loadConfigOrThrow[OktaSyncConfig](ConfigFactory.load().resolveSecrets())
  val oktaService: OktaService = OktaService(
    config = config.serviceConfig,
    repository = OktaRepository(
      client = OktaClientFactory.getOktaClient(config.clientConfig),
      httpClient  = HttpOktaClient(config.clientConfig)
    )
  )

}
