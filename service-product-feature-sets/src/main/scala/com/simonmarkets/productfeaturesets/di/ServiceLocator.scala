package com.simonmarkets.productfeaturesets.di

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.api.users.HttpACLClient
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.Client
import com.simonmarkets.productfeaturesets.config.AppConfiguration
import com.simonmarkets.productfeaturesets.repository.FeatureRepository
import com.simonmarkets.productfeaturesets.repository.FeatureRepository.FeatureRepositoryMongo
import com.simonmarkets.productfeaturesets.service.FeatureService
import com.simonmarkets.productfeaturesets.service.FeatureService.FeatureServiceImpl
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.resteasy.framework.RestEasyCore
import com.simonmarkets.resteasy.requests.AuthorizedDirectives
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import com.simonmarkets.utils.data.mongo.context.DatabaseContext

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

/** Gathers up all service and repository dependencies */
class ServiceLocator(config: AppConfiguration)(
    implicit system: ActorSystem,
    materializer: Materializer,
    executionContext: ExecutionContext,
    startupTraceId: TraceId
) extends TraceLogging {
  log.info("Initializing service locator")

  log.info("Initializing authorization directives")
  private val httpAclClient = HttpACLClient(config.aclClientConfig)
  private val systemUser = httpAclClient.getUserACL(config.systemUserId).await(Duration.Inf)
  private val userAclDirective = UserAclAuthorizedDirective(httpAclClient)
  val authDirective: AuthorizedDirectives[UserACL] = RestEasyCore
    .allowSystemCalls[UserACL](userAclDirective, User(systemUser.userId, "", None, Nil), systemUser)

  log.info("Starting mongo connection")
  private val mongoClient = Client.create(config.mongoDB.client)
  private val mongoDatabase = mongoClient.getDatabase(config.mongoDB.feature.database)
  private implicit val databaseContext: DatabaseContext = DatabaseContext(mongoDatabase, mongoClient)
  private val featureRepository: FeatureRepository = new FeatureRepositoryMongo()
  val featureService: FeatureService = new FeatureServiceImpl(featureRepository)
}