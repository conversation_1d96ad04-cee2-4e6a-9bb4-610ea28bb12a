package com.simonmarkets.productfeaturesets.access

import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder}
import com.simonmarkets.productfeaturesets.model.data.Feature

object FeatureKeysGenerator extends AcceptedAccessKeysGenerator[Feature] {
  override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[Feature]] = Map(
    Capabilities.Admin -> AcceptedKeyBuilder(buildAdminKeys)
  )
}