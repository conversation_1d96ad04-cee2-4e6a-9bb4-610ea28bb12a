package com.simonmarkets.productfeaturesets.routes

import akka.http.scaladsl.server._
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.error.Conflict
import com.simonmarkets.productfeaturesets.model.requests.{DeleteFeatureRequest, GetFeatureRequest, UpsertFeatureRequest}
import com.simonmarkets.productfeaturesets.service.FeatureService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.framework.RestEasyDirectives._
import com.simonmarkets.resteasy.requests.AuthorizedDirectives

class ProductFeatureSetsRoutes(
    authDirectives: AuthorizedDirectives[UserACL],
    featureService: FeatureService,
) extends TraceLogging with DirectivesWithCirce with JsonCodecs {

  import authDirectives._

  private val PATH_PREFIX = "feature-sets"

  def routes: Route = {
    pathPrefix("simon" / "api" / "v1") {
      concat(
        unauthenticated { tid =>
          implicit val traceId: TraceId = tid
          warmupRoute { request =>
            log.info("Received warmup request", request)
            complete("true")
          }
        },
        authorized() { (tid, acl) =>
          implicit val traceId: TraceId = tid
          implicit val userACL: UserACL = acl
          handleExceptions(customExceptionHandler) {
            concat(
              `GET /feature-sets/` {
                complete(featureService.getAll())
              },
              `GET /feature-sets/:id` { id =>
                complete(featureService.get(GetFeatureRequest(id)))
              },
              `POST /feature-sets/` {
                entity(as[UpsertFeatureRequest]) { request =>
                  complete(featureService.create(request))
                }
              },
              `PUT /feature-sets/:id` { id =>
                entity(as[UpsertFeatureRequest]) { request =>
                  complete(featureService.update(request, id))
                }
              },
              `DELETE /feature-sets/:id` { id =>
                complete(featureService.delete(DeleteFeatureRequest(id)))
              }
            )
          }
        }
      )
    }
  }

  private def `GET /feature-sets/:id`: Directive1[String] = path(PATH_PREFIX / Segment) & get

  private def `GET /feature-sets/`: Directive0 = path(PATH_PREFIX) & get

  private def `POST /feature-sets/`: Directive0 = path(PATH_PREFIX) & post

  private def `PUT /feature-sets/:id`: Directive1[String] = path(PATH_PREFIX / Segment) & put

  private def `DELETE /feature-sets/:id`: Directive1[String] = path(PATH_PREFIX / Segment) & delete

  private def customExceptionHandler(implicit traceId: TraceId): ExceptionHandler =
    ExceptionHandler {
      case ex: HttpError => complete(ex.httpResponse)
      case ex: Conflict => complete(HttpError.conflict(ex.message).httpResponse)
      case ex: IllegalArgumentException => complete(HttpError.notFound(ex.getMessage).httpResponse)
      case ex => extractUri {
        uri =>
          log.error(ex, s"Exception encountered: [$uri]")
          complete(HttpError.internalServerError("Server error while processing the request. Please contact SIMON Engineering team.").httpResponse)
      }
    }
}