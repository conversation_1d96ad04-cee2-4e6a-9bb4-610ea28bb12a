package com.simonmarkets.productfeaturesets.repository

import com.simonmarkets.mongodb.snapshots.SnapshotableRepository.RecoverFuture
import com.simonmarkets.productfeaturesets.model.data.Feature
import com.simonmarkets.utils.data.core.GenericRepository
import com.simonmarkets.utils.data.core.context.RepositoryContext
import com.simonmarkets.utils.data.mongo.SimonMongoRepository
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import com.simonmarkets.utils.data.mongo.model.SimonResourceCompanion
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.model.Filters.{and, equal, in}

import scala.concurrent.{ExecutionContext, Future}

trait FeatureRepository extends GenericRepository[Feature] {
  def getAll(availableKeys: Set[String])(implicit ctx: RepositoryContext): Future[Seq[Feature]]

  def createFeature(feature: Feature, availableKeys: Set[String])(implicit ctx: RepositoryContext): Future[Feature]

}

object FeatureRepository {

  object Fields {
    val id = "id"
    val title = "title"
    val description = "description"
    val assetClass = "assetClass"
    val supportContact = "supportContact"
    val capabilities = "capabilities"
    val accessKeys = "acceptedAccessKeys"
  }

  class FeatureRepositoryMongo(implicit ec: ExecutionContext,
      dbCtx: DatabaseContext) extends SimonMongoRepository[Feature] with FeatureRepository {

    private lazy val companion: SimonResourceCompanion[Feature] = implicitly[SimonResourceCompanion[Feature]]
    private lazy val collection: MongoCollection[Feature] = dbCtx.getCollection[Feature](companion.collectionName).withCodecRegistry(companion.codecRegistry)

    /**
     * Returns all Features
     *
     * @param availableKeys : the available access keys
     * @param ctx           : the repository contxt (implicit)
     * @return : A sequence of all features
     */
    override def getAll(availableKeys: Set[String])(implicit ctx: RepositoryContext): Future[Seq[Feature]] = {
      val filters = in(Fields.accessKeys, availableKeys.toList: _*)
      collection.find(filters).toFuture
    }

    /**
     * Returns a feature given an Id. Will only return the feature if the feature is valid
     *
     * @param id            : the feature id to lookup
     * @param availableKeys : the available access keys
     * @param ctx           : the repository contxt (implicit)
     * @return : The feature if found
     */
    override def get(id: String, availableKeys: Set[String])
      (implicit ctx: RepositoryContext): Future[Option[Feature]] = {
      val filters = List(equal(Fields.id, id), in(Fields.accessKeys, availableKeys.toList: _*))
      collection.find(and(filters: _*)).headOption()
    }

    override def createFeature(feature: Feature, availableKeys: Set[String])
      (implicit ctx: RepositoryContext): Future[Feature] =
      create(feature, availableKeys).refineDuplicateKey
  }
}