package com.simonmarkets.productfeaturesets.config

import com.simonmarkets.api.users.config.AclClientConfig
import com.simonmarkets.resteasy.framework.RunMode
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration.SystemRoutesConfig
import com.simonmarkets.resteasy.framework.info.InfoConfig
import pureconfig.generic.ProductHint
import pureconfig.{CamelCase, ConfigFieldMapping, KebabCase}
import com.simonmarkets.mongodb.config.MongoClientConfig
import com.simonmarkets.resteasy.framework.config.authn.AuthenticationConfiguration

case class AppConfiguration(
    runMode: RunMode,
    systemRoutes: Option[SystemRoutesConfig],
    authentication: Option[AuthenticationConfiguration],
    aclClientConfig: AclClientConfig,
    systemUserId: String,
    info: Option[InfoConfig],
    mongoDB: MongoConfiguration,
) extends RestEasyAppConfiguration

case class DbConfig(
    collection: String,
    database: String
)

case class MongoConfiguration(
    feature: DbConfig,
    client: MongoClientConfig
)

object AppConfiguration {
  implicit def hint[T]: ProductHint[T] = ProductHint(ConfigFieldMapping(CamelCase, KebabCase))
}