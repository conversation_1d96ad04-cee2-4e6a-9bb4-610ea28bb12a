package com.simonmarkets.productfeaturesets.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.FeatureCapabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.snapshots.SnapshotableRepository.RecoverFuture
import com.simonmarkets.productfeaturesets.model.data.Feature
import com.simonmarkets.productfeaturesets.model.requests.{DeleteFeatureRequest, GetFeatureRequest, UpsertFeatureRequest}
import com.simonmarkets.productfeaturesets.model.view.FeatureView
import com.simonmarkets.productfeaturesets.repository.FeatureRepository

import java.util.UUID

import scala.concurrent.{ExecutionContext, Future}

trait FeatureService {
  /** Returns the Feature by given search parameters, if present */
  def get(request: GetFeatureRequest)(implicit traceId: TraceId, user: UserACL): Future[FeatureView]

  /** Returns the Feature by given search parameters, if present */
  def getAll()(implicit traceId: TraceId, user: UserACL): Future[Seq[FeatureView]]

  /** Inserts the Feature */
  def create(request: UpsertFeatureRequest)(implicit traceId: TraceId, user: UserACL): Future[FeatureView]

  /** Updates the Feature */
  def update(request: UpsertFeatureRequest, id: String)(implicit traceId: TraceId, user: UserACL): Future[FeatureView]

  /** Deletes the given Feature, if present */
  def delete(request: DeleteFeatureRequest)(implicit traceId: TraceId, user: UserACL): Future[Boolean]
}

object FeatureService {
  class FeatureServiceImpl(repository: FeatureRepository)
    (implicit executionContext: ExecutionContext) extends FeatureService with TraceLogging {
    /** Returns the Feature by given search parameters, if present */
    override def get(request: GetFeatureRequest)(implicit traceId: TraceId, user: UserACL): Future[FeatureView] = {
      val availableKeys = FeatureCapabilities.getAvailableAccessKeysForCapabilities(FeatureCapabilities.ViewCapabilities, user)
      repository
        .get(request.id, availableKeys).getOrNotFound.map(_.view)
    }

    /** Returns the Feature by given search parameters, if present */
    override def getAll()(implicit traceId: TraceId, user: UserACL): Future[Seq[FeatureView]] = {
      val availableKeys = FeatureCapabilities.getAvailableAccessKeysForCapabilities(FeatureCapabilities.ViewCapabilities, user)
      repository.getAll(availableKeys).map(_.map(feature => feature.view))
    }

    /** Inserts the Feature */
    override def create(request: UpsertFeatureRequest)
      (implicit traceId: TraceId, user: UserACL): Future[FeatureView] = {
      val availableKeys = FeatureCapabilities.getAvailableAccessKeysForCapabilities(FeatureCapabilities.EditCapabilities, user)
      if (availableKeys.nonEmpty) {
        val feature = Feature(
          id = UUID.randomUUID.toString,
          title = request.title,
          description = request.description,
          assetClass = request.assetClass,
          supportContact = request.supportContact,
          capabilities = request.capabilities,
          createdOn = java.time.Instant.now(),
          createdBy = user.userId,
          updatedOn = None,
          updatedBy = None,
          acceptedAccessKeys = FeatureCapabilities.capabilityToAvailableKeyBuilders.keys.toSet
        )
        log.info("Creating a new Feature", feature)
        repository.createFeature(feature, availableKeys).map(_.view)
      } else {
        Future.failed(HttpError.unauthorized())
      }
    }

    /** Updates the Feature */
    override def update(request: UpsertFeatureRequest, id: String)
      (implicit traceId: TraceId, user: UserACL): Future[FeatureView] = {
      val availableKeys = FeatureCapabilities.getAvailableAccessKeysForCapabilities(FeatureCapabilities.EditCapabilities, user)
      repository.
        get(id, availableKeys)
        .flatMap {
          case None =>
            Future.failed(HttpError.notFound(s"$id not found"))
          case Some(feature) =>
            val updated = feature.copy(
              title = request.title,
              description = request.description,
              assetClass = request.assetClass,
              supportContact = request.supportContact,
              capabilities = request.capabilities,
              updatedOn = Some(java.time.Instant.now()),
              updatedBy = Some(user.userId),
            )
            log.info("Updating the Feature", feature)
            repository.update(updated, availableKeys).getOrNotFound.refineDuplicateKey
        }
        .map(feature => feature.view)
    }


    /** Deletes the given Feature, if present */
    override def delete(request: DeleteFeatureRequest)(implicit traceId: TraceId, user: UserACL): Future[Boolean] = {
      val availableKeys = FeatureCapabilities.getAvailableAccessKeysForCapabilities(FeatureCapabilities.EditCapabilities, user)
      repository
        .delete(request.id, availableKeys)
        .flatMap { result =>
          if (result) Future.successful(result)
          else Future.failed(HttpError.notFound(s"${request.id} not found"))
        }
    }
  }

  implicit class FutureOptOps[A](val future: Future[Option[A]]) extends AnyVal {
    /** Returns the flattened future, or NotFound if the value is empty */
    def getOrNotFound(implicit ec: ExecutionContext): Future[A] = {
      future.flatMap { opt =>
        opt.map { a =>
          Future.successful(a)
        }.getOrElse(
          Future.failed(HttpError.notFound("Feature not found"))
        )
      }
    }
  }
}