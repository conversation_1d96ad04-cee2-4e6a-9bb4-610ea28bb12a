package com.simonmarkets.productfeaturesets.model

import com.simonmarkets.entitlements.AcceptedAccessKeysGenerator
import com.simonmarkets.productfeaturesets.access.FeatureKeysGenerator
import com.simonmarkets.productfeaturesets.model.view.FeatureView
import com.simonmarkets.resteasy.utils.SimonCodecs
import com.simonmarkets.utils.data.mongo.model.SimonResourceCompanion
import org.bson.codecs.configuration.CodecProvider
import org.mongodb.scala.bson.codecs.Macros._

import java.time.Instant

object data {
  case class Feature(
      id: String,
      title: String,
      description: String,
      assetClass: String,
      supportContact: String,
      capabilities: Set[String],
      createdOn: Instant,
      createdBy: String,
      updatedOn: Option[Instant],
      updatedBy: Option[String],
      acceptedAccessKeys: Set[String] = Set.empty[String]
  ) {
    def view: FeatureView = FeatureView(id, title, description, assetClass, supportContact, capabilities, createdOn, createdBy, updatedOn, updatedBy)
  }

  object Feature extends SimonResourceCompanion[Feature] {
    override def collectionName: String = "simonOnboarding.feature"

    override def resourceName: String = "Feature"

    override def accessKeyGenerator: AcceptedAccessKeysGenerator[Feature] = FeatureKeysGenerator

    override def codecProviders: List[CodecProvider] = List(classOf[Feature], SimonCodecs.SimonIdProvider)
  }
}