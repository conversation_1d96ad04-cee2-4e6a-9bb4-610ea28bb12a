package com.simonmarkets.productfeaturesets

import com.simonmarkets.productfeaturesets.config.AppConfiguration
import com.simonmarkets.productfeaturesets.config.AppConfiguration._
import com.simonmarkets.productfeaturesets.di.ServiceLocator
import com.simonmarkets.productfeaturesets.routes.ProductFeatureSetsRoutes
import com.simonmarkets.resteasy.framework.RestEasyModule.RestEasyLambda
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import io.simon.openapi.generator.OpenApiGenerator
import pureconfig.generic.auto._

object ProductFeatureSetsApp extends RestEasyModule[AppConfiguration] with App {
  /** Returns the service name, for example, "FlawlessGems". Used for logging purposes.
   * The "service-" prefix is appended automatically. */
  override def serviceName: String = "ProductFeatureSets"

  /** Returns the root path to all of the service endpoints.
   * For example, "/simon/api/v1/lorem" */
  override def servicePath: String = "simon/api/v1/feature-sets"

  /** Is executed when the service starts up */
  override def init(environment: Environment): Unit = {
    val locator = new ServiceLocator(config)
    val routes = new ProductFeatureSetsRoutes(locator.authDirective, locator.featureService).routes
    environment.addRoutes(routes)
  }

  OpenApiGenerator.generateOpenApiDocumentation()
  RestEasy.start()

  /** Wraps the module into aws lambda */
  object ProductFeatureSetsLambda extends RestEasyLambda
}