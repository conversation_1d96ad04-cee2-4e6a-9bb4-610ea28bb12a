package com.simonmarkets.productfeaturesets.service

import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.productfeaturesets.helpers.{FeatureProviderHelper, UserProviderHelper}
import com.simonmarkets.productfeaturesets.model.data.Feature
import com.simonmarkets.productfeaturesets.model.requests.{DeleteFeatureRequest, GetFeatureRequest, UpsertFeatureRequest}
import com.simonmarkets.productfeaturesets.repository.FeatureRepository
import com.simonmarkets.utils.data.core.context.RepositoryContext
import org.mockito.ArgumentMatchers.{any, eq => meq}
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}

import scala.concurrent.Future

class FeatureServiceSpec
  extends AsyncWordSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfterEach
    with UserProviderHelper
    with FeatureProviderHelper {

  val mockRepo: FeatureRepository = mock[FeatureRepository]
  val service = new FeatureService.FeatureServiceImpl(mockRepo)

  val traceId: TraceId = TraceId("FeatureServiceSpec")
  implicit val token: String = "AuthToken"

  override def afterEach(): Unit = {
    reset(mockRepo)
    super.afterEach()
  }

  "FeatureServiceImpl" when {
    "get is invoked" should {
      "Return the Feature by given search parameters" in {
        when(mockRepo.get(any[String], any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(Some(activeViewFeature)))

        val request = GetFeatureRequest(activeViewFeature.id)

        val future = service.get(request)(traceId, viewACL)
        future.map(result => result shouldBe activeViewFeature.view)
      }

      "Not return due to wrong capabilities" in {
        when(mockRepo.get(any[String], any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(None))

        val request = GetFeatureRequest(activeEditFeature.id)

        val future = service.get(request)(traceId, badACL)
        future.failed.map { fail =>
          fail shouldBe HttpError.notFound("Feature not found")
        }
      }
    }

    "getAll is invoked" should {
      "Return the Feature by given search parameters" in {
        when(mockRepo.getAll(any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(Seq(activeViewFeature)))

        val future = service.getAll()(traceId, viewACL)
        future.map(result => result shouldBe Seq(activeViewFeature.view))
      }

      "Not return due to wrong capabilities" in {
        when(mockRepo.getAll(any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(Seq()))
        val future = service.getAll()(traceId, viewACL)

        future.map(result => result shouldBe empty)
      }
    }

    "create is invoke" should {
      val request: UpsertFeatureRequest =
        UpsertFeatureRequest(
          title = activeViewFeature.title,
          description = activeViewFeature.description,
          assetClass = activeViewFeature.assetClass,
          supportContact = activeViewFeature.supportContact,
          capabilities = activeViewFeature.capabilities
        )
      "Create a new feature" in {
        when(mockRepo.createFeature(any[Feature], any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(activeViewFeature))

        val future = service.create(request)(traceId, editACL)
        future.map(result => result shouldBe activeViewFeature.view)
      }

      "Fail to create a new feature if already exists" in {
        val error = HttpError.conflict("A Valid Feature with the provided fields already exists.")
        when(mockRepo.createFeature(any[Feature], any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.failed(error))

        val future = service.create(request)(traceId, editACL)
        future.failed.map { fail =>
          fail shouldBe error
        }
      }

      "Fail when the user doesn't have the necessary capabilities" in {
        val future = service.create(request)(traceId, viewACL)
        future.failed.map { fail =>
          fail shouldBe HttpError.unauthorized()
        }
      }
    }

    "Update is invoke" should {
      "Update a feature" in {
        val request: UpsertFeatureRequest =
          UpsertFeatureRequest(
            title = activeViewFeature.title,
            description = activeViewFeature.description,
            assetClass = activeViewFeature.assetClass,
            supportContact = activeViewFeature.supportContact,
            capabilities = activeViewFeature.capabilities
          )

        when(mockRepo.get(any[String], any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(Some(activeViewFeature)))

        val expected = activeViewFeature.copy(description = "UPDATED")
        when(mockRepo.update(any[Feature], any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(Some(expected)))

        val future = service.update(request, activeViewFeature.id)(traceId, editACL)
        future.map(result => result shouldBe expected.view)
      }

      "Fail when the user doesn't have the necessary capabilities" in {
        val request: UpsertFeatureRequest =
          UpsertFeatureRequest(
            title = activeEditFeature.title,
            description = activeEditFeature.description,
            assetClass = activeEditFeature.assetClass,
            supportContact = activeEditFeature.supportContact,
            capabilities = activeEditFeature.capabilities
          )

        when(mockRepo.get(any[String], any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(None))

        val future = service.update(request, activeEditFeature.id)(traceId, viewACL)
        future.failed.map { fail =>
          fail shouldBe HttpError.notFound(s"${activeEditFeature.id} not found")
        }
      }
    }

    "Delete is invoked" should {
      "delete a feautre" in {
        val request: DeleteFeatureRequest = DeleteFeatureRequest(activeViewFeature.id)
        when(mockRepo.delete(meq(request.id), any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(true))

        val future = service.delete(request)(traceId, editACL)

        future.map(result => result shouldBe true)
      }

      "Fail when the user doesn't have the necessary capabilities or not found" in {
        val request: DeleteFeatureRequest = DeleteFeatureRequest(activeEditFeature.id)
        when(mockRepo.delete(any[String], any[Set[String]])(any[RepositoryContext]))
          .thenReturn(Future.successful(false))

        val future = service.delete(request)(traceId, viewACL)
        future.failed.map { fail =>
          fail shouldBe HttpError.notFound(s"${activeEditFeature.id} not found")
        }
      }
    }
  }

}
