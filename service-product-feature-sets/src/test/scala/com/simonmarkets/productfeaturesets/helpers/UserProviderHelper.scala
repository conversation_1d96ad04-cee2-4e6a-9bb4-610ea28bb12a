package com.simonmarkets.productfeaturesets.helpers

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.Admin
import com.simonmarkets.capabilities.FeatureCapabilities
import simon.Id.NetworkId

import java.time.LocalDateTime

trait UserProviderHelper {

  def userFromParams(userId: String, firstName: String, lastName: String): UserACL =
    UserACL(
      userId = userId,
      networkId = NetworkId("network"),
      lastVisitedAt = Some(LocalDateTime.now),
      email = "<EMAIL>",
      firstName = firstName,
      lastName = lastName,
      distributorId = None,
      omsId = None,
      tradewebEligible = false,
      regSEligible = false,
      isActive = Some(true)
    )


  lazy val user: UserACL = userFromParams("user1", "Hello", "World")

  lazy val adminACL: UserACL = user.copy(networkId = NetworkId("SIMON Admin"), capabilities = Set(Admin))
  lazy val viewACL: UserACL = user.copy(networkId = NetworkId("NON SIMON Admin"), capabilities = Set(FeatureCapabilities.ViewFeatureViaNetwork))
  lazy val editACL: UserACL = user.copy(networkId = NetworkId("NON SIMON Admin"), capabilities = Set(FeatureCapabilities.EditFeatureViaNetwork))
  lazy val badACL: UserACL = user.copy(networkId = NetworkId("NON SIMON Admin"), capabilities = Set())

}

object UserProviderHelper extends UserProviderHelper