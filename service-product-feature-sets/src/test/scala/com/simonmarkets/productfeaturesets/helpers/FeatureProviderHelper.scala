package com.simonmarkets.productfeaturesets.helpers

import com.simonmarkets.capabilities.FeatureCapabilities
import com.simonmarkets.productfeaturesets.model.data.Feature

import java.time.Instant

import scala.util.Random

trait FeatureProviderHelper {

  def createFeature(id: String, acceptedAccessKeys: Set[String]): Feature =
    Feature(id = id,
      title = Random.nextString(10),
      description = "FooBar",
      assetClass = "Annuities",
      supportContact = "<EMAIL>",
      capabilities = Set("Foo", "Bar", Random.nextString(10)),
      createdOn = Instant.now(),
      createdBy = "Test",
      updatedOn = None,
      updatedBy = None,
      acceptedAccessKeys = acceptedAccessKeys)


  val activeViewFeature = createFeature(
    id = "activeViewFeature",
    acceptedAccessKeys = FeatureCapabilities.ViewCapabilities)

  val inactiveViewFeature = createFeature(
    id = "inactiveViewFeature",
    acceptedAccessKeys = FeatureCapabilities.ViewCapabilities)


  val activeEditFeature = createFeature(
    id = "activeViewFeature",
    acceptedAccessKeys = FeatureCapabilities.EditCapabilities)

}
