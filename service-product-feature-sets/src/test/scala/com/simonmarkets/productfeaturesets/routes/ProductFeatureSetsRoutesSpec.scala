package com.simonmarkets.productfeaturesets.routes

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.server.Route
import akka.http.scaladsl.testkit.ScalatestRouteTest
import com.goldmansachs.marquee.pipg.service.user.TestUserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.logging.TraceId
import com.simonmarkets.productfeaturesets.helpers.FeatureProviderHelper
import com.simonmarkets.productfeaturesets.model.requests.{DeleteFeatureRequest, GetFeatureRequest, UpsertFeatureRequest}
import com.simonmarkets.productfeaturesets.model.view
import com.simonmarkets.productfeaturesets.model.view.FeatureView
import com.simonmarkets.productfeaturesets.service.FeatureService
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.resteasy.authn.User
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{reset, when}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import simon.Id.NetworkId

import scala.concurrent.Future

class ProductFeatureSetsRoutesSpec extends WordSpec
  with MockitoSugar
  with Matchers
  with ScalatestRouteTest
  with DirectivesWithCirce
  with BeforeAndAfterEach
  with JsonCodecs
  with FeatureProviderHelper {

  private val V1_FEATURE_SETS = "/simon/api/v1/feature-sets"

  private val traceId: TraceId = TraceId.randomize
  private val userId = "userId"
  private val networkId = NetworkId("networkId")
  private val userAcl = TestUserACL(userId, networkId)
  private val user = mock[User]

  private val mockAuthorizedDirectives = mock[UserAclAuthorizedDirective]
  when(user.token) thenReturn "AuthToken"
  when(mockAuthorizedDirectives.authorizedUser()) thenReturn tprovide((traceId, user, userAcl))
  when(mockAuthorizedDirectives.authorized()) thenReturn tprovide((traceId, userAcl))
  when(mockAuthorizedDirectives.unauthenticated) thenReturn provide(traceId)

  private val mockService: FeatureService = mock[FeatureService]

  private val defaultMsg = "Server error while processing the request. Please contact SIMON Engineering team."
  val notFound = "Feature-set not found"

  private def expectedMsg(msg: String) = s"""{"messages":["$msg"]}"""

  override def afterEach(): Unit = {
    reset(mockService)
    super.afterEach()
  }

  private def routes: Route =
    new ProductFeatureSetsRoutes(mockAuthorizedDirectives, mockService)
      .routes

  "ProductFeatureSetsRoutes" should {
    "handle GET /feature-sets" when {
      "No param is provided" in {
        val expected: Seq[FeatureView] = Seq(activeViewFeature.view, activeViewFeature.view)
        when(mockService.getAll()(any[TraceId], ArgumentMatchers.eq(userAcl))).thenReturn(Future.successful(expected))

        Get(V1_FEATURE_SETS) ~> Route.seal(routes) ~> check {
          status shouldEqual StatusCodes.OK
          responseAs[Seq[FeatureView]] shouldBe expected
        }
      }
    }
    "handle POST /feature-sets" when {
      "service successfully creates the feature-set" in {
        val request: UpsertFeatureRequest =
          UpsertFeatureRequest(title = activeViewFeature.title,
            description = activeViewFeature.description,
            assetClass = activeViewFeature.assetClass,
            supportContact = activeViewFeature.supportContact,
            capabilities = activeViewFeature.capabilities)

        val expected: FeatureView = activeViewFeature.view
        when(mockService.create(ArgumentMatchers.eq(request))(any[TraceId], ArgumentMatchers.eq(userAcl)))
          .thenReturn(Future.successful(expected))

        Post(V1_FEATURE_SETS, request) ~> Route.seal(routes) ~> check {
          status shouldEqual StatusCodes.OK
          responseAs[FeatureView] shouldEqual expected
        }
      }

      "service fails to creates the feature-set" in {
        val request: UpsertFeatureRequest =
          UpsertFeatureRequest(title = activeViewFeature.title,
            description = activeViewFeature.description,
            assetClass = activeViewFeature.assetClass,
            supportContact = activeViewFeature.supportContact,
            capabilities = activeViewFeature.capabilities)

        when(mockService.create(ArgumentMatchers.eq(request))(any[TraceId], ArgumentMatchers.eq(userAcl)))
          .thenReturn(Future.failed(new RuntimeException()))

        Post(V1_FEATURE_SETS, request) ~> Route.seal(routes) ~> check {
          status shouldEqual StatusCodes.InternalServerError
          responseAs[String] shouldEqual expectedMsg(defaultMsg)
        }
      }
    }

    "handle GET /feature-sets/:id" when {
      "id is provided" in {
        val expected: FeatureView = activeViewFeature.view
        when(mockService.get(any[GetFeatureRequest])(any[TraceId], ArgumentMatchers.eq(userAcl)))
          .thenReturn(Future.successful(expected))

        Get(V1_FEATURE_SETS + s"/${activeViewFeature.id}") ~> Route.seal(routes) ~> check {
          status shouldEqual StatusCodes.OK
          responseAs[view.FeatureView] shouldBe expected
        }
      }

      "invalid id is provided" in {
        when(mockService.get(any[GetFeatureRequest])(any[TraceId], ArgumentMatchers.eq(userAcl)))
          .thenReturn(Future.failed(new IllegalArgumentException(notFound)))

        Get(V1_FEATURE_SETS + s"/${activeViewFeature.id}") ~> Route.seal(routes) ~> check {
          status shouldEqual StatusCodes.NotFound
          responseAs[String] shouldBe expectedMsg(notFound)
        }
      }
    }

    "handle PUT /feature-sets/:id" when {
      val request: UpsertFeatureRequest =
        UpsertFeatureRequest(title = activeViewFeature.title,
          description = "UPDATED",
          assetClass = activeViewFeature.assetClass,
          supportContact = activeViewFeature.supportContact,
          capabilities = activeViewFeature.capabilities)

      "id is provided" in {
        val expected: FeatureView = activeViewFeature.view
        when(mockService.update(ArgumentMatchers.eq(request), ArgumentMatchers.eq(activeViewFeature.id))(any[TraceId], ArgumentMatchers.eq(userAcl)))
          .thenReturn(Future.successful(expected))

        Put(V1_FEATURE_SETS + s"/${activeViewFeature.id}", request) ~> Route.seal(routes) ~> check {
          status shouldEqual StatusCodes.OK
          responseAs[view.FeatureView] shouldBe expected
        }
      }

      "invalid id is provided" in {
        when(mockService.update(ArgumentMatchers.eq(request), any[String])(any[TraceId], ArgumentMatchers.eq(userAcl)))
          .thenReturn(Future.failed(new IllegalArgumentException(notFound)))

        Put(V1_FEATURE_SETS + s"/INVALID", request) ~> Route.seal(routes) ~> check {
          status shouldEqual StatusCodes.NotFound
          responseAs[String] shouldBe expectedMsg(notFound)
        }
      }
    }

    "handle DELETE /feature-sets/:id" when {
      "id is provided" in {
        val expected = true
        when(mockService.delete(any[DeleteFeatureRequest])(any[TraceId], ArgumentMatchers.eq(userAcl)))
          .thenReturn(Future.successful(expected))

        Delete(V1_FEATURE_SETS + s"/${activeViewFeature.id}") ~> Route.seal(routes) ~> check {
          status shouldEqual StatusCodes.OK
          responseAs[Boolean] shouldBe expected
        }
      }

      "invalid id is provided" in {
        when(mockService.delete(any[DeleteFeatureRequest])(any[TraceId], ArgumentMatchers.eq(userAcl)))
          .thenReturn(Future.failed(new IllegalArgumentException(notFound)))

        Delete(V1_FEATURE_SETS + s"/${activeViewFeature.id}") ~> Route.seal(routes) ~> check {
          status shouldEqual StatusCodes.NotFound
          responseAs[String] shouldBe expectedMsg(notFound)
        }
      }
    }
  }
}
