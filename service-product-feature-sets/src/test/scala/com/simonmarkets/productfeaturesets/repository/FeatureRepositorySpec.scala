package com.simonmarkets.productfeaturesets.repository

import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.error.Conflict
import com.simonmarkets.mongodb.snapshots.SnapshotableRepository.RecoverFuture
import com.simonmarkets.productfeaturesets.helpers.FeatureProviderHelper
import com.simonmarkets.productfeaturesets.model.data.Feature
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import com.simonmarkets.utils.data.mongo.ops.RegularMongoGenericRepositoryOps._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.model.{IndexModel, IndexOptions, Indexes}
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext.Implicits.global


class FeatureRepositorySpec extends WordSpec with MockitoSugar with EmbeddedMongoLike with Matchers with BeforeAndAfterEach with FeatureProviderHelper {

  implicit lazy val dbCtx: DatabaseContext = DatabaseContext(db, client)
  private lazy val repository = new FeatureRepository.FeatureRepositoryMongo()
  lazy val collection: MongoCollection[Feature] = repository.mongoOps.getCollection

  private implicit val traceId: TraceId = TraceId("mongo-resource-test")

  private val adminAvailableAccessKeys = Set(Capabilities.Admin)
  private val notAdminAvailableAccessKeys = Set(Capabilities.DefaultCapability)

  private val uniqueOption = new IndexOptions().unique(true)

  override def beforeEach(): Unit = {
    super.beforeEach()
    collection.drop().toFuture().await
    collection.createIndexes(Seq(
      IndexModel(Indexes.ascending("id"), uniqueOption),
      IndexModel(Indexes.ascending("title"), uniqueOption)
    )).toFuture().await
  }

  "FeatureRepository" can {
    "getValidFeatures" should {
      "find features by accessKeys" in {
        collection.insertMany(Seq(activeViewFeature)).toFuture.await()
        val res = repository.getAll(adminAvailableAccessKeys).await()
        res.nonEmpty shouldBe true
      }

      "fail to find features by accessKeys" in {
        collection.insertMany(Seq(activeViewFeature)).toFuture.await()
        val res = repository.getAll(notAdminAvailableAccessKeys).await()
        res.isEmpty shouldBe true
      }
    }

    "get" should {
      "find features by id and accessKeys" in {
        collection.insertMany(Seq(activeViewFeature)).toFuture.await()
        val res = repository.get(activeViewFeature.id, adminAvailableAccessKeys).await()
        res.nonEmpty shouldBe true
      }

      "not find features by wrong id" in {
        collection.insertMany(Seq(activeViewFeature)).toFuture.await()
        val res = repository.get("wrong", adminAvailableAccessKeys).await()
        res.isEmpty shouldBe true
      }

      "fail to find features by accessKeys" in {
        collection.insertMany(Seq(activeViewFeature)).toFuture.await()
        val res = repository.get(activeViewFeature.id, notAdminAvailableAccessKeys).await()
        res.isEmpty shouldBe true
      }

    }

    "create" should {

      "fail with conflict on duplicate key" in {
        collection.insertOne(activeViewFeature).toFuture.await()
        val res2 = repository.createFeature(activeViewFeature, adminAvailableAccessKeys)
        a[Conflict] shouldBe thrownBy(res2.await)
      }

    }

    "update" should {

      "fail with conflict on duplicate key" in {
        collection.insertMany(Seq(activeViewFeature, inactiveViewFeature)).toFuture.await()
        val conflictingDoc = activeViewFeature.copy(title = inactiveViewFeature.title)
        val res2 = repository.update(conflictingDoc, adminAvailableAccessKeys).refineDuplicateKey
        a[Conflict] shouldBe thrownBy(res2.await)
      }

    }
  }
}
