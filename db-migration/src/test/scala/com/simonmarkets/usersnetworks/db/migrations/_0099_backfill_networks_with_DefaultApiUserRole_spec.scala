package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.Document
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}
import com.simonmarkets.syntax._

import scala.concurrent.ExecutionContext

class _0099_backfill_networks_with_DefaultApiUserRole_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  val existingRole: CustomRoleDefinition = CustomRoleDefinition("DoNotTouch", Set("cap1", "cap2"))
  val expectedAddedRole: CustomRoleDefinition = CustomRoleDefinition("DefaultApiUserRole", Set.empty[String])

  private val testNetwork = Network(
    id = simon.Id.NetworkId("networkId"),
    name = "Test Network",
    networkCode = "BLAH",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(1, "test"),
    customRolesConfig = Set(existingRole)
  )

  lazy val networks: MongoCollection[Document] = db.getCollection[Document]("networks_new")

  override def beforeEach(): Unit = (for {
    _ <- networks.drop()
  } yield()).toFuture().await

  "_0099_backfill_networks_with_DefaultApiUserRole" should {
    lazy val migration = new _0099_backfill_networks_with_DefaultApiUserRole(db)

    "add DefaultApiUserRole to networks" in {
      val doc = NetworkFormat.write(testNetwork)

      testNetwork.customRolesConfig.map(_.role) should not contain "DefaultApiUserRole"

      for {
        _ <- networks.insertOne(doc).toFuture
        _ <- migration.apply
        updatedNetwork <- networks.find.head.map(NetworkFormat.read)
      } yield updatedNetwork.customRolesConfig.map(_.role) should contain("DefaultApiUserRole")
    }
  }

}
