package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.Capabilities.AdminCapability
import com.simonmarkets.capabilities.OrdersCapabilities.{CancelOrderOnBehalfOfViaSystemUserCapability, CancelOrderViaOwnerCapability, EditOrderViaOwnerCapability}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0089_add_cancelAfterBooksClose_capabilitySpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val addCapabilities = Set("cancelOrderAfterBooksClose")

  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, Set(CancelOrderViaOwnerCapability.name))

  val testAnotherManager: CustomRoleDefinition = CustomRoleDefinition(
    "anotherManager",
    Set(CancelOrderOnBehalfOfViaSystemUserCapability.name, CancelOrderViaOwnerCapability.name, AdminCapability.name))

  lazy val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix,
    Set(AdminCapability.name, EditOrderViaOwnerCapability.name))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(1, "test"),
    customRolesConfig = Set(testFaManager, testFa)
  )

  private val testNetwork2 = testNetwork1.copy(customRolesConfig = Set(testAnotherManager, testFa))

  override def beforeEach(): Unit = helper.beforeEachHelper()

  "_0089_add_cancelAfterBooksClose_capabilitySpec" should {

    "ensure cancel capability is added where needed" in {
      val docs = Set(testNetwork1, testNetwork2).map(NetworkFormat.write).toList
      helper.networksCol.insertMany(docs).toFuture.await
      helper.verifyCapabilityAbsent(Set(testNetwork1, testNetwork2), addCapabilities)

      val migration = new _0089_add_cancelAfterBooksClose_capability(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(
        Seq((testNetwork1, UserRole.EqPIPGFA.productPrefix), (testNetwork2, UserRole.EqPIPGFA.productPrefix)),
        addCapabilities)
      helper.verifyCapabilityPresent(
        Seq((testNetwork1, UserRole.EqPIPGFAManager.productPrefix), (testNetwork2, "anotherManager")),
        addCapabilities)
    }
  }
}
