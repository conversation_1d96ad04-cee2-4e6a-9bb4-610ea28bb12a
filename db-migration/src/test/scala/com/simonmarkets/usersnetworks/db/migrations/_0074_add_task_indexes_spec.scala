package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0074_add_task_indexes_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0074_add_task_indexes_spec" should {

    lazy val tasks = db.getCollection("users.tasks")
    lazy val inputs = db.getCollection("users.tasks.inputs")
    lazy val outputs = db.getCollection("users.tasks.outputs")

    "create task indexes" in {

      lazy val migration = new _0074_add_task_indexes(db)
      migration.apply.await

      whenReady(tasks.getIndexes) { indexes =>
        indexes should contain(Index("id_1_entitlements_1", List(IndexedField("id"), IndexedField("entitlements"))))
      }

      whenReady(inputs.getIndexes) { indexes =>
        indexes should contain(Index("taskId_1_row_1", List(IndexedField("taskId"), IndexedField("row"))))
      }

      whenReady(outputs.getIndexes) { indexes =>
        indexes should contain(Index("taskId_1_row_1", List(IndexedField("taskId"), IndexedField("row"))))
      }
    }
  }
}
