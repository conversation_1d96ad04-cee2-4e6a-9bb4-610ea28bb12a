package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0063_create_index_on_userId_for_userPaidFeatures_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0063_create_index_on_userId_for_userPaidFeatures" should {

    lazy val col = db.getCollection("users.paidFeatures")

    "create indexes" in {

      val migration = new _0063_create_index_on_userId_for_userPaidFeatures(db)
      migration.apply.await()

      whenReady(col.getIndexes) { indexes =>
        indexes should contain(Index("userId_1", List(IndexedField("userId")), unique = true))
      }
    }
  }
}
