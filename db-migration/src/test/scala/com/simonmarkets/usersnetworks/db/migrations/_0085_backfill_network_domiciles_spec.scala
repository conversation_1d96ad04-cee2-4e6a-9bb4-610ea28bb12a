package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.enums.DomicileCode
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.usersnetworks.db.migrations.utils.TestNetwork
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0085_backfill_network_domiciles_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val testNetwork1 = TestNetwork(
    id = "Test Network 1"
  )
  private val domiciledNetwork1 = testNetwork1.copy(
    id = NetworkId("Network 1"),
    domiciles = Some(Set(DomicileCode.US))
  )
  private val domiciledNetwork2 = testNetwork1.copy(
    id = NetworkId("Network 2"),
    domiciles = Some(Set(DomicileCode.US, DomicileCode.CA))
  )
  private val nonDomiciledNetwork1 = testNetwork1.copy(
    id = NetworkId("Network 3"),
  )
  private val nonDomiciledNetwork2 = testNetwork1.copy(
    id = NetworkId("Network 4"),
    domiciles = None
  )

  "_0085_backfill_network_domiciles" should {

    "only update networks with empty domiciles" in {

      val migration = new _0085_backfill_network_domiciles(db)

      val networkInserts: Seq[Document] = Seq(domiciledNetwork1, domiciledNetwork2, nonDomiciledNetwork1, nonDomiciledNetwork2).map(NetworkFormat.write)
      migration.networksColl.insertMany(networkInserts).toFuture().await

      migration.apply.await

      whenReady(migration.networksColl.find().toFuture) { n =>

        val migratedNetworks = n.map(NetworkFormat.read)
        val expectedNoChangeNetworks = Seq(
          domiciledNetwork1,
          domiciledNetwork2
        )
        val expectedUpdatedNetworks = Seq(
          nonDomiciledNetwork1.copy(domiciles = Some(Set(DomicileCode.US))),
          nonDomiciledNetwork2.copy(domiciles = Some(Set(DomicileCode.US)))
        )
        val updatedNetworks = migratedNetworks.filter(network => expectedUpdatedNetworks.contains(network))
        updatedNetworks should contain theSameElementsAs expectedUpdatedNetworks

        val noChangeNetworks = migratedNetworks.filter(network => expectedNoChangeNetworks.contains(network))
        noChangeNetworks should contain theSameElementsAs expectedNoChangeNetworks
      }
    }
  }
}
