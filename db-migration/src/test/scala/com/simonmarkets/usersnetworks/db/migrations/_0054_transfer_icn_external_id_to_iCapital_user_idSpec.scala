package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.FutureBlock
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId


import scala.concurrent.ExecutionContext

class _0054_transfer_icn_external_id_to_iCapital_user_idSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val networkId = NetworkId("networkId")

  //dummy data
  private val user1 = TestUser.apply(
    id = "id_1",
    networkId = networkId,
    externalIds = Seq(ExternalId("icn", "5235"))
  )

  val dummyExternal = ExternalId("some","id")
  private val user2 = TestUser.apply(
    id = "id_2",
    networkId = networkId,
    externalIds = Seq(ExternalId("icn", "5236"), dummyExternal)
  )

  private val user3 = TestUser.apply(
    id = "id_2",
    networkId = networkId,
    externalIds = Seq(dummyExternal)
  )



  "_0054_transfer_icn_external_id_to_iCapital_user_id" should {

    "transfer icn external ids to iCapitalUserId field" in {

      val migration = new _0054_transfer_icn_external_id_to_iCapital_user_id(db)

      val userInserts: Seq[Document] = Seq(user1, user2, user3).map(UserFormat.write)
      migration.usersColl.insertMany(userInserts).toFuture().await()

      migration.apply.await()

      whenReady(migration.usersColl.find().toFuture) { userDocs =>

        val migratedUsers = userDocs.map(UserFormat.read)
        val expectedUsers = Seq(
          user1.copy(externalIds = Seq.empty, iCapitalUserId = Some("5235")),
          user2.copy(externalIds = Seq(dummyExternal), iCapitalUserId = Some("5236")),
          user3
        )

        migratedUsers.map(TestUser.cleanDates) should contain theSameElementsAs expectedUsers.map(TestUser.cleanDates)
      }
    }

  }

}
