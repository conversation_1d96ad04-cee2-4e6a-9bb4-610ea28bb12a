package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0060_feature_sets_index_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0060_feature_sets_index" should {

    lazy val col = db.getCollection("simonOnboarding.feature")

    "create indexes" in {

      val migration = new _0060_feature_sets_index(db)
      migration.apply.await()

      whenReady(col.getIndexes) { indexes =>
        indexes should contain allElementsOf List(
          Index("id_1", List(IndexedField("id")), unique = true),
          Index("title_1", List(IndexedField("title")), unique = true)
        )
      }
    }
  }
}
