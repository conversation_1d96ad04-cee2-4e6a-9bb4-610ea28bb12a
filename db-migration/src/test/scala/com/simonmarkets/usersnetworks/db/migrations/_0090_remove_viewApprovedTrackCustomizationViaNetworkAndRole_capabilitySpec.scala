package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.syntax._
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0090_remove_viewApprovedTrackCustomizationViaNetworkAndRole_capabilitySpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val removeCapabilities: Set[String] = Set("viewApprovedTrackCustomizationViaNetworkAndRole")

  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, removeCapabilities + "famanagerCapability")

  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, removeCapabilities + "faCapability")

  private val testnetwork1 = Network(
    id = simon.Id.NetworkId("network-id-1"),
    name = "network-name-1",
    networkCode = "network-1",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(1, "network-test"),
    customRolesConfig = Set(testFaManager, testFa)
  )

  override def beforeEach(): Unit = helper.beforeEachHelper()

  "_0090_remove_viewApprovedTrackCustomizationViaNetworkAndRole_capability" should {

    "remove viewApprovedTrackCustomizationViaNetworkAndRole capability" in {
      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture.await
      helper.verifyCapabilityPresent(Set(testnetwork1), removeCapabilities)

      val migration = new _0090_remove_viewApprovedTrackCustomizationViaNetworkAndRole_capability(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(Set(testnetwork1), removeCapabilities)
    }
  }
}
