package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0070_create_index_on_id_and_idType_for_paidFeatures_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0070_create_index_on_id_and_idType_for_paidFeatures_spec" should {

    lazy val col = db.getCollection("users.paidFeatures")

    "create indexes" in {

      val migration = new _0070_create_index_on_id_and_idType_for_paidFeatures(db)
      migration.apply.await()

      whenReady(col.getIndexes) { indexes =>
        indexes should contain(Index("id_1_idType_1", List(IndexedField("id"), IndexedField("idType")), unique = true))
      }
    }
  }
}