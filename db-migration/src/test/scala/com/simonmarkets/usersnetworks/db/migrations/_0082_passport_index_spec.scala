package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext


class _0082_passport_index_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0082_passport_index" should {

    "create indexes" in {

      val migration = new _0082_passport_index(db)
      migration.apply.await()

      whenReady(migration.col.getIndexes) { indexes =>
        indexes should contain(Index("code_1", List(IndexedField("code")), unique = true))
      }
    }
  }
}
