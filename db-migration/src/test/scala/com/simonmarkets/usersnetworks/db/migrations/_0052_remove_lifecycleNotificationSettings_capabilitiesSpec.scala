package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0052_remove_lifecycleNotificationSettings_capabilitiesSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val removeCapabilities = Set("viewLifecycleNotificationSettingsViaOwner", "editLifecycleNotificationSettingsViaNetwork",
    "editLifecycleNotificationSettingsViaOwner", "viewLifecycleNotificationSettingsViaNetwork")

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, removeCapabilities + "famanager")
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, removeCapabilities + "fa")

  private val testnetwork1 = Network(
    id = simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFaManager, testFa)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0052_remove_lifecycleNotificationSettings_capabilities" should {
    "remove unused lifecycle notification capabilities" in {

      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testnetwork1, UserRole.EqPIPGFAManager.productPrefix)), removeCapabilities)

      val migration = new _0052_remove_lifecycleNotificationSettings_capabilities(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(Seq((testnetwork1, UserRole.EqPIPGFAManager.productPrefix)), removeCapabilities)
      helper.verifyCapabilityPresent(Seq((testnetwork1, UserRole.EqPIPGFAManager.productPrefix)), Set("famanager"))
      helper.verifyCapabilityAbsent(Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix)), removeCapabilities)
      helper.verifyCapabilityPresent(Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix)), Set("fa"))
    }
  }
}
