package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, NetworkType, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.PortfolioCapabilities.{EditPortfolioViaOwner, ViewPortfolioViaOwner, ViewPortfolioViaPublic}
import com.simonmarkets.capabilities.SpectrumLearningCenterCapabilities.ViewSpectrumLearningCenter
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import com.simonmarkets.syntax._

import scala.concurrent.ExecutionContext

class _0024_add_ViewPortfolioViaPublic_to_spectrum_usersSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach{
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val issuerWithViewSpectrum: CustomRoleDefinition = CustomRoleDefinition("Issuer", Set(ViewSpectrumLearningCenter))
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("someOtherCapability"))


  private val testnetwork1 = Network(
    id =  simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    networkTypes = Some(List(NetworkType.Issuer)),
    customRolesConfig = Set(issuerWithViewSpectrum, testFa)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0024_add_ViewPortfolioViaPublic_to_spectrum_users" should {
    "add desired capabilities to roles with viewSpectrumLearningCenter enabled" in {

      helper.verifyCapabilityAbsent(helper.allNetworks, Set(ViewPortfolioViaPublic))
      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testnetwork1, "Issuer")), Set(ViewSpectrumLearningCenter))
      helper.verifyCapabilityPresent(Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix)), Set("someOtherCapability"))

      val migration = new _0024_add_ViewPortfolioViaPublic_to_spectrum_users(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testnetwork1, "Issuer")), Set(ViewSpectrumLearningCenter, ViewPortfolioViaPublic, EditPortfolioViaOwner, ViewPortfolioViaOwner))
      helper.verifyCapabilityAbsent(Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix)), Set(ViewPortfolioViaPublic))
    }

    "add ViewPortfolioViaPublic when EditPortfolioViaOwner is enabled already" in {
      val issuerWithViewSpectrumPublic: CustomRoleDefinition = CustomRoleDefinition("Issuer", Set(ViewSpectrumLearningCenter, EditPortfolioViaOwner))
      val doc = NetworkFormat.write(testnetwork1.copy(customRolesConfig = Set(issuerWithViewSpectrumPublic, testFa)))
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testnetwork1, "Issuer")), Set(ViewSpectrumLearningCenter, EditPortfolioViaOwner))
      helper.verifyCapabilityPresent(Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix)), Set("someOtherCapability"))

      val migration = new _0024_add_ViewPortfolioViaPublic_to_spectrum_users(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testnetwork1, "Issuer")), Set(ViewSpectrumLearningCenter, ViewPortfolioViaPublic, ViewPortfolioViaOwner, EditPortfolioViaOwner))
    }
  }
}
