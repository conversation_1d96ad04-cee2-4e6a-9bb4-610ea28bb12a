package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.usersnetworks.db.migrations._0048_rename_viewUIRainbowCapability_to_Architect.{CustomRoleDefinition, Network, networkCodecRegistry}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.collection.immutable.Document
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0048_rename_viewUIRainbowCapability_to_ArchitectSpec extends WordSpec with Matchers with EmbeddedMongoLike {
  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0048_rename_viewUIRainbowCapability_to_Architect" should {
    "rename viewUIRainbow capability to viewUIArchitect in all relevant networks" in {
      val customRoleDefinition1 = CustomRoleDefinition(
        role = "role1",
        capabilities = Set("viewUIRainbow", "otherCapabilities")
      )
      val customRoleDefinition2 = CustomRoleDefinition(
        role = "role2",
        capabilities = Set("otherCapabilities")
      )
      val updatedCustomRoleDefinition1 = customRoleDefinition1.copy(
        capabilities = Set("viewUIArchitect", "otherCapabilities")
      )

      val net1 = Network(
        id = "net1",
        customRolesConfig = Set(customRoleDefinition1, customRoleDefinition2)
      )
      val net2 = Network(
        id = "net2",
        customRolesConfig = Set(customRoleDefinition2)
      )

      val networkCollection: MongoCollection[Network] = db.getCollection[Network]("networks_new").withCodecRegistry(networkCodecRegistry)
      networkCollection.insertMany(Seq(net1, net2)).toFuture.await

      val migration = new _0048_rename_viewUIRainbowCapability_to_Architect(db)
      migration.apply.await

      val networks = networkCollection.find(Document()).toFuture.await
      networks.map(network => network.id -> network).toMap shouldBe Map(
        "net1" -> net1.copy(
          customRolesConfig = Set(updatedCustomRoleDefinition1, customRoleDefinition2)
        ),
        "net2" -> net2
      )
    }
  }
}
