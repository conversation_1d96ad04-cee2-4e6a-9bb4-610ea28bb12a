package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.OrdersCapabilities
import com.simonmarkets.capabilities.OrdersCapabilities.{CancelOrderViaOwnerCapability, EditOrderViaNetworkCapability, EditOrderViaNetworkTypeCapability, EditOrderViaOwnerCapability}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0088_add_editAfterBooksClose_capabilitySpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val addCapabilities = Set("editOrderAfterBooksClose")

  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, Set(EditOrderViaOwnerCapability.name))

  val testAnotherManager: CustomRoleDefinition = CustomRoleDefinition(
    "anotherManager",
    Set(EditOrderViaNetworkCapability.name, EditOrderViaNetworkTypeCapability.name))

  lazy val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set(CancelOrderViaOwnerCapability.name))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(1, "test"),
    customRolesConfig = Set(testFaManager, testFa)
  )

  private val testNetwork2 = testNetwork1.copy(customRolesConfig = Set(testAnotherManager, testFa))

  override def beforeEach(): Unit = helper.beforeEachHelper()

  "_0088_add_editAfterBooksClose_capabilitySpec" should {

    "ensure edit capability is added where needed" in {
      val docs = Set(testNetwork1, testNetwork2).map(NetworkFormat.write).toList
      helper.networksCol.insertMany(docs).toFuture.await
      helper.verifyCapabilityAbsent(Set(testNetwork1, testNetwork2), addCapabilities)

      val migration = new _0088_add_editAfterBooksClose_capability(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(
        Seq((testNetwork1, UserRole.EqPIPGFA.productPrefix), (testNetwork2, UserRole.EqPIPGFA.productPrefix)),
        Set(OrdersCapabilities.EditOrderAfterBooksCloseCapability.name))
      helper.verifyCapabilityPresent(
        Seq((testNetwork1, UserRole.EqPIPGFAManager.productPrefix), (testNetwork2, "anotherManager")),
        Set(OrdersCapabilities.EditOrderAfterBooksCloseCapability.name))
    }
  }
}
