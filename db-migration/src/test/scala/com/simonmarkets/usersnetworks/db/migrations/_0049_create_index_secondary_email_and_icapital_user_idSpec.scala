package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext


class _0049_create_index_secondary_email_and_icapital_user_idSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global


  "0049_create_index_secondary_email_and_icapital_user_idSpec" should {

    "create index on secondaryEmail and iCapitalUserId fields in users collection" in {
      val collection = db.getCollection("users")
      val migration = new _0049_create_index_secondary_email_and_icapital_user_id(db)
      migration.apply.await
      whenReady(collection.getIndexes) { indexes =>
        indexes should contain(Index("secondaryEmail_1", List(IndexedField("secondaryEmail")), unique = false))
        indexes should contain(Index("iCapitalUserId_1", List(IndexedField("iCapitalUserId")), unique = true))
      }
    }
  }


}
