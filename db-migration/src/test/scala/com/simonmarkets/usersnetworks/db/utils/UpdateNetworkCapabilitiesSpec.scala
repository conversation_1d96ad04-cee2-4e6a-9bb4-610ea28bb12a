package com.simonmarkets.usersnetworks.db.utils

import com.goldmansachs.marquee.pipg.UserRole
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.syntax._
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoDatabase
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class UpdateNetworkCapabilitiesSpec extends WordSpec with EmbeddedMongoLike with Matchers with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  /** Test migration that adds some capabilities to all networks */
  class UpdateAllNetworkCapabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
    override implicit val traceId: TraceId = TraceId("UpdateAllNetworkCapabilities")

    override def capabilities: Set[Capability] = Set("addCapabilityAll1", "addCapabilityAll2")

    override def shouldAdd: Boolean = true

    override def shouldUpdate(network: Network, customRole: Role): Boolean = true
  }

  /** Test migration that adds some capabilities to one network for some roles */
  class UpdateOneNetworkCapabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
    override implicit val traceId: TraceId = TraceId("UpdateOneNetworkCapabilities")

    override def capabilities: Set[Capability] = Set("addCapabilitySome1", "addCapabilitySome2")

    override def shouldAdd: Boolean = true

    override def shouldUpdate(network: Network, customRole: Role): Boolean =
      network.id == simon.Id.NetworkId("networkIdMulti") && (customRole == UserRole.EqPIPGFAManager.productPrefix || customRole == UserRole.Issuer.productPrefix)
  }

  /** Test migration that removes some capabilities from all networks */
  class RemoveSomeCapabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
    override implicit val traceId: TraceId = TraceId("RemoveSomeCapabilities")

    override def capabilities: Set[Capability] = Set("common-capability1", "common-capability2")

    override def shouldAdd: Boolean = false

    override def shouldUpdate(network: Network, customRole: Role): Boolean = true
  }

  /** Test migration that does not make any updates */
  class DoNothingCapabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
    override implicit val traceId: TraceId = TraceId("DoNothingCapabilities")

    override def capabilities: Set[Capability] = Set("common-capability1", "common-capability2")

    override def shouldAdd: Boolean = false

    override def shouldUpdate(network: Network, customRole: Role): Boolean = false
  }

  private lazy val updateAllNetworkCapabilities = new UpdateAllNetworkCapabilities(db)
  private lazy val updateOneNetworkCapabilities = new UpdateOneNetworkCapabilities(db)
  private lazy val removeSomeNetworkCapabilities = new RemoveSomeCapabilities(db)
  private lazy val doNothingCapabilities = new DoNothingCapabilities(db)

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "UpdateNetworkCapabilities" when {

    "configured to add to all networks" should {

      "update all networks" in {
        updateAllNetworkCapabilities.apply.await
        helper.verifyCapabilityPresent(helper.allNetworks, Set("addCapabilityAll1", "addCapabilityAll2"))
      }

    }

    "configured to remove from all networks" should {

      "update all networks" in {
        removeSomeNetworkCapabilities.apply.await
        helper.verifyCapabilityAbsent(helper.allNetworks, Set("common-capability1", "common-capability2"))
      }

    }

    "configured to do nothing to all networks" should {

      "do nothing" in {
        doNothingCapabilities.apply.await
      }

    }

    "configured to add to some networks and networks" should {

      "update only specific networks and roles" in {
        updateOneNetworkCapabilities.apply.await
        // Present in network / roles specified by UpdateOneNetworkCapabilities
        helper.verifyCapabilityPresent(
          Seq((helper.multiRoleNetwork, UserRole.EqPIPGFAManager.productPrefix), (helper.multiRoleNetwork, UserRole.Issuer.productPrefix)),
          Set("addCapabilitySome1", "addCapabilitySome2")
        )
        // Absent in all others
        helper.verifyCapabilityAbsent(helper.allNetworks - helper.multiRoleNetwork, Set("addCapabilitySome1", "addCapabilitySome2"))
      }

    }

  }


}
