package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.LearnV2ContentCustomizationsCapabilities._
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0105_add_viewContentCustomizationViaNetworkAndLocation_capabilitySpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  private val oldContentCustomizationCapabilities: Set[String] = Set(
    ViewContentCustomizationViaNetworkCapability.name
  )

  def ContentCustomizationCapabilities: Set[String] = Set(
    ViewContentCustomizationViaNetworkAndLocationCapability.name
  )

  val idHubOrganization: IdHubOrganization = IdHubOrganization(1, "idHubOrg")
  val withOldContentCustomizationCapabilities: CustomRoleDefinition = CustomRoleDefinition("withOldContentCustomizationCapabilities", oldContentCustomizationCapabilities)
  val withoutOldContentCustomizationCapabilities: CustomRoleDefinition = CustomRoleDefinition("withoutOldContentCustomizationCapabilities", Set("someFakeCapability"))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkId1"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    customRolesConfig = Set(withoutOldContentCustomizationCapabilities)
  )

  private val testNetwork2 = Network(
    id = simon.Id.NetworkId("networkId2"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    customRolesConfig = Set(withOldContentCustomizationCapabilities, withoutOldContentCustomizationCapabilities)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0105_add_viewContentCustomizationViaNetworkAndLocation_capability" should {
    "add viewContentCustomizationViaNetworkAndLocation capability to existing roles with existing ViewContentCustomizationViaNetwork capability" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, ContentCustomizationCapabilities)
      val docs = Set(testNetwork1, testNetwork2).map(NetworkFormat.write).toSeq
      helper.networksCol.insertMany(docs).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testNetwork1, withoutOldContentCustomizationCapabilities.role),(testNetwork2, withoutOldContentCustomizationCapabilities.role)), Set("someFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, withOldContentCustomizationCapabilities.role)), oldContentCustomizationCapabilities)

      val migration = new _0105_add_viewContentCustomizationViaNetworkAndLocation_capability(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testNetwork1, withoutOldContentCustomizationCapabilities.role),(testNetwork2, withoutOldContentCustomizationCapabilities.role)), Set("someFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, withOldContentCustomizationCapabilities.role)), ContentCustomizationCapabilities)
    }
  }
}
