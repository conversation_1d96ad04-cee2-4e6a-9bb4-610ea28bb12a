package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.LearnV2TrackCustomizationsCapabilities._
import com.simonmarkets.capabilities.LearnV2TracksCapabilities
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import com.simonmarkets.syntax._

import scala.concurrent.ExecutionContext

class _0063_add_viewApprovedTrackCustomization_capabilitiesSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  private val oldTrackCapabilities: Set[String] = Set(
    LearnV2TracksCapabilities.ViewActiveTracksViaTrackIdCapability.name,
    LearnV2TracksCapabilities.ViewAllTracksViaTrackIdCapability.name
  )

  def trackCustomizationCapabilities: Set[String] = Set(
    ViewApprovedTrackCustomizationViaUserIdCapability.name,
    ViewApprovedTrackCustomizationViaNetworkCapability.name,
    ViewApprovedTrackCustomizationViaNetworkAndLocationCapability.name,
    ViewApprovedTrackCustomizationViaNetworkAndFaNumberCapability.name
  )

  val idHubOrganization: IdHubOrganization = IdHubOrganization(1, "idHubOrg")
  val withOldTrackCapabilities: CustomRoleDefinition = CustomRoleDefinition("withOldTrackCapabilities", oldTrackCapabilities)
  val withoutOldTrackCapabilities: CustomRoleDefinition = CustomRoleDefinition("withoutOldTrackCapabilities", Set("someFakeCapability"))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkId1"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    customRolesConfig = Set(withoutOldTrackCapabilities)
  )

  private val testNetwork2 = Network(
    id = simon.Id.NetworkId("networkId2"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    customRolesConfig = Set(withOldTrackCapabilities, withoutOldTrackCapabilities)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0063_add_track_customization_capabilities" should {
    "add Track Customization View capabilities to existing roles with existing Track View capabilities" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, trackCustomizationCapabilities)
      val docs = Set(testNetwork1, testNetwork2).map(NetworkFormat.write).toSeq
      helper.networksCol.insertMany(docs).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testNetwork1, withoutOldTrackCapabilities.role),(testNetwork2, withoutOldTrackCapabilities.role)), Set("someFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, withOldTrackCapabilities.role)), oldTrackCapabilities)

      val migration = new _0063_add_viewApprovedTrackCustomization_capabilities(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testNetwork1, withoutOldTrackCapabilities.role),(testNetwork2, withoutOldTrackCapabilities.role)), Set("someFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, withOldTrackCapabilities.role)), trackCustomizationCapabilities)
    }
  }
}
