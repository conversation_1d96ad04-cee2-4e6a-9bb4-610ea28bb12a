package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.SimonUICapabilities
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0093_add_ViewUILifecyclePortalSIClientsWidgetCapabilitySpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, Set(SimonUICapabilities.ViewNewSILifecycleDashboardCapability.name))
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("someOtherCapability"))
  val lifecycleDashboardCapability: Set[String] = Set(SimonUICapabilities.ViewNewSILifecycleDashboardCapability.name)
  val siClientsDashboardCapability: Set[String] =  Set(SimonUICapabilities.ViewUILifecyclePortalSIClientsWidgetCapability.name)


  private val testnetwork1 = Network(
    id = simon.Id.NetworkId("network1"),
    name = "network1",
    networkCode = "N1",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFaManager, testFa)
  )

  private val testnetwork2 = Network(
    id = simon.Id.NetworkId("network2"),
    name = "network2",
    networkCode = "N2",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa)
  )

  val network1Roles: Seq[(Network, String)] = Seq((testnetwork1, UserRole.EqPIPGFAManager.productPrefix))
  val network2Roles: Seq[(Network, String)] = Seq((testnetwork2, UserRole.EqPIPGFAManager.productPrefix))

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0093_add_ViewUILifecyclePortalSIClientsWidgetCapabilitySpec" should {
    "add ViewUILifecyclePortalSIClientsWidget capability to all roles that have ViewNewSILifecycleDashboard capability" in {
      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityAbsent(network1Roles, siClientsDashboardCapability)
      helper.verifyCapabilityPresent(network1Roles, lifecycleDashboardCapability)
      val migration = new _0093_add_ViewUILifecyclePortalSIClientsWidgetCapability(db)
      migration.apply.await
      helper.verifyCapabilityPresent(network1Roles, siClientsDashboardCapability)
    }

    "not add ViewUILifecyclePortalSIClientsWidget capability to roles that do not have ViewNewSILifecycleDashboard capability" in {
      val doc = NetworkFormat.write(testnetwork2)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityAbsent(network2Roles, lifecycleDashboardCapability)
      val migration = new _0093_add_ViewUILifecyclePortalSIClientsWidgetCapability(db)
      migration.apply.await
      helper.verifyCapabilityAbsent(network2Roles, siClientsDashboardCapability)
    }

  }
}
