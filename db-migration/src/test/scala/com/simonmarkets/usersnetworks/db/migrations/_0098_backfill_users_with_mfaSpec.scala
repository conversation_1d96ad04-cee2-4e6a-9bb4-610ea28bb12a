package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion

import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.mongodb.scala.model.Filters
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0098_backfill_users_with_mfaSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global



  "_0098_backfill_users_with_mfaSpec" should {
    "backfill users with mfa" in {
      val coll = db.getCollection[Document]("users")
      val migration = new _0098_backfill_users_with_mfa(db)


      val docs = (0 to 2).map(i => Document("id" -> i))

      coll.insertMany(docs).toFuture().await
      migration.apply.await

      val numBackfilledUsers = coll
        .countDocuments(Filters.exists("mfas", exists = true))
        .toFuture()
        .await

      numBackfilledUsers shouldBe 3
    }
  }
}
