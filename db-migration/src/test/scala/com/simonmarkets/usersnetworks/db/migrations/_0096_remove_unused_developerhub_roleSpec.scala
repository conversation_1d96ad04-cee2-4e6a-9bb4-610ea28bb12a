package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.users.common.{LoginMode, User}
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.{Document, MongoCollection}
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}
import simon.Id.NetworkId
import com.simonmarkets.syntax._
import com.simonmarkets.users.repository.encoders.UserFormat

import scala.concurrent.ExecutionContext

class _0096_remove_unused_developerhub_roleSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  val testUnusedRole: CustomRoleDefinition = CustomRoleDefinition("DeveloperHubAdmin", Set("cap1", "cap2"))

  val testOtherRole: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("cap1"))

  private val testnetwork1 = Network(
    id = simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(1, "test"),
    customRolesConfig = Set(testOtherRole, testUnusedRole)
  )

  lazy val usersColl: MongoCollection[User] = db.getCollection[User]("users").withCodecRegistry(UserFormat.codecRegistry)
  lazy val networksColl: MongoCollection[Document] = db.getCollection[Document]("networks_new")

  override def beforeEach(): Unit = {
    (for {
      _ <- usersColl.drop()
      _ <- networksColl.drop()
    } yield ()).toFuture().await
  }

  "_0096_remove_unused_developerhub_role" should {

    lazy val migration = new _0096_remove_unused_developerhub_role(db)

    "remove DeveloperHubAdmin role from networks" in {
      val doc = NetworkFormat.write(testnetwork1)

      testnetwork1.customRolesConfig.map(_.role) should contain("DeveloperHubAdmin")


      for {
        _ <- networksColl.insertOne(doc).toFuture
        _ <- migration.apply
        updatedNetwork <- networksColl.find().head().map(NetworkFormat.read)
      } yield updatedNetwork.customRolesConfig.map(_.role) should not contain "DeveloperHubAdmin"
    }

    "remove DeveloperHubAdmin role from users" in {
      val networkId = NetworkId("networkId")
      val user1 = TestUser.apply(id = "id1", networkId, customRoles = Set("otherRole", "DeveloperHubAdmin"))

      user1.customRoles should contain("DeveloperHubAdmin")

      for {
        _ <- usersColl.insertOne(user1).toFuture()
        _ <- migration.apply
        newUser <- usersColl.find().head()
      } yield newUser.customRoles should not contain "DeveloperHubAdmin"

    }
  }
}