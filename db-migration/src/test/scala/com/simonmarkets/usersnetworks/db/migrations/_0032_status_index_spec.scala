package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.repository.MongoUserImpersonationRepository.userImpersonationCodecRegistry
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0032_status_index_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_31_status_index" should {
    lazy val impersonationColl = db.getCollection("users.impersonation").withCodecRegistry(userImpersonationCodecRegistry)

    "create indexes" in {
      val migration = new _0032_status_index(db)
      migration.apply.await()

      whenReady(impersonationColl.getIndexes) { indexes =>
        indexes should contain(
          Index("_id_", List(IndexedField("_id")), unique = false))

        indexes should contain(
          Index("status_1_impersonatorUserId_1", List(IndexedField(migration.Status), IndexedField(migration.ImpersonatorUserId)), unique = true))
      }
    }
  }
}