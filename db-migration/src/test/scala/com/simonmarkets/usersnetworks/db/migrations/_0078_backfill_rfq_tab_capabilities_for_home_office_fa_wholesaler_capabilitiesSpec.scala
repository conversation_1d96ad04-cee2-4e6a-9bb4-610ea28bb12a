package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.{RfqCapabilities, RfqTemplatesCapabilities}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0078_backfill_rfq_tab_capabilities_for_home_office_fa_wholesaler_capabilitiesSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, Set("editRfqViaNetwork", "viewNetworkTemplateRfqViaNetworkCapability", RfqCapabilities.ViewRfqViaOwner))
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set(RfqCapabilities.ViewRfqViaOwner, "editRfqViaOwner"))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa)
  )

  private val testNetwork2 = Network(
    id = simon.Id.NetworkId("testNetwork2"),
    name = "testNetwork2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa, testFaManager)
  )

  private val testNetwork3 = Network(
    id = simon.Id.NetworkId("testNetwork3"),
    name = "testNetwork3",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFaManager)
  )

  val newCapabilitiesFa = Set(
    RfqTemplatesCapabilities.ViewRfqTemplateViaUserCreatedCapability.name,
    RfqTemplatesCapabilities.EditRfqTemplateViaUserCreatedCapability.name
  )
  val newCapabilitiesFaManger = Set(
    RfqTemplatesCapabilities.EditRfqTemplateViaNetworkCapability.name,
    RfqTemplatesCapabilities.ViewRfqTemplateViaNetworkCapability.name,
    RfqTemplatesCapabilities.ViewRfqTemplateViaUserCreatedCapability.name
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0078_add_RfqTemplateCapabilitiesSpec" should {
    "add new capabilities when certain capabilities already exist" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilitiesFa)
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilitiesFaManger)
      val doc = NetworkFormat.write(testNetwork1)
      val doc2 = NetworkFormat.write(testNetwork2)
      val doc3 = NetworkFormat.write(testNetwork3)
      helper.networksCol.insertMany(Seq(doc, doc2, doc3)).toFuture().await()

      val migration = new _0078_add_RfqTemplateCapabilities(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testNetwork1, UserRole.EqPIPGFA.productPrefix)), newCapabilitiesFa)
      helper.verifyCapabilityPresent(Seq((testNetwork2, UserRole.EqPIPGFA.productPrefix)), newCapabilitiesFa)
      helper.verifyCapabilityPresent(Seq((testNetwork2, UserRole.EqPIPGFAManager.productPrefix)), newCapabilitiesFaManger)
      helper.verifyCapabilityPresent(Seq((testNetwork3, UserRole.EqPIPGFAManager.productPrefix)), newCapabilitiesFaManger)
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilitiesFa)
    }
  }
}
