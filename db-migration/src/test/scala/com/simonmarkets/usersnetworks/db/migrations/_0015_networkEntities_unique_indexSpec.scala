package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext


class _0015_networkEntities_unique_indexSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0015_networkEntities_unique_index" should {

    lazy val coll = db.getCollection("networkEntities")

    "create indexes" in {
      val migration = new _0015_networkEntities_unique_index(db)
      migration.apply.await()

      whenReady(coll.getIndexes) { indexes =>
        indexes should contain(Index("entities.key_1", List(IndexedField("entities.key")), unique = true))
      }
    }
  }
}
