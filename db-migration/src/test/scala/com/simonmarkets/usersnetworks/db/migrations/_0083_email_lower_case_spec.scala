package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.model.{Filters, Indexes}
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0083_email_lower_case_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val user1 = MiniUser(id = "1", email = "<EMAIL>")
  private val user2 = user1.copy(id = "2", email = "<EMAIL>")

  "_0083_email_lower_case" should {

    "lower emails" in {

      val migration = new _0083_email_lower_case(db)
      val col = migration.userCol

      for {

        _ <- col.createIndex(Indexes.ascending("emailLowerCase")).toFuture
        _ <- col.insertMany(Seq(user1, user2)).toFuture
        _ <- col.find(Filters.equal("id", user1.id)).head.map(_.email shouldBe "<EMAIL>")
        _ <- col.find(Filters.equal("id", user2.id)).head.map(_.email shouldBe "<EMAIL>")
        _ <- migration.apply
        _ <- col.find(Filters.equal("id", user1.id)).head.map(_.email shouldBe "<EMAIL>")
        _ <- col.find(Filters.equal("id", user2.id)).head.map(_.email shouldBe "<EMAIL>")
      } yield succeed
    }
  }
}
