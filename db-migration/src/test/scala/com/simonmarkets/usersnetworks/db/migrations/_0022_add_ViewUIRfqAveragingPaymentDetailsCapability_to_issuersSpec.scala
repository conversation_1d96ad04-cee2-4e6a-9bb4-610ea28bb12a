package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, NetworkType, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.SimonUICapabilities.ViewUIRfqAveragingPaymentDetailsCapability
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0022_add_ViewUIRfqAveragingPaymentDetailsCapability_to_issuersSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach{
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val issuer: CustomRoleDefinition = CustomRoleDefinition("Issuer", Set("someCapability"))
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("someOtherCapability"))


  private val testnetwork1 = Network(
    id =  simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    networkTypes = Some(List(NetworkType.Issuer)),
    customRolesConfig = Set(issuer, testFa)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0022_add_ViewUIRfqAveragingPaymentDetailsCapability_to_issuersSpec" should {
    "add desired capabilities to specific roles" in {

      helper.verifyCapabilityAbsent(helper.allNetworks, Set(ViewUIRfqAveragingPaymentDetailsCapability.name))
      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testnetwork1, "Issuer")), Set("someCapability"))
      helper.verifyCapabilityPresent(Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix)), Set("someOtherCapability"))

      val migration = new _0022_add_ViewUIRfqAveragingPaymentDetailsCapability_to_issuers(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testnetwork1, "Issuer")), Set("someCapability", ViewUIRfqAveragingPaymentDetailsCapability.name))
      helper.verifyCapabilityPresent(Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix)), Set("someOtherCapability", ViewUIRfqAveragingPaymentDetailsCapability.name))
      helper.verifyCapabilityAbsent(helper.allNetworks, Set(ViewUIRfqAveragingPaymentDetailsCapability.name))
    }
  }
}
