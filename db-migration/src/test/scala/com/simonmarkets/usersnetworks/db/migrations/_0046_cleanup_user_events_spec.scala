package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.FutureBlock
import com.simonmarkets.logging.TraceId
import com.simonmarkets.users.common.{EventInfo, User, UserDomainEvent}
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import java.time.temporal.ChronoUnit

import scala.concurrent.ExecutionContext

class _0046_cleanup_user_events_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val networkId = NetworkId("networkId")

  //dummy data
  private val user1 = TestUser.apply(
    id = "id_1",
    networkId = networkId,
    eventInfo = EventInfo(UserDomainEvent.UserActivatedSendEmail, "", TraceId.randomize)
  )

  private val user2 = TestUser.apply(
    id = "id_2",
    networkId = networkId,
    eventInfo = EventInfo(UserDomainEvent.UserPasswordReset, "", TraceId.randomize)
  )

  private val user3 = TestUser.apply(
    id = "id_3",
    networkId = networkId,
    eventInfo = EventInfo(UserDomainEvent.UserActivatedDontSendEmail, "", TraceId.randomize)
  )
  private val user4 = TestUser.apply(
    id = "id_4",
    networkId = networkId,
    eventInfo = EventInfo.Default
  )

  private val expectedInfo = EventInfo(
    UserDomainEvent.UserStateTransitioned,
    "_0046_cleanup_user_events_migration",
    TraceId("_0046_cleanup_user_events")
  )

  "_0046_cleanup_user_events" should {

    "reset event info" in {

      val migration = new _0046_cleanup_user_events(db)

      val userInserts: Seq[Document] = Seq(user1, user2, user3, user4).map(UserFormat.write)
      migration.usersColl.insertMany(userInserts).toFuture().await()

      migration.apply.await()

      whenReady(migration.usersColl.find().toFuture) { userDocs =>

        val migratedUsers = userDocs.map(UserFormat.read)
        val expectedUsers = Seq(
          user1.copy(eventInfo = expectedInfo),
          user2.copy(eventInfo = expectedInfo),
          user3,
          user4
        )

        migratedUsers.map(cleanDates) should contain theSameElementsAs expectedUsers.map(cleanDates)
      }
    }

  }

  //there is some loss of precision in date conversions that causes match failures
  private def cleanDates(u: User): User = u.copy(
    createdAt = u.createdAt.truncatedTo(ChronoUnit.HOURS),
    updatedAt = u.updatedAt.truncatedTo(ChronoUnit.HOURS),
    lastVisitedAt = u.lastVisitedAt.map(_.truncatedTo(ChronoUnit.HOURS))
  )

}
