package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.CustomRoleDefinition
import com.gs.marquee.foundation.util.AwaitTimeout.FutureTimeoutOps
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, WordSpec}

import scala.concurrent.ExecutionContext

class _0068_add_developerhub_capabilitiesSpec extends WordSpec with BeforeAndAfterEach with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  val developerHubUserCustomRole = CustomRoleDefinition("DeveloperHubUser", Set())
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)
  lazy val migration = new _0068_add_developerhub_capabilities(db)

  lazy val networks = helper.allNetworks.map(n =>
    n.copy(customRolesConfig = n.customRolesConfig ++ Set(developerHubUserCustomRole))
  )

  override protected def beforeEach(): Unit = {
    super.beforeEach()
    helper.beforeEachHelper(networks)
  }

  "_0068_add_developerhub_capabilities" should {
    "add capabilities to all networks that have role" in {
      val ns: Seq[(Network, String)] = networks.collect {
        case n if n.customRolesConfig.exists(_.role == developerHubUserCustomRole.role) =>
          (n, developerHubUserCustomRole.role)
      }.toSeq

      migration.apply.await
      helper.verifyCapabilityPresent(ns,Set(
        "developer",
        "viewUIDeveloperHub",
        "viewDeveloperHubViaUserId",
        "viewDeveloperHubServicesByEnv",
        "viewDeveloperHubExternalUserPreferencesViaUserId",
        "editDeveloperHubExternalUserPreferencesViaUserId",
      ))
    }
  }
}
