package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.db.migration.FutureBlock
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestNetwork
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0057_add_NetworkReadOnlyCapabilitySpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val network1 = TestNetwork.apply(
    id = NetworkId("id_1").toString,
  )

  private val network2 = TestNetwork.apply(
    id = NetworkId("id_2").toString,
  )

  private val network3 = TestNetwork.apply(
    id = NetworkId("id_3").toString,
  )

  "_0057_add_NetworkReadOnlyCapability" should {

    "add ReadOnlyAdmin entitlement to network" in {

      val migration = new _0057_add_NetworkReadOnlyAdminCapability(db)

      val networkInserts: Seq[Document] = Seq(network1, network2, network3).map(NetworkFormat.write)
      migration.networksCollection.insertMany(networkInserts).toFuture().await()

      migration.apply.await()

      val updatedNetworks = migration.networksCollection.find().toFuture

      whenReady(updatedNetworks) { networkDocs =>
        val migratedNetworks = networkDocs.map(NetworkFormat.read)
        val hasReadOnlyAdminCapability = migratedNetworks.map { network =>
          network.entitlements.contains(Capabilities.ReadOnlyAdmin)
        }
        hasReadOnlyAdminCapability shouldBe List(true, true, true)
      }
    }
  }
}
