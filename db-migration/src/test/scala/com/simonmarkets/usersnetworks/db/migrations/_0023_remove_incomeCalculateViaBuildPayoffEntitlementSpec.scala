package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0023_remove_incomeCalculateViaBuildPayoffEntitlementSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  private lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)
  private val incomeCalculateViaBuildPayoffEntitlement = "incomeCalculateViaBuildPayoffEntitlement"
  private val testFaManager: CustomRoleDefinition =
    CustomRoleDefinition(
      UserRole.EqPIPGFA.productPrefix,
      Set("EqPIPGFA", incomeCalculateViaBuildPayoffEntitlement, "EqPIPGFAManager"))
  private val testFa2: CustomRoleDefinition =
    CustomRoleDefinition(
      UserRole.Issuer.productPrefix,
      Set("Issuer", incomeCalculateViaBuildPayoffEntitlement, "common-capability2", "issuer-capability"))
  private val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  private val testnetwork1 = Network(
    id = simon.Id.NetworkId("networkIdFmA"),
    name = "networkIdWithFMRoleA",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFaManager)
  )
  private val testnetwork2 = Network(
    id = simon.Id.NetworkId("networkIdIssuer"),
    name = "networkIdWithIssuerRole",
    networkCode = "ZBA",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa2)
  )
  private lazy val migration = new _0023_remove_incomeCalculateViaBuildPayoffEntitlement(db)

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }


  "remove tradeOfferingViaTradePayoffEntitlement from all networks" in {
    helper.verifyCapabilityAbsent(helper.allNetworks, Set(incomeCalculateViaBuildPayoffEntitlement));
    {
      val docs = Seq(testnetwork1, testnetwork2).map(NetworkFormat.write)
      helper.networksCol.insertMany(docs).toFuture().await()
    }
    Seq(testnetwork1, testnetwork2).foreach(network =>
      helper.verifyCapabilityPresent(Set(network), Set(incomeCalculateViaBuildPayoffEntitlement)))

    migration.apply.await

    helper.verifyCapabilityAbsent(
      helper.allNetworks ++ Set(testnetwork1, testnetwork2),
      Set(incomeCalculateViaBuildPayoffEntitlement))
  }
}
