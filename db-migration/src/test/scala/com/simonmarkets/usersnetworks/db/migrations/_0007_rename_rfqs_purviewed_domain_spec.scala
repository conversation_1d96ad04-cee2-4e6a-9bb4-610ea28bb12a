package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax._
import com.simonmarkets.usersnetworks.db.migrations._0007_rename_rfqs_purviewed_domain._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.{Document, MongoCollection}
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0007_rename_rfqs_purviewed_domain_spec extends WordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_7_rename_rfqs_purviewed_domain" should {
    "update networks" in {
      val issuerPurview1 = IssuerPurview(
        network = "purviewNet1",
        issuers = Set("test"),
        wholesaler = None,
        purviewedDomains = None
      )
      val issuerPurview2 = IssuerPurview(
        network = "purviewNet2",
        issuers = Set("test"),
        wholesaler = None,
        purviewedDomains = Some(Set("Users", "Accounts", "Rfqs"))
      )
      val updatedIssuerPurview2 = issuerPurview2.copy(purviewedDomains = Some(Set("Users", "Accounts", "EditRfqs")))
      val issuerPurview3 = IssuerPurview(
        network = "purviewNet2",
        issuers = Set("test"),
        wholesaler = None,
        purviewedDomains = Some(Set("Users", "Accounts"))
      )

      val net1 = Network(
        id = "net1",
        purviewNetworks = None
      )
      val net2 = Network(
        id = "net2",
        purviewNetworks = Some(Set(issuerPurview1, issuerPurview2, issuerPurview3))
      )

      val networkCollection: MongoCollection[Network] = db.getCollection[Network]("networks_new").withCodecRegistry(networkCodecRegistry)
      networkCollection.insertMany(Seq(net1, net2)).toFuture.await

      val migration = new _0007_rename_rfqs_purviewed_domain(db)
      migration.apply.await

      val networks = networkCollection.find(Document()).toFuture.await
      networks.map(n => n.id -> n).toMap shouldBe Map(
        "net1" -> net1,
        "net2" -> net2.copy(
          purviewNetworks = Some(Set(issuerPurview1, updatedIssuerPurview2, issuerPurview3))
        )
      )
    }
  }
}
