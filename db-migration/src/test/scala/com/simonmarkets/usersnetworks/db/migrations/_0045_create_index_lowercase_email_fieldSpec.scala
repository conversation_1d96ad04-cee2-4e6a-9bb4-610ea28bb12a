package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{Index, IndexedField}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}

import scala.concurrent.ExecutionContext



class _0045_create_index_lowercase_email_fieldSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global


  "0044_create_index_lowercase_email_field" should {

    "create index on emailLowercase field for users collection" in {
      val collection = db.getCollection("users")
      val migration = new _0045_create_index_lowercase_email_field(db)
      migration.apply.await
      whenReady(collection.getIndexes) { indexes =>
        indexes should contain(Index("emailLowerCase_1", List(IndexedField("emailLowerCase")), unique = false))
      }
    }
  }


}
