package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0069_rename_userId_to_id_for_paidFeatures_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0069_rename_userId_to_id_for_paidFeatures_spec" should {

    lazy val col = db.getCollection("users.paidFeatures")

    "apply migration and add idType UserId" in {
      // Insert the initial document
      val documents = Seq(
        Document("_id" -> "6581bcb2850d114a73e2a1d1", "userId" -> "00u1c1q9y2omuBAga0h8", "version" -> 1),
        Document("_id" -> "23f23g23b3g3efwfewef23", "userId" -> "v2f3rvsf24f2fd2d2", "version" -> 1)
      )
      col.insertMany(documents).toFuture().await()
      // Apply migration
      val indexMigration = new _0063_create_index_on_userId_for_userPaidFeatures(db)
      indexMigration.apply.await()

      whenReady(col.getIndexes) { indexes =>
        indexes should contain(Index("userId_1", List(IndexedField("userId")), unique = true))
      }

      val migration = new _0069_rename_userId_to_id_for_paidFeatures(db)
      migration.apply.await()

      whenReady(col.getIndexes) { indexes =>
        indexes should not contain (Index("userId_1", List(IndexedField("userId")), unique = true))
      }

      val updatedDocument = col.find(Document("_id" -> "6581bcb2850d114a73e2a1d1")).headOption().await()
      updatedDocument should be(
        Some(Document(
          "_id" -> "6581bcb2850d114a73e2a1d1",
          "id" -> "00u1c1q9y2omuBAga0h8",
          "version" -> 1
        )))

      val updatedDocumentTwo = col.find(Document("_id" -> "23f23g23b3g3efwfewef23")).headOption().await()
      updatedDocumentTwo should be(
        Some(Document(
          "_id" -> "23f23g23b3g3efwfewef23",
          "id" -> "v2f3rvsf24f2fd2d2",
          "version" -> 1
        )))
    }
  }
}