package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.RfqConfigsCapabilities.ViewRfqConfigsViaNetworkCapability
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0055_add_ViewRfqConfigsViaNetworkCapability_to_all_networksSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  def capabilities: Set[String] = Set(ViewRfqConfigsViaNetworkCapability.name)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("someCapability"))
  val testFa2: CustomRoleDefinition = CustomRoleDefinition(UserRole.Issuer.productPrefix, Set("someOtherCapability"))

  private val testnetwork1 = Network(
    id = simon.Id.NetworkId("networkIdFmA"),
    name = "networkIdWithFMRoleA",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFaManager)
  )

  private val testnetwork2 = Network(
    id = simon.Id.NetworkId("networkIdIssuer"),
    name = "networkIdWithIssuerRole",
    networkCode = "ZBA",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa2)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0055_add_ViewRfqConfigsViaNetworkCapability_to_all_networks" should {
    "add ViewRfqConfigsViaNetworkCapability to all networks" in {

      helper.verifyCapabilityAbsent(helper.allNetworks, capabilities)
      val docs = Set(testnetwork1, testnetwork2).map(NetworkFormat.write).toSeq
      helper.networksCol.insertMany(docs).toFuture().await()
      helper.verifyCapabilityPresent(Set(testnetwork1), Set("someCapability"))
      helper.verifyCapabilityPresent(Set(testnetwork2), Set("someOtherCapability"))

      val migration = new _0055_add_ViewRfqConfigsViaNetworkCapability_to_all_networks(db)
      migration.apply.await

      helper.verifyCapabilityPresent(helper.allNetworks, capabilities)
      helper.verifyCapabilityPresent(Set(testnetwork1), Set("someCapability") ++ capabilities)
      helper.verifyCapabilityPresent(Set(testnetwork2), Set("someOtherCapability") ++ capabilities)
    }
  }

}
