package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.IdHubOrganization
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations._0027_add_viewUIBacktestingComparisonCapability_capability.annuityNetworks
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0027_add_viewUIBacktestingComparisonCapability_capabilitySpec
    extends WordSpec
    with Matchers
    with EmbeddedMongoLike
    with BeforeAndAfterEach {

  implicit private val ec: ExecutionContext = CurrentThreadExecutionContext

  private lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  private val networksToAdd = annuityNetworks.zipWithIndex.map { case (name, idx) =>
    Network(
      id = simon.Id.NetworkId(s"test$idx"),
      name = name,
      networkCode = "abc",
      eventInfo = EventInfo.Default,
      loginMode = LoginMode.UsernamePassword,
      idHubOrganization = IdHubOrganization(idx, "test")
    )
  }

  val added = Set("viewUIBacktestingComparisonCapability")

  override def beforeEach(): Unit = helper.beforeEachHelper()

  "_0027_add_viewUIBacktestingComparisonCapability_capability" should {

    "add capability to all specified networks" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, added)
      val networkDocs = networksToAdd.map(NetworkFormat.write)
      helper.networksCol.insertMany(networkDocs.toSeq).toFuture.await
      helper.verifyCapabilityAbsent(networksToAdd, added)

      val migration = new _0027_add_viewUIBacktestingComparisonCapability_capability(db)
      migration.apply.await

      helper.verifyCapabilityPresent(networksToAdd, added)
    }

    "don't add to random network" in {
      val testnetwork1 = Network(
        id =  simon.Id.NetworkId("networkIdYes"),
        name = "networkIdYes",
        networkCode = "XYZ",
        eventInfo = EventInfo.Default,
        loginMode = LoginMode.UsernamePassword,
        idHubOrganization = IdHubOrganization(1, "test")
      )

      helper.verifyCapabilityAbsent(helper.allNetworks, added)
      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture.await
      helper.verifyCapabilityAbsent(Set(testnetwork1), added)

      val migration = new _0027_add_viewUIBacktestingComparisonCapability_capability(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(Set(testnetwork1), added)
    }

  }

}
