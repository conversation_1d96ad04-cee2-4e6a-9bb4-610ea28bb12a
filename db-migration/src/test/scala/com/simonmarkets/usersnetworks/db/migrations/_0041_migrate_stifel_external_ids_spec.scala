package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.UserAclField.externalIds
import com.gs.marquee.foundation.util.AwaitTimeout.FutureTimeoutOps
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0041_migrate_stifel_external_ids_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {
  implicit val ec: ExecutionContext = ExecutionContext.global
  System.setProperty("simon.env", "test")

  private val StifelNetworkId = NetworkId("Test Network 1")
  private val OtherNetworkId = NetworkId("Other")
  private val stifelUser = TestUser.apply(
    id = "id_1",
    networkId = StifelNetworkId,
    email = "email_1",
    distributorId = Some("stifel_id_1")
  )
  private val otherUser = TestUser.apply(
    id = "id_2",
    networkId = OtherNetworkId,
    email = "email_2",
    distributorId = Some("not_a_stifel_id")
  )
  private val stifelUser2 = TestUser.apply(
    id = "id_3",
    networkId = StifelNetworkId,
    email = "email_3",
    distributorId = Some("stifel_id_3"),
    externalIds = Seq(ExternalId("stifel", "stifel_id_3"))
  )

  "_0041_migrate_stifel_external_ids" should {

    "only migrate Stifel users without stifel ids" in {

      val migration = new _0041_migrate_stifel_external_ids(db)

      val userInserts: Seq[Document] = Seq(stifelUser, otherUser, stifelUser2).map(UserFormat.write)
      migration.usersColl.insertMany(userInserts).toFuture().await

      migration.apply.await

      whenReady(migration.usersColl.find().toFuture) { userDocs =>

        val migratedUsers = userDocs.map(UserFormat.read)
        val expectedUsers = Seq(
          stifelUser.copy(externalIds = Seq(ExternalId("stifel", "stifel_id_1"))),
          otherUser,
          stifelUser2
        )

        migratedUsers.map(TestUser.cleanDates) should contain theSameElementsAs expectedUsers.map(TestUser.cleanDates)
      }
    }
  }

}
