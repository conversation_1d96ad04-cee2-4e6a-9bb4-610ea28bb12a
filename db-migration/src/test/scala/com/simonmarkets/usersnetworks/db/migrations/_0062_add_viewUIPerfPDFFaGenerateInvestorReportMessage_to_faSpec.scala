package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, NetworkType, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.SimonUICapabilities
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0062_add_viewUIPerfPDFFaGenerateInvestorReportMessage_to_faSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idHubOrganization: IdHubOrganization = IdHubOrganization(1, "idHubOrg")
  val financialAdvisor: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("dummyCapability1"))
  val homeOffice: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, Set("dummyCapability2"))

  private val otherNetwork = Network(
    id = simon.Id.NetworkId("networkId1"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    networkTypes = Some(List(NetworkType.Other)),
    customRolesConfig = Set(financialAdvisor)
  )

  private val everythingNetwork = Network(
    id = simon.Id.NetworkId("networkId2"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    networkTypes = Some(List(NetworkType.Issuer)),
    customRolesConfig = Set(homeOffice, financialAdvisor)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0056_add_SimonUICapabilities.ViewUIPerfPDFFaGenerateInvestorReportMessageCapability_to_issuers_and_wholesalers" should {
    "add SimonUICapabilities.ViewUIPerfPDFFaGenerateInvestorReportMessageCapability to issuers" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, Set(SimonUICapabilities.ViewUIPerfPDFFaGenerateInvestorReportMessageCapability.name))
      val docs = Set(otherNetwork, everythingNetwork).map(NetworkFormat.write).toSeq
      helper.networksCol.insertMany(docs).toFuture().await()
      helper.verifyCapabilityPresent(Seq((otherNetwork, "EqPIPGFA")), Set("dummyCapability1"))
      helper.verifyCapabilityPresent(Seq((everythingNetwork, "EqPIPGFAManager")), Set("dummyCapability2"))

      val migration = new _0062_add_viewUIPerfPDFFaGenerateInvestorReportMessage_to_fa(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((otherNetwork, "EqPIPGFA"), (everythingNetwork, "EqPIPGFA")), Set("dummyCapability1", SimonUICapabilities.ViewUIPerfPDFFaGenerateInvestorReportMessageCapability.name))
      helper.verifyCapabilityAbsent(Seq((everythingNetwork, "EqPIPGFAManager")), Set("dummyCapability1", SimonUICapabilities.ViewUIPerfPDFFaGenerateInvestorReportMessageCapability.name))

      helper.verifyCapabilityAbsent(helper.allNetworks.filterNot(network => Set(helper.faNetwork.id, helper.multiRoleNetwork.id).contains(network.id)),
        Set(SimonUICapabilities.ViewUIPerfPDFFaGenerateInvestorReportMessageCapability.name))
    }
  }
}
