package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.common.User
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import java.time.temporal.ChronoUnit

import scala.concurrent.ExecutionContext

class _0004_admin_users_non_multi_network_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val AdminNetworkId = NetworkId("SIMON Admin")
  private val TrainingNetworkId = NetworkId("a819e947-3b66-4b6d-bceb-25e3561e89d7")
  private val OtherNetworkId = NetworkId("Other")
  private val user1 = TestUser.apply(
    id = "id_1",
    networkId = AdminNetworkId,
    email = "email_1",
    idpLoginId = "not_email"
  )
  private val user2 = TestUser.apply(
    id = "id_2",
    networkId = AdminNetworkId,
    email = "email_2",
    idpLoginId = "not_email"
  )
  private val user3 = TestUser.apply(
    id = "id_3",
    networkId = OtherNetworkId,
    email = "email_3",
    idpLoginId = "not_email"
  )
  private val user4 = TestUser.apply(
    id = "id_4",
    networkId = TrainingNetworkId,
    email = "email_4",
    idpLoginId = "not_email"
  )

  "_4_admin_users_non_multi_network" should {

    "set simon network users idpLoginId == email" in {

      val migration = new _0004_admin_users_non_multi_network(db)

      val userInserts: Seq[Document] = Seq(user1, user2, user3, user4).map(UserFormat.write)
      migration.usersColl.insertMany(userInserts).toFuture().await

      migration.apply.await

      whenReady(migration.usersColl.find().toFuture) { userDocs =>

        val migratedUsers = userDocs.map(UserFormat.read)
        val expectedUsers = Seq(
          user1.copy(idpLoginId = "email_1"),
          user2.copy(idpLoginId = "email_2"),
          user3,
          user4.copy(idpLoginId = "email_4")
        )

        migratedUsers.map(cleanDates) should contain theSameElementsAs expectedUsers.map(cleanDates)
      }
    }
  }

  //there is some loss of precision in date conversions that causes match failures
  private def cleanDates(u: User): User = u.copy(
    createdAt = u.createdAt.truncatedTo(ChronoUnit.HOURS),
    updatedAt = u.updatedAt.truncatedTo(ChronoUnit.HOURS),
    lastVisitedAt = u.lastVisitedAt.map(_.truncatedTo(ChronoUnit.HOURS))
  )

}