package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.{Document}
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0043_remove_duplicate_idpIds_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global
  System.setProperty("simon.env", "test")

  private val OtherNetworkId = NetworkId("Other")

  private val dupUser1 = TestUser.apply(
    id = "duplicateId1",
    networkId = OtherNetworkId,
    idpId = Some("idp_id_1")
  )
  private val dupUser2 = TestUser.apply(
    id = "duplicateId2",
    networkId = OtherNetworkId,
    idpId = Some("idp_id_1")
  )
  private val notDupUser1 = TestUser.apply(
    id = "id_3",
    networkId = OtherNetworkId,
    idpId = Some("idp_id_2")
  )

  "_0043_remove_duplicate_idpIds" should {
    "empty the idpId field for all documents with matching ids" in {

      val migration = new _0043_remove_duplicate_idpIds(db)

      val userInserts: Seq[Document] = Seq(dupUser1, dupUser2, notDupUser1).map(UserFormat.write)
      migration.usersCollection.insertMany(userInserts).toFuture().await

      migration.apply.await

      whenReady(migration.usersCollection.find().toFuture) { userDocs =>
        val dupUsers = userDocs.map(UserFormat.read)
        val expectedUsers = Seq(
          dupUser1.copy(idpId = None),
          dupUser2.copy(idpId = None),
          notDupUser1
        )
        dupUsers.map(TestUser.cleanDates) should contain theSameElementsAs (expectedUsers.map(TestUser.cleanDates))
      }
    }
  }
}