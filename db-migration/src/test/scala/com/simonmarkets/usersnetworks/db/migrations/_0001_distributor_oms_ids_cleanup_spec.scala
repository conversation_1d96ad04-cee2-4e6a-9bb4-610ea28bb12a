package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.ExternalAlias
import com.simonmarkets.db.migration.FutureBlock
import com.simonmarkets.users.common.User
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.{TestNetwork, TestUser}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

import scala.concurrent.ExecutionContext

class _0001_distributor_oms_ids_cleanup_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  //dummy data
  private val distAlias1 = "distAlias1"
  private val distAlias2 = "distAlias2"
  private val omsAlias1 = "omsAlias1"

  private val network1 = TestNetwork(id = "netId2", distributorAlias = Some(ExternalAlias(distAlias1, "")), omsAlias = Some(omsAlias1))
  private val network2 = TestNetwork(id = "netId1", distributorAlias = Some(ExternalAlias(distAlias2, "")))

  private val distributorId1 = "distId1"
  private val distributorId2 = "distId2"
  private val distributorId3 = "distId3"

  private val omsId1 = "omsId1"
  private val omsId2 = "omsId2"

  private val today = LocalDateTime.now
  private val yesterday = today.minusDays(1)

  private val user1 = TestUser.apply(
    id = "id_1",
    networkId = network1.id,
    distributorId = Some(distributorId1),
    omsId = Some(omsId1),
    lastVisitedAt = Some(yesterday)
  )
  private val user2 = TestUser.apply(
    id = "id_2",
    networkId = network1.id,
    distributorId = Some(distributorId1),
    omsId = Some(omsId1),
    lastVisitedAt = Some(today)
  )
  private val user3 = TestUser.apply(
    id = "id_3",
    networkId = network2.id,
    distributorId = Some(distributorId2),
    omsId = Some(omsId2),
    lastVisitedAt = Some(today)
  )
  private val user4 = TestUser.apply(
    id = "id_4",
    networkId = network2.id,
    distributorId = Some(distributorId3),
    omsId = Some(omsId2),
    lastVisitedAt = Some(yesterday)
  )


  "_1_distributor_oms_ids_cleanup" should {

    "remove duplicate oms and distributor ids" in {

      val migration = new _0001_distributor_oms_ids_cleanup(db)

      val userInserts: Seq[Document] = Seq(user1, user2, user3, user4).map(UserFormat.write)
      migration.usersColl.insertMany(userInserts).toFuture().await()

      migration.apply.await()

      whenReady(migration.usersColl.find().toFuture) { userDocs =>

        val migratedUsers = userDocs.map(UserFormat.read)
        val expectedUsers = Seq(
          user1.copy(distributorId = None, omsId = None),
          user2,
          user3,
          user4.copy(omsId = None)
        )

        migratedUsers.map(cleanDates) should contain theSameElementsAs expectedUsers.map(cleanDates)
      }
    }

  }

  //there is some loss of precision in date conversions that causes match failures
  private def cleanDates(u: User): User = u.copy(
    createdAt = u.createdAt.truncatedTo(ChronoUnit.HOURS),
    updatedAt = u.updatedAt.truncatedTo(ChronoUnit.HOURS),
    lastVisitedAt = u.lastVisitedAt.map(_.truncatedTo(ChronoUnit.HOURS))
  )

}