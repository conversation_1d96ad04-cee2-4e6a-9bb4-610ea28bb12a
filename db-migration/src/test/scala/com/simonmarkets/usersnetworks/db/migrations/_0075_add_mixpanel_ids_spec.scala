package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.shared.MaskedId
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0075_add_mixpanel_ids_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val network1 = NetworkId("netId1")

  private val user1 = TestUser.apply(
    id = "id_1",
    networkId = network1,
    maskedIds = Set.empty
  )
  private val user2 = TestUser.apply(
    id = "id_2",
    networkId = network1,
    maskedIds = Set(
      MaskedId(
        target = "NotMix", id = "abcd"
      )
    )
  )
  private val user3 = TestUser.apply(
    id = "id_3",
    networkId = network1,
    maskedIds = Set(
      MaskedId(
        target = "NotMix", id = "abcd"
      ),
      MaskedId(
        target = "MixPanel", id = "abcd3"
      )
    )
  )


  "_0075_add_mixpanel_ids" should {

    "add mixpanel ids to users without them" in {

      val migration = new _0075_add_mixpanel_ids(db)

      val userInserts: Seq[Document] = Seq(user1, user2, user3).map(UserFormat.write)

      migration.users.insertMany(userInserts).toFuture.await

      migration.apply.await

      whenReady(migration.users.find().toFuture) { userDocs =>

        val migratedUsers = userDocs.map(UserFormat.read)

        migratedUsers.map(TestUser.cleanDates) should contain(TestUser.cleanDates(user3))
        migratedUsers.find(_.id == user1.id).get.maskedIds.find(_.target == "MixPanel") should not be empty
        migratedUsers.find(_.id == user2.id).get.maskedIds.find(_.target == "MixPanel") should not be empty
        migratedUsers.find(_.id == user2.id).get.maskedIds.find(_.target == "NotMix") should not be empty
      }
    }

  }

}