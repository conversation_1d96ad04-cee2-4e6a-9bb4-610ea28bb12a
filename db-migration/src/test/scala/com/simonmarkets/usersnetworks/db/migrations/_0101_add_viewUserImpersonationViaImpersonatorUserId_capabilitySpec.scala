package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.UserImpersonationCapabilities
import com.simonmarkets.capabilities.UsersCapabilities.ImpersonateUserViaNetworkCapability
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0101_add_viewUserImpersonationViaImpersonatorUserId_capabilitySpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val addCapabilities: Set[String] = Set("viewUserImpersonationViaImpersonatorUserId")

  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, Set(ImpersonateUserViaNetworkCapability.name))
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set.empty)
  val testAnotherManager: CustomRoleDefinition = CustomRoleDefinition(role = "anotherManager", capabilities = Set.empty)

  private val testNetwork1: Network = Network(
    id = simon.Id.NetworkId("networkId1"),
    name = "networkIdName1",
    networkCode = "networkCode1",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(id = 1, name = "test"),
    customRolesConfig = Set(testFaManager, testFa)
  )

  private val testNetwork2: Network = testNetwork1.copy(customRolesConfig = Set(testAnotherManager, testFa))

  override def beforeEach(): Unit = helper.beforeEachHelper()

  "_0101_add_viewUserImpersonationViaImpersonatorUserId_capabilitySpec" should {

    "ensure viewUserImpersonationViaImpersonatorUserId capability is added where needed" in {
      val docs = Set(testNetwork1, testNetwork2).map(NetworkFormat.write).toList
      helper.networksCol.insertMany(docs).toFuture.await
      helper.verifyCapabilityAbsent(Set(testNetwork1, testNetwork2), addCapabilities)

      val migration = new _0101_add_viewUserImpersonationViaImpersonatorUserId_capability(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(
        networkRoles = Seq((testNetwork1, UserRole.EqPIPGFA.productPrefix), (testNetwork2, testFa.role)),
        capabilities = Set(UserImpersonationCapabilities.ViewUserImpersonationViaImpersonatorUserIdCapability.name))
      helper.verifyCapabilityPresent(
        networkRoles = Seq((testNetwork1, UserRole.EqPIPGFAManager.productPrefix), (testNetwork2, testAnotherManager.role)),
        capabilities = Set(UserImpersonationCapabilities.ViewUserImpersonationViaImpersonatorUserIdCapability.name))
    }
  }
}
