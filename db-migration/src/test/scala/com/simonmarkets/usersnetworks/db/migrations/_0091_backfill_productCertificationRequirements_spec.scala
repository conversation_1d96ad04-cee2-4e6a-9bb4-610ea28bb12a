package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax._
import com.goldmansachs.marquee.pipg.{CertificationAlternativesForProduct, CompensationType, ProductCertificationRequirements}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.shared.ProductType.{Annuities, StructuredProduct, TargetReturnETF}
import com.simonmarkets.usersnetworks.db.migrations.utils.TestNetwork
import com.simonmarkets.shared.ProductType
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.Document
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0091_backfill_productCertificationRequirements_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {
  implicit val ec: ExecutionContext = ExecutionContext.global

  private val siCertificationRequirements1 = CertificationAlternativesForProduct(
    productId = "321", productName = "S1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi")))

  private val siCertificationRequirements2 = CertificationAlternativesForProduct(
    productId = "432", productName = "S2", certificationAlternatives = Seq(Seq("ghi")),
    compensationType = Some(CompensationType.Commission))

  private val siCertificationRequirements3 = CertificationAlternativesForProduct(
    productId = "789", productName = "S3", certificationAlternatives = Seq(Seq("bla")),
    compensationType = Some(CompensationType.Fee), underliers = Some(Seq("und1", "und2")))

  private val annuityCertificationRequirements = CertificationAlternativesForProduct(
    productId = "123", productName = "A1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi")))

  private val definedOutcomeETFCertificationRequirements = CertificationAlternativesForProduct(
    productId = "456", productName = "B1", certificationAlternatives = Seq(Seq("abc"), Seq("def")))

  private val altCertificationRequirements = CertificationAlternativesForProduct(
    productId = "789", productName = "C1", certificationAlternatives = Seq(Seq("abc"), Seq("ghi")))

  private val testNetwork1 = TestNetwork(
    id = "id_1",
    siCertificationRequirements = Seq(siCertificationRequirements1),
    annuityCertificationRequirements = Seq(annuityCertificationRequirements),
    definedOutcomeETFCertificationRequirements = Seq(definedOutcomeETFCertificationRequirements),
    altCertificationRequirements = None,
  )

  private val testNetwork2 = TestNetwork(
    id = "id_2",
    siCertificationRequirements = Seq(siCertificationRequirements1, siCertificationRequirements2, siCertificationRequirements3),
    annuityCertificationRequirements = Seq(annuityCertificationRequirements),
    definedOutcomeETFCertificationRequirements = Seq(definedOutcomeETFCertificationRequirements),
    altCertificationRequirements = None,
  )
  private val testNetwork3 = TestNetwork(
    id = "id_3",
    siCertificationRequirements = Seq(siCertificationRequirements1, siCertificationRequirements2, siCertificationRequirements3),
    annuityCertificationRequirements = Seq(annuityCertificationRequirements),
    definedOutcomeETFCertificationRequirements = Seq(definedOutcomeETFCertificationRequirements),
    altCertificationRequirements = Some(Seq(altCertificationRequirements)),
  )
  private val testNetworkFormat1: Document = NetworkFormat.write(testNetwork1)
  private val testNetworkFormat2: Document = NetworkFormat.write(testNetwork2)
  private val testNetworkFormat3: Document = NetworkFormat.write(testNetwork3)

  private val expectedSIProductTypeReqs = ProductCertificationRequirements(
    productType = StructuredProduct,
    certificationRequirements = Seq(siCertificationRequirements1, siCertificationRequirements2, siCertificationRequirements3)
  )

  private val expectedAnnuityProductTypeReqs = ProductCertificationRequirements(
    productType = Annuities,
    certificationRequirements = Seq(annuityCertificationRequirements)
  )

  private val expectedOutcomeETFProductTypeReqs = ProductCertificationRequirements(
    productType = TargetReturnETF,
    certificationRequirements = Seq(definedOutcomeETFCertificationRequirements)
  )

  private val expectedAltProductTypeReqs = ProductCertificationRequirements(
    productType = ProductType.AlternativeInvestment,
    certificationRequirements = Seq(altCertificationRequirements)
  )

  "_0091_backfill_productCertificationRequirements" should {
    "backfill productTypeCertificationRequirements" in {

      val networkCollection = db.getCollection[Document]("networks_new")

      networkCollection.insertMany(Seq(testNetworkFormat1, testNetworkFormat2, testNetworkFormat3)).toFuture().await()

      val migration = new _0091_backfill_productCertificationRequirements(db)
      migration.apply.await()

      val updatedNetworks = networkCollection.find(Document()).toFuture.await
      updatedNetworks.map(network => {
        val formattedNetwork = NetworkFormat.read(network)
        formattedNetwork.id -> formattedNetwork
      }).toMap shouldBe Map(
        "id_1" -> testNetwork1.copy(
          productTypeCertificationRequirements = Some(Seq(
            ProductCertificationRequirements(
              productType = StructuredProduct,
              certificationRequirements = Seq(siCertificationRequirements1)),
            expectedAnnuityProductTypeReqs,
            expectedOutcomeETFProductTypeReqs)
        )),
        "id_2" -> testNetwork2.copy(
          productTypeCertificationRequirements = Some(Seq(expectedSIProductTypeReqs, expectedAnnuityProductTypeReqs, expectedOutcomeETFProductTypeReqs))),
        "id_3" -> testNetwork3.copy(
          productTypeCertificationRequirements = Some(Seq(expectedSIProductTypeReqs, expectedAnnuityProductTypeReqs, expectedOutcomeETFProductTypeReqs, expectedAltProductTypeReqs)))
      )
    }
  }
}
