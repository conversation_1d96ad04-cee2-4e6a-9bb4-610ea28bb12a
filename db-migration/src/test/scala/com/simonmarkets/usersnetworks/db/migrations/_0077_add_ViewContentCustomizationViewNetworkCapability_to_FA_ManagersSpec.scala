package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.LearnV2ContentCustomizationsCapabilities.ViewContentCustomizationViaNetworkCapability
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import com.simonmarkets.syntax._

import scala.concurrent.ExecutionContext

class _0077_add_ViewContentCustomizationViewNetworkCapability_to_FA_ManagersSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  def contentCustomizationCapabilities: Set[String] = Set(ViewContentCustomizationViaNetworkCapability.name)

  val idHubOrganization: IdHubOrganization = IdHubOrganization(1, "idHubOrg")
  val faManager: CustomRoleDefinition = CustomRoleDefinition("EqPIPGFAManager", Set("someFakeCapability"))
  val fa: CustomRoleDefinition = CustomRoleDefinition("EqPIPGFA", Set("someFakeCapability"))
  val wholesaler: CustomRoleDefinition = CustomRoleDefinition("Wholesaler", Set("someFakeCapability"))
  val issuer: CustomRoleDefinition = CustomRoleDefinition("Issuer", Set("someFakeCapability"))
  val someOtherRole: CustomRoleDefinition = CustomRoleDefinition("someOtherRole", Set("someOtherFakeCapability"))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkId1"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    customRolesConfig = Set(someOtherRole)
  )

  private val testNetwork2 = Network(
    id = simon.Id.NetworkId("networkId2"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    customRolesConfig = Set(faManager, fa, wholesaler, issuer, someOtherRole)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0077_add_ViewContentCustomizationViewNetworkCapability_to_FA_Managers" should {
    "add Content Customization View capabilities to all networks with EqPIPGFAManager roles" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, contentCustomizationCapabilities)
      val docs = Set(testNetwork1, testNetwork2).map(NetworkFormat.write).toSeq
      helper.networksCol.insertMany(docs).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testNetwork1, someOtherRole.role), (testNetwork2, someOtherRole.role)), Set("someOtherFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, faManager.role)), Set("someFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, fa.role)), Set("someFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, wholesaler.role)), Set("someFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, issuer.role)), Set("someFakeCapability"))

      val migration = new _0077_add_ViewContentCustomizationViewNetworkCapability_to_FA_Managers(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testNetwork1, someOtherRole.role), (testNetwork2, someOtherRole.role)), Set("someOtherFakeCapability"))
      helper.verifyCapabilityAbsent(Seq((testNetwork1, someOtherRole.role), (testNetwork2, someOtherRole.role)), contentCustomizationCapabilities)
      helper.verifyCapabilityPresent(Seq((testNetwork2, faManager.role)), contentCustomizationCapabilities)
      helper.verifyCapabilityPresent(Seq((testNetwork2, fa.role)), contentCustomizationCapabilities)
      helper.verifyCapabilityPresent(Seq((testNetwork2, wholesaler.role)), contentCustomizationCapabilities)
      helper.verifyCapabilityPresent(Seq((testNetwork2, issuer.role)), contentCustomizationCapabilities)
    }
  }
}
