package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax._
import com.simonmarkets.usersnetworks.db.migrations._0006_duplicate_tracks_cleanup._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.{Document, MongoCollection}
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0006_duplicate_tracks_cleanup_spec extends WordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_6_duplicate_tracks_cleanup" should {
    "update networks" in {
      def learnTracks(trackIds: String*): Seq[LearnTrack] = trackIds.map(LearnTrack(_, isActive = true))

      System.setProperty("simon.env", "test")

      val net1 = Network(
        id = "net1",
        learnTracksV2 = learnTracks("track-1", "track-2", "track-3", "tid1", "tid2"),
        siCertificationRequirements = Seq(CertificationAlternativesForProduct(
          "si", "si", Seq(
            Seq("track-1", "tid1"),
            Seq("track-2", "track-3", "tid2"),
          )
        )),
        annuityCertificationRequirements = Seq(CertificationAlternativesForProduct(
          "a1", "a1", Seq(
            Seq("tid3"),
            Seq("track-5"),
            Seq("track-6"),
          )
        )),
        definedOutcomeETFCertificationRequirements = Seq.empty,
        altCertificationRequirements = None,
      )
      val net2 = Network(
        id = "net2",
        learnTracksV2 = learnTracks("track-5", "track-6"),
        siCertificationRequirements = Seq.empty,
        annuityCertificationRequirements = Seq.empty,
        definedOutcomeETFCertificationRequirements = Seq.empty,
        Some(Seq(CertificationAlternativesForProduct("alt", "alt", Seq(Seq("track-5", "track-6")))))
      )
      val net3 = Network(
        id = "net3",
        learnTracksV2 = learnTracks("tid4", "tid5"),
        siCertificationRequirements = Seq(CertificationAlternativesForProduct("si", "si", Seq(Seq("tid4", "tid5")))),
        annuityCertificationRequirements = Seq.empty,
        definedOutcomeETFCertificationRequirements = Seq.empty,
        altCertificationRequirements = None,
      )

      val networkCollection: MongoCollection[Network] = db.getCollection[Network]("networks_new").withCodecRegistry(networkCodecRegistry)
      networkCollection.insertMany(Seq(net1, net2, net3)).toFuture.await

      val migration = new _0006_duplicate_tracks_cleanup(db)
      migration.apply.await

      val networks = networkCollection.find(Document()).toFuture.await
      networks.map(n => n.id -> n).toMap shouldBe Map(
        "net1" -> net1.copy(
          learnTracksV2 = learnTracks("track-1", "tid1", "tid2"),
          siCertificationRequirements = Seq(CertificationAlternativesForProduct("si", "si", Seq(Seq("track-1", "tid1"), Seq("track-1", "tid2")))),
          annuityCertificationRequirements = Seq(CertificationAlternativesForProduct("a1", "a1", Seq(Seq("tid3"), Seq("track-5")))),
        ),
        "net2" -> net2.copy(
          learnTracksV2 = learnTracks("track-5"),
          altCertificationRequirements = Some(Seq(CertificationAlternativesForProduct("alt", "alt", Seq(Seq("track-5")))))
        ),
        "net3" -> net3
      )
    }
  }
}
