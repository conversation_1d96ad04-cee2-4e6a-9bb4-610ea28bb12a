package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0035_remove_refreshRfqQuoteCapabilitiesSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach{
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  def oldRfqCapabilities: Set[String] = Set(
    "refreshRfqQuoteViaOwner",
    "refreshRfqQuoteViaNetwork",
    "refreshRfqQuoteViaNetworkIssuerPurview",
    "refreshRfqIdeaQuoteViaOwner",
    "refreshRfqIdeaQuoteViaNetwork",
    "refreshRfqIdeaQuoteViaNetworkIssuerPurview",
  )

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("EqPIPGFA", "refreshRfqQuoteViaOwner", "EqPIPGFAManager", "refreshRfqQuoteViaNetworkIssuerPurview"))
  val testFa2: CustomRoleDefinition = CustomRoleDefinition(UserRole.Issuer.productPrefix, Set("Issuer", "refreshRfqQuoteViaNetworkIssuerPurview", "refreshRfqIdeaQuoteViaNetworkIssuerPurview", "common-capability2", "issuer-capability"))

  private val testnetwork1 = Network(
    id =  simon.Id.NetworkId("networkIdFmA"),
    name = "networkIdWithFMRoleA",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFaManager)
  )

  private val testnetwork2 = Network(
    id =  simon.Id.NetworkId("networkIdIssuer"),
    name = "networkIdWithIssuerRole",
    networkCode = "ZBA",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa2)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0035_remove_refreshRfqQuoteCapabilitiesSpec" should {
    "remove refreshRfq/refreshRfqIdea from all networks" in {

      helper.verifyCapabilityAbsent(helper.allNetworks, oldRfqCapabilities)
      val docs = Set(testnetwork1, testnetwork2).map(NetworkFormat.write).toSeq
      helper.networksCol.insertMany(docs).toFuture().await()
      helper.verifyCapabilityPresent(Set(testnetwork1), Set("refreshRfqQuoteViaOwner", "refreshRfqQuoteViaNetworkIssuerPurview"))
      helper.verifyCapabilityPresent(Set(testnetwork2), Set("refreshRfqQuoteViaNetworkIssuerPurview", "refreshRfqIdeaQuoteViaNetworkIssuerPurview"))

      val migration = new _0035_remove_refreshRfqQuoteCapabilities(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(helper.allNetworks ++ Set(testnetwork1, testnetwork2), oldRfqCapabilities)
    }
  }
}
