package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.networks.ExternalId
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0033_migrate_fidelity_ids_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global
  System.setProperty("simon.env", "test")

  private val FidelityNetworkId = NetworkId("Test Network 1")
  private val OtherNetworkId = NetworkId("Other")
  private val fidelityUser = TestUser.apply(
    id = "id_1",
    networkId = FidelityNetworkId,
    email = "email_1",
    distributorId = Some("ws_id_1")
  )
  private val otherUser = TestUser.apply(
    id = "id_2",
    networkId = OtherNetworkId,
    email = "email_2",
    distributorId = Some("not_a_ws_id")
  )
  private val fidelityUser2 = TestUser.apply(
    id = "id_3",
    networkId = FidelityNetworkId,
    email = "email_3",
    distributorId = Some("ws_id_3"),
    externalIds = Seq(ExternalId("wealthscape", "ws_id_3"))
  )

  "_0033_migrate_fidelity_ids" should {

    "only migrate fidelity users without wealthscape ids" in {

      val migration = new _0033_migrate_fidelity_ids(db)

      val userInserts: Seq[Document] = Seq(fidelityUser, otherUser, fidelityUser2).map(UserFormat.write)
      migration.usersColl.insertMany(userInserts).toFuture().await

      migration.apply.await

      whenReady(migration.usersColl.find().toFuture) { userDocs =>

        val migratedUsers = userDocs.map(UserFormat.read)
        val expectedUsers = Seq(
          fidelityUser.copy(externalIds = Seq(ExternalId("wealthscape", "ws_id_1"))),
          otherUser,
          fidelityUser2
        )

        migratedUsers.map(TestUser.cleanDates) should contain theSameElementsAs expectedUsers.map(TestUser.cleanDates)
      }
    }
  }

}
