package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.Document
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0068_add_idType_to_paidFeatures_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0068_add_idType_to_paidFeatures_spec" should {

    lazy val col = db.getCollection("users.paidFeatures")

    "apply migration and add idType UserId" in {
      // Insert the initial document
      val documents = Seq(
        Document("_id" -> "6581bcb2850d114a73e2a1d1", "userId" -> "00u1c1q9y2omuBAga0h8", "version" -> 1),
        Document("_id" -> "23f23g23b3g3efwfewef23", "userId" -> "v2f3rvsf24f2fd2d2", "version" -> 1)
      )
      col.insertMany(documents).toFuture().await()
      // Apply migration
      val migration = new _0068_add_idType_to_paidFeatures(db)
      migration.apply.await()

      val updatedDocument = col.find(Document("_id" -> "6581bcb2850d114a73e2a1d1")).headOption().await()
      updatedDocument should be(
        Some(Document(
          "_id" -> "6581bcb2850d114a73e2a1d1",
          "userId" -> "00u1c1q9y2omuBAga0h8",
          "idType" -> "UserId",
          "version" -> 1
        )))

      val updatedDocumentTwo = col.find(Document("_id" -> "23f23g23b3g3efwfewef23")).headOption().await()
      updatedDocumentTwo should be(
        Some(Document(
          "_id" -> "23f23g23b3g3efwfewef23",
          "userId" -> "v2f3rvsf24f2fd2d2",
          "idType" -> "UserId",
          "version" -> 1
        )))
    }
  }
}