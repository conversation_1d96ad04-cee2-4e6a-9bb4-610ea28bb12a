package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax._
import com.simonmarkets.usersnetworks.db.data.networkEntity.{IssuerLookup, IssuerMapping}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import com.simonmarkets.usersnetworks.db.migrations._0009_networkEntities_populating_SI._
import org.mongodb.scala.{Document, MongoCollection}
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0009_networkEntities_populating_SISpec extends WordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_9_networkEntities_populating_SISpec" should {
    "update networks" in {
      val networkEntitiesCollection: MongoCollection[NetworkEntities] = db.getCollection[NetworkEntities]("networkEntities").withCodecRegistry(networkEntityCodecs)

      val migration = new _0009_networkEntities_populating_SI(db)
      migration.apply.await(15.seconds)

      val networkEntities = networkEntitiesCollection.find(Document()).toFuture.await(15.seconds)

      networkEntities.size shouldBe IssuerMapping.siEntityToProdNetworkIdMapping.size

      val actualGS = IssuerMapping.issuerKeysByEntity("Goldman Sachs").flatMap(key => IssuerLookup.lookup.get(key).map(issuerInfoToEntity)).toSet
      val migrationGS = networkEntities.find(entity => entity.networkId == "Goldman Sachs")
      migrationGS.isDefined shouldBe true
      migrationGS.map(networkEntity => {
        networkEntity.entities shouldBe actualGS
        networkEntity.networkId shouldBe "Goldman Sachs"
      })

      val actualMontreal = IssuerMapping.issuerKeysByEntity("Bank of Montreal").flatMap(key => IssuerLookup.lookup.get(key).map(issuerInfoToEntity)).toSet
      val migrationMontreal = networkEntities.find(entity => entity.networkId == "cb7451d3-e0bb-4fb6-a43a-4921af596025")
      migrationMontreal.isDefined shouldBe true
      migrationMontreal.map(networkEntity => {
        networkEntity.entities shouldBe actualMontreal
        networkEntity.networkId shouldBe "cb7451d3-e0bb-4fb6-a43a-4921af596025"
      })

      //config checks to make sure new entities are defined in all relevant maps
      IssuerMapping.issuerKeysByEntity.size shouldBe IssuerMapping.siEntityToQaNetworkIdMapping.size
      IssuerMapping.issuerKeysByEntity.size shouldBe IssuerMapping.siEntityToProdNetworkIdMapping.size
    }
  }
}
