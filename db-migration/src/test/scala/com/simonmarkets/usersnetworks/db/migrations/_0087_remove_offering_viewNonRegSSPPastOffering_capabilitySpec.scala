package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0087_remove_offering_viewNonRegSSPPastOffering_capabilitySpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val removeCapabilities = Set("viewNonRegSSPPastOffering")

  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, removeCapabilities + "famanagerCapability")

  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, removeCapabilities + "faCapability")

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(1, "test"),
    customRolesConfig = Set(testFaManager, testFa)
  )

  override def beforeEach(): Unit = helper.beforeEachHelper()

  "_0087_remove_offering_viewNonRegSSPPastOffering_capabilitySpec" should {

    "remove redundant offering capabilities" in {
      val doc = NetworkFormat.write(testNetwork1)
      helper.networksCol.insertOne(doc).toFuture.await
      helper.verifyCapabilityPresent(Set(testNetwork1), removeCapabilities)

      val migration = new _0087_remove_offering_viewNonRegSSPPastOffering_capability(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(Set(testNetwork1), removeCapabilities)
    }
  }
}
