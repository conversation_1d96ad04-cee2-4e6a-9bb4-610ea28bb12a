package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.LifecycleEventsCapabilities._
import com.simonmarkets.capabilities.HoldingsCapabilities
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0099_add_lifecycle_events_capabilities_spec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set(HoldingsCapabilities.ViewHoldingViaLocation))
  val editLifecycleEvents: Set[String] = Set(EditLifecycleEventViaPayOffCapability.name, ViewLifecycleEventViaPayOffCapability.name)

  private val testnetwork1 = Network(
    id = simon.Id.NetworkId("network1"),
    name = "network1",
    networkCode = "N1",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa),
    payoffEntitlementsV2 = Map()
  )

  val network1Roles: Seq[(Network, String)] = Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix))

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0099_add_lifecycle_events_capabilities" should {
    "add editLifecycleEventViaPayOffEntitlement and viewLifecycleEventViaPayOffEntitlement capability to all roles" in {
      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityAbsent(network1Roles, editLifecycleEvents)
      val migration = new _0099_add_lifecycle_events_capabilities(db)
      migration.apply.await
      helper.verifyCapabilityPresent(network1Roles, editLifecycleEvents)
    }
  }
}
