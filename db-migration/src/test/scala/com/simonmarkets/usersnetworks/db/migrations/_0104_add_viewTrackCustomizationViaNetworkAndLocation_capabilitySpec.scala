package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.LearnV2TrackCustomizationsCapabilities._
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0104_add_viewTrackCustomizationViaNetworkAndLocation_capabilitySpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  private val oldTrackCustomizationCapabilities: Set[String] = Set(
    ViewTrackCustomizationViaNetworkCapability.name
  )

  def trackCustomizationCapabilities: Set[String] = Set(
    ViewTrackCustomizationViaNetworkAndLocationCapability.name
  )

  val idHubOrganization: IdHubOrganization = IdHubOrganization(1, "idHubOrg")
  val withOldTrackCustomizationCapabilities: CustomRoleDefinition = CustomRoleDefinition("withOldTrackCustomizationCapabilities", oldTrackCustomizationCapabilities)
  val withoutOldTrackCustomizationCapabilities: CustomRoleDefinition = CustomRoleDefinition("withoutOldTrackCustomizationCapabilities", Set("someFakeCapability"))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkId1"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    customRolesConfig = Set(withoutOldTrackCustomizationCapabilities)
  )

  private val testNetwork2 = Network(
    id = simon.Id.NetworkId("networkId2"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    customRolesConfig = Set(withOldTrackCustomizationCapabilities, withoutOldTrackCustomizationCapabilities)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0104_add_viewTrackCustomizationViaNetworkAndLocation_capability" should {
    "add viewTrackCustomizationViaNetworkAndLocation capability to existing roles with existing ViewTrackCustomizationViaNetwork capability" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, trackCustomizationCapabilities)
      val docs = Set(testNetwork1, testNetwork2).map(NetworkFormat.write).toSeq
      helper.networksCol.insertMany(docs).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testNetwork1, withoutOldTrackCustomizationCapabilities.role),(testNetwork2, withoutOldTrackCustomizationCapabilities.role)), Set("someFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, withOldTrackCustomizationCapabilities.role)), oldTrackCustomizationCapabilities)

      val migration = new _0104_add_viewTrackCustomizationViaNetworkAndLocation_capability(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testNetwork1, withoutOldTrackCustomizationCapabilities.role),(testNetwork2, withoutOldTrackCustomizationCapabilities.role)), Set("someFakeCapability"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, withOldTrackCustomizationCapabilities.role)), trackCustomizationCapabilities)
    }
  }
}
