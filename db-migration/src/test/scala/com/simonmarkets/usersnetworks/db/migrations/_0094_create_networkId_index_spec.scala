package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0094_create_networkId_index_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0094_create_networkId_index" should {

    "create indexes" in {

      val migration = new _0094_create_networkId_index(db)
      migration.apply.await()

      whenReady(migration.col.getIndexes) { indexes =>
        indexes should contain(Index("networkId_1", List(IndexedField("networkId")), unique = true))
      }
    }
  }
}
