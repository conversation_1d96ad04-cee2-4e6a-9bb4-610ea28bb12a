package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import com.simonmarkets.usersnetworks.db.migrations._0019_add_rejection_reasons_kovack._
import org.mongodb.scala.{Document, MongoCollection}
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0019_add_rejection_reasons_kovack_spec extends WordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global
  val rejectionReasonsWithGroup = List(
    "Client’s Investment Experience Not Aligned|Suitability Issue",
    "Client’s Annual Income Not Aligned|Suitability Issue",
    "Client’s Net Worth/Liquid Net Worth Not Aligned|Suitability Issue",
    "Client’s Investment Objectives/Risk Tolerance Not Aligned|Suitability Issue",
    "Client’s Liquidity Needs Not Aligned|Suitability Issue",
    "Client’s Age Not Aligned|Suitability Issue",
    "Client Acknowledgement/Disclosure Documents Not on File|NIGO",
    "Invalid Account Number|NIGO",
    "Account Restricted|NIGO",
    "Possible Duplicate|NIGO",
    "Advisor Certification Not Complete|NIGO",
    "Trade Desk Rejected|NIGO"
  )

  "_0015_add_rejection_reasons_kovack_spec" should {
    "add rejection reasons" in {
      val rejectionReasonsCollection: MongoCollection[RejectionReasons] = db.getCollection[RejectionReasons]("rejectionReasons").withCodecRegistry(rejectionReasonsCodec)

      val migration = new _0019_add_rejection_reasons_kovack(db)
      migration.apply.await

      val rejectionReasons = rejectionReasonsCollection.find(Document()).toFuture.await

      rejectionReasons.size shouldBe 1

      val kovackRejectionReason = rejectionReasons.head

      kovackRejectionReason.network shouldBe "66a9e263-d4c4-4485-b798-ef75bc3b5a14"

      val reasons = kovackRejectionReason.reasons

      reasons.size shouldBe 12

      reasons.foreach(res => {
        res.reason should not be null
        res.group should not be null
        rejectionReasonsWithGroup should contain(res.reason + "|" + res.group)
        res.userCreated shouldBe "DBMigration"
        res.userLastUpdated shouldBe "DBMigration"
        res.version shouldBe 1
        res.id should not be null
        }
      )
    }
  }
}
