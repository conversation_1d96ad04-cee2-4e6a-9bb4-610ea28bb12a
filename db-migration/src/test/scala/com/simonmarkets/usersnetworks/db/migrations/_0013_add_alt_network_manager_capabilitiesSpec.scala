package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.AltOfferingCapabilities.{ViewAltOfferingViaNetworkManager, ApproveAltOfferingViaNetworkManager, ViewAltOfferingViaPurviewNetworkManager, ApproveAltOfferingViaPurviewNetworkManager}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations._0013_add_alt_network_manager_capabilities.ALTS_FA_MANAGER_ROLE
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0013_add_alt_network_manager_capabilitiesSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach{
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val altsFAManager: CustomRoleDefinition = CustomRoleDefinition(ALTS_FA_MANAGER_ROLE, Set("someCapability"))
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("someOtherCapability"))


  private val testnetwork1 = Network(
    id =  simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(altsFAManager, testFa)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0013_add_alt_network_manager_capabilitiesSpec" should {
    "add desired capabilities to specific roles" in {

      helper.verifyCapabilityAbsent(helper.allNetworks, Set(ViewAltOfferingViaNetworkManager, ApproveAltOfferingViaNetworkManager))
      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testnetwork1, ALTS_FA_MANAGER_ROLE)), Set("someCapability"))

      val migration = new _0013_add_alt_network_manager_capabilities(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testnetwork1, ALTS_FA_MANAGER_ROLE)), Set("someCapability", ViewAltOfferingViaNetworkManager, ApproveAltOfferingViaNetworkManager))
      helper.verifyCapabilityAbsent(Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix)), Set(ViewAltOfferingViaNetworkManager, ApproveAltOfferingViaNetworkManager, ViewAltOfferingViaPurviewNetworkManager, ApproveAltOfferingViaPurviewNetworkManager))
    }
  }
}
