package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.usersnetworks.db.migrations._0036_remove_productNomenclature_short_JPMPB.ProductNomenclature
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import java.time.{LocalDate, ZoneOffset}

import scala.concurrent.ExecutionContext

class _0036_remove_productNomenclature_short_JPMPBSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  val toDeleteJPMPB: ProductNomenclature = ProductNomenclature(
    id = "id1",
    networkId = "JP Morgan Private Bank",
    userCreated = "userId1",
    timeCreated = LocalDate.of(2022, 10, 26).atStartOfDay().toInstant(ZoneOffset.UTC)
  )

  val toKeepJPMPB: ProductNomenclature = ProductNomenclature(
    id = "id2",
    networkId = "JP Morgan Private Bank",
    userCreated = "userId1",
    timeCreated = LocalDate.of(2022, 10, 28).atStartOfDay().toInstant(ZoneOffset.UTC)
  )

  val toIgnore: ProductNomenclature = ProductNomenclature(
    id = "id3",
    networkId = "TestNetwork1",
    userCreated = "userId1",
    timeCreated = LocalDate.of(2022, 10, 26).atStartOfDay().toInstant(ZoneOffset.UTC)
  )

  "_0036_remove_productNomenclature_short_JPMPB" should {

    "only migrate fidelity users" in {
      val migration = new _0036_remove_productNomenclature_short_JPMPB(db)

      migration.productNomenclatureColl.insertMany(Seq(toDeleteJPMPB, toKeepJPMPB, toIgnore)).toFuture().await
      migration.apply.await

      whenReady(migration.productNomenclatureColl.find().toFuture) { docs =>
        docs.size shouldBe 2
        docs should contain theSameElementsAs Seq(toKeepJPMPB, toIgnore)
      }
    }
  }

}
