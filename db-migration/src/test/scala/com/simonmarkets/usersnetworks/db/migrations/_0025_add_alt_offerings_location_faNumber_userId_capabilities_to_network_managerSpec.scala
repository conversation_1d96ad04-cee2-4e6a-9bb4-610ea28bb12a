package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.AltOfferingCapabilities.{ViewApprovedAltOfferingViaFaNumberCapability, ViewApprovedAltOfferingViaLocationCapability, ViewApprovedAltOfferingViaUserCapability, ViewClosedAltOfferingViaFaNumberCapability, ViewClosedAltOfferingViaLocationCapability, ViewClosedAltOfferingViaUserCapability, ViewPendingAltOfferingViaFaNumberCapability, ViewPendingAltOfferingViaLocationCapability, ViewPendingAltOfferingViaUserCapability}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import com.simonmarkets.syntax._
import com.simonmarkets.usersnetworks.db.migrations._0025_add_alt_offerings_location_faNumber_userId_capabilities_to_network_manager.ALTS_FA_MANAGER_ROLE

import scala.concurrent.ExecutionContext

class _0025_add_alt_offerings_location_faNumber_userId_capabilities_to_network_managerSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val altsFAManager: CustomRoleDefinition = CustomRoleDefinition(ALTS_FA_MANAGER_ROLE, Set("someCapability"))
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("someOtherCapability"))
  val capabilities = Set(
    ViewApprovedAltOfferingViaUserCapability.name,
    ViewPendingAltOfferingViaUserCapability.name,
    ViewClosedAltOfferingViaUserCapability.name,
    ViewApprovedAltOfferingViaLocationCapability.name,
    ViewPendingAltOfferingViaLocationCapability.name,
    ViewClosedAltOfferingViaLocationCapability.name,
    ViewApprovedAltOfferingViaFaNumberCapability.name,
    ViewPendingAltOfferingViaFaNumberCapability.name,
    ViewClosedAltOfferingViaFaNumberCapability.name
  )
  private val testnetwork1 = Network(
    id =  simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(altsFAManager, testFa)
  )
  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0025_add_alt_offerings_location_faNumber_userId_capabilities_to_network_manager" should {
    "add desired capabilities to fa manager role" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, capabilities)
      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture().await
      helper.verifyCapabilityPresent(Seq((testnetwork1, ALTS_FA_MANAGER_ROLE)),Set("someCapability"))

      val migration = new _0025_add_alt_offerings_location_faNumber_userId_capabilities_to_network_manager(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testnetwork1, ALTS_FA_MANAGER_ROLE)), Set("someCapability") ++ capabilities)
      val otherRole = UserRole.EqPIPGFA.productPrefix
      helper.verifyCapabilityAbsent(Seq((testnetwork1, otherRole)), capabilities)
    }
  }
}
