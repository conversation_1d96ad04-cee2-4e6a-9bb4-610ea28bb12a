package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.IdHubOrganization
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.{Document, MongoCollection}
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0051_remove_canOptInPricingFeedbackSpec extends WordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0051_remove_canOptInPricingFeedback" should {
    "update networks" in {
      
      val idHubOrg = IdHubOrganization(1, "id")
      
      val withCapability = Map(
        "canOptInPricingFeedback" -> List("view"),
        "blah" -> List("view")
      )

      val updatedCapabilities = Map(
        "blah" -> List("view")
      )

      val withoutCapability = Map(
        "someCapability" -> List("view"),
        "blah" -> List("view")
      )

      val net1 = Network(
        id = simon.Id.NetworkId("net1"),
        name = "networkIdWithFMRole",
        networkCode = "ABC",
        eventInfo = EventInfo.Default,
        loginMode = LoginMode.UsernamePassword,
        idHubOrganization = idHubOrg,
        capabilities = withCapability
      )

      val net2 = Network(
        id = simon.Id.NetworkId("net2"),
        name = "networkIdWithFMRole",
        networkCode = "ABC",
        eventInfo = EventInfo.Default,
        loginMode = LoginMode.UsernamePassword,
        idHubOrganization = idHubOrg,
        capabilities = withoutCapability
      )

      val net3 = Network(
        id = simon.Id.NetworkId("net3"),
        name = "networkIdWithFMRole",
        networkCode = "ABC",
        eventInfo = EventInfo.Default,
        loginMode = LoginMode.UsernamePassword,
        idHubOrganization = idHubOrg
      )

      val networkCollection: MongoCollection[Document] = db.getCollection[Document]("networks_new").withCodecRegistry(DEFAULT_CODEC_REGISTRY)
      networkCollection.insertMany(Seq(net1, net2, net3).map(NetworkFormat.write)).toFuture.await

      val migration = new _0051_remove_canOptInPricingFeedback(db)
      migration.apply.await

      val networks = networkCollection.find(Document()).toFuture.await
      networks.map(n => {
        val formattedNetwork = NetworkFormat.read(n)
        formattedNetwork.id -> formattedNetwork
      }).toMap shouldBe Map(
        "net1" -> net1.copy(
          capabilities = updatedCapabilities
        ),
        "net2" -> net2,
        "net3" -> net3
      )
    }
  }
}
