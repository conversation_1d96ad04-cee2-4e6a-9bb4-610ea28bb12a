package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0021_networks_snapshot_indexSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0021_networks_snapshot_index" should {

    lazy val networksSnapshotColl = db.getCollection("networks_new.snapshots")

    "create indexes" in {

      val migration = new _0021_networks_snapshot_index(db)
      migration.apply.await()

      whenReady(networksSnapshotColl.getIndexes) { indexes =>
        indexes should contain(Index("id_1", List(IndexedField("id"))))
      }
    }
  }
}