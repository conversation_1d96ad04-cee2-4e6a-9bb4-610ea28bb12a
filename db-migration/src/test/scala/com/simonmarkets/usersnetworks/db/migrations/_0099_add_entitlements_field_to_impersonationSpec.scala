package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.mongodb.scala.model.Filters
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0099_add_entitlements_field_to_impersonationSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0099_add_entitlements_field_to_impersonationSpec" should {
    "add entitlements field to impersonation" in {
      val coll = db.getCollection[Document]("users.impersonation")
      val migration = new _0099_add_entitlements_field_to_impersonation(db)

      val docs = (0 to 2).map(i => Document("id" -> i))

      coll.insertMany(docs).toFuture().await
      migration.apply.await

      val numberOfBackfilledImpersonations = coll
        .countDocuments(Filters.exists("entitlements", exists = true))
        .toFuture()
        .await

      numberOfBackfilledImpersonations shouldBe 3
    }
  }
}
