package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.CustomRoleDefinition
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.{AccountsCapabilities, OrderAccountsCapabilities}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0080_add_OrderAccountsCapabilitiesSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val testOneCapabilitySet = Set(AccountsCapabilities.ViewAccountViaOwnerCapability.name)
  val orderAccountsOneCapabilitySet = Set(OrderAccountsCapabilities.ViewOrderAccountViaOwner)
  val testTwoCapabilitySet = Set(AccountsCapabilities.ViewAccountViaOwner, AccountsCapabilities.EditAccountViaNetwork)
  val orderAccountsTwoCapabilitySet = Set(OrderAccountsCapabilities.ViewOrderAccountViaOwner, OrderAccountsCapabilities.EditOrderAccountViaNetwork)
  val testThreeCapabilitySet = Set(AccountsCapabilities.ViewAccountViaLocation, AccountsCapabilities.UploadAccountsSnapshotViaNetwork,
    AccountsCapabilities.ViewAccountViaNetwork)
  val orderAccountsThreeCapabilitySet = Set(OrderAccountsCapabilities.ViewOrderAccountViaLocation, OrderAccountsCapabilities.UploadOrderAccountsSnapshotViaNetwork,
    OrderAccountsCapabilities.ViewOrderAccountViaNetwork)
  val testAllCapabilitySet = AccountsCapabilities.DetailedViewCapabilities.map(_.name) ++ AccountsCapabilities.DetailedEditCapabilities.map(_.name) ++
    AccountsCapabilities.DetailedUploadAccountsSnapshotCapabilities.map(_.name)
  val orderAccountsAllCapabilitiesSet = Set(OrderAccountsCapabilities.ViewOrderAccountViaNetwork,
    OrderAccountsCapabilities.ViewOrderAccountViaLocation,
    OrderAccountsCapabilities.ViewOrderAccountViaFaNumber,
    OrderAccountsCapabilities.ViewOrderAccountViaPurviewedDomain,
    OrderAccountsCapabilities.ViewOrderAccountViaLocationHierarchy,
    OrderAccountsCapabilities.ViewOrderAccountViaDistributorId,
    OrderAccountsCapabilities.ViewOrderAccountViaOwner,
    OrderAccountsCapabilities.EditOrderAccountViaNetwork,
    OrderAccountsCapabilities.UploadOrderAccountsSnapshotViaNetwork)

  val testOneRole: CustomRoleDefinition = CustomRoleDefinition("one-capability", testOneCapabilitySet)
  val testTwoRole: CustomRoleDefinition = CustomRoleDefinition("two-capability", testTwoCapabilitySet)
  val testThreeRole: CustomRoleDefinition = CustomRoleDefinition("three-capability", testThreeCapabilitySet)
  val testAllRole: CustomRoleDefinition = CustomRoleDefinition("all-capability", testAllCapabilitySet)

  private lazy val testNetwork1 = helper.noConfigNetwork.copy(id = NetworkId("testNetwork1"), name = "testNetwork1", customRolesConfig = Set(testOneRole))
  private lazy val testNetwork2 = helper.noConfigNetwork.copy(id = NetworkId("testNetwork2"), name = "testNetwork2",
    customRolesConfig = Set(testOneRole, testTwoRole, testThreeRole, testAllRole))

  override def beforeEach(): Unit = helper.beforeEachHelper()

  "_0080_add_OrderAccountsCapabilities" should {

    "add OrderAccountsCapabilities to roles" in {
      val doc1 = NetworkFormat.write(testNetwork1)
      val doc2 = NetworkFormat.write(testNetwork2)
      lazy val doc3 = NetworkFormat.write(helper.noConfigNetwork)
      helper.networksCol.insertMany(Seq(doc1, doc2, doc3)).toFuture.await
      helper.verifyCapabilityAbsent(networkRoles = Seq((testNetwork1, "one-capability")), orderAccountsOneCapabilitySet)
      helper.verifyCapabilityAbsent(networkRoles = Seq((testNetwork2, "two-capability")), orderAccountsTwoCapabilitySet)
      helper.verifyCapabilityAbsent(networkRoles = Seq((testNetwork2, "three-capability")), orderAccountsThreeCapabilitySet)
      helper.verifyCapabilityAbsent(networkRoles = Seq((testNetwork2, "all-capability")), orderAccountsAllCapabilitiesSet)


      val migration = new _0080_add_OrderAccountsCapabilities(db)
      migration.apply.await

      helper.verifyCapabilityPresent(networkRoles = Seq((testNetwork1, "one-capability")), orderAccountsOneCapabilitySet)
      helper.verifyCapabilityAbsent(networkRoles = Seq((testNetwork1, "one-capability")), orderAccountsAllCapabilitiesSet -- orderAccountsOneCapabilitySet)

      helper.verifyCapabilityPresent(networkRoles = Seq((testNetwork2, "two-capability")), orderAccountsTwoCapabilitySet)
      helper.verifyCapabilityAbsent(networkRoles = Seq((testNetwork1, "two-capability")), orderAccountsAllCapabilitiesSet -- orderAccountsTwoCapabilitySet)

      helper.verifyCapabilityPresent(networkRoles = Seq((testNetwork2, "three-capability")), orderAccountsThreeCapabilitySet)
      helper.verifyCapabilityAbsent(networkRoles = Seq((testNetwork1, "threee-capability")), orderAccountsAllCapabilitiesSet -- orderAccountsThreeCapabilitySet)

      helper.verifyCapabilityPresent(networkRoles = Seq((testNetwork2, "all-capability")), orderAccountsAllCapabilitiesSet)

      helper.verifyCapabilityAbsent(Set(helper.noConfigNetwork), orderAccountsAllCapabilitiesSet)

    }
  }

}
