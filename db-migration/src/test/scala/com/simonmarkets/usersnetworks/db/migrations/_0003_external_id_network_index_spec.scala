package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0003_external_id_network_index_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_3_external_id_network_index" should {

    lazy val externalIdTypeColl = db.getCollection("users.externalIdTypes")

    "create indexes" in {

      val migration = new _0003_external_id_network_index(db)
      migration.apply.await()

      whenReady(externalIdTypeColl.getIndexes) { indexes =>
        indexes should contain(Index("matchableNetworks_1", List(IndexedField("matchableNetworks"))))
      }
    }
  }
}
