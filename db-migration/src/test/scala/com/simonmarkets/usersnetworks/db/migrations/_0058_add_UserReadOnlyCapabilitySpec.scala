package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.db.migration.FutureBlock
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0058_add_UserReadOnlyCapabilitySpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val networkId = NetworkId("networkId")

  private val user1 = TestUser.apply(
    id = "id_1",
    networkId = networkId,
  )

  private val user2 = TestUser.apply(
    id = "id_2",
    networkId = networkId,
  )

  private val user3 = TestUser.apply(
    id = "id_3",
    networkId = networkId,
  )

  "_0058_add_UserReadOnlyCapability" should {

    "add ReadOnlyAdmin entitlement to users" in {

      val migration = new _0058_add_UserReadOnlyCapabilities_capability(db)

      val userInserts: Seq[Document] = Seq(user1, user2, user3).map(UserFormat.write)
      migration.usersCollection.insertMany(userInserts).toFuture().await()

      migration.apply.await()
      val updatedUsers = migration.usersCollection.find().toFuture
      whenReady(updatedUsers) { userDocs =>

        val migratedUsers = userDocs.map(UserFormat.read)

        val hasReadOnlyAdminCapability = migratedUsers.map { user =>
          user.entitlements.contains(Capabilities.ReadOnlyAdmin)
        }

        hasReadOnlyAdminCapability shouldBe List(true, true, true)
      }
    }
  }
}
