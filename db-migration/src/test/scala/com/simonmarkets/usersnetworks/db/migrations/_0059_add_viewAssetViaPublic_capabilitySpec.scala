package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.{PortfolioCapabilities, SimonUICapabilities}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0059_add_viewAssetViaPublic_capabilitySpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, Set(SimonUICapabilities.ViewUIArchitectCapability.name, "EqPIPGFAManager"))
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("someOtherCapability"))


  private val testnetwork1 = Network(
    id =  simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFaManager, testFa)
  )

  val newCapability = Set(
    PortfolioCapabilities.ViewAssetViaPublicCapability.name
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0059_add_viewAssetViaPublic_capabilitySpec" should {
    "add new capability" in {

      helper.verifyCapabilityAbsent(helper.allNetworks, newCapability)
      val doc = NetworkFormat.write(testnetwork1)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testnetwork1, UserRole.EqPIPGFAManager.productPrefix)), Set(SimonUICapabilities.ViewUIArchitectCapability.name, "EqPIPGFAManager"))

      val migration = new _0059_add_viewAssetViaPublic_capability(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(helper.allNetworks, Set(SimonUICapabilities.ViewUIArchitectCapability.name))
      helper.verifyCapabilityPresent(Seq((testnetwork1, UserRole.EqPIPGFAManager.productPrefix)), Set(SimonUICapabilities.ViewUIArchitectCapability.name, "EqPIPGFAManager") ++ newCapability)
      helper.verifyCapabilityAbsent(Seq((testnetwork1, UserRole.EqPIPGFA.productPrefix)), Set(SimonUICapabilities.ViewUIArchitectCapability.name) ++ newCapability)
    }
  }
}
