package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.SimonUICapabilities
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0109_backfill_rfq_view_layout_capabilities_for_distributorsSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("viewUIRfqFA", "viewUIRfqWholesaler", "viewUIRfqHomeOffice"))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa)
  )

  val newCapabilities = Set(
    SimonUICapabilities.ViewUIRfqNewSubmissionLayoutCapability.name,
    SimonUICapabilities.ViewUIRfqNewCardLayoutCapability.name
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0109_backfill_rfq_view_layout_capabilities_for_distributors" should {
    "add new capabilities when certain capabilities already exist" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilities)
      val doc = NetworkFormat.write(testNetwork1)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testNetwork1, UserRole.EqPIPGFAManager.productPrefix)), Set("viewUIRfqFA", "viewUIRfqWholesaler", "viewUIRfqHomeOffice"))

      val migration = new _0109_backfill_rfq_view_layout_capabilities_for_distributors(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testNetwork1, UserRole.EqPIPGFA.productPrefix)), Set("viewUIRfqFA", "viewUIRfqWholesaler", "viewUIRfqHomeOffice") ++ newCapabilities)
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilities)
    }
  }
}
