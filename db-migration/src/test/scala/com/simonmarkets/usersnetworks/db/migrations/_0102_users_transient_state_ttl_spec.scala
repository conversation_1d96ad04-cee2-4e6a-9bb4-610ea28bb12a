package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration._
import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0102_users_transient_state_ttl_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0102_users_transient_state_ttl" should {

    "add icnPassword ttl" in {

      val migration = new _0102_user_transient_state_ttl(db)
      migration.apply.await()

      whenReady(migration.col.getIndexes) { indexes =>
        indexes should contain(Index("userId_1", List(IndexedField("userId")), unique = true))
        indexes should contain(Index("createdAt_1", List(IndexedField("createdAt")), unique = false, List("expireAfterSeconds")))
      }
    }
  }
}