package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration._
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.users.common.User
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.{TestNetwork, TestUser}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoCollection
import org.mongodb.scala.bson.collection.Document
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0092_viaNetwork_capabilities_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = scala.concurrent.ExecutionContext.Implicits.global

  val network1 = TestNetwork(
    id = "netId1",
    entitlements = Set("admin")
  )

  val user1 = TestUser(
    id = "user1",
    networkId = network1.id,
    entitlements = Set("admin")
  )

  "_0092_viaNetwork_capabilities" should {

    "add network capabilities" in {
      val networkCollection: MongoCollection[Document] = db.getCollection[Document]("networks_new")
      networkCollection.insertMany(Seq(NetworkFormat.write(network1))).toFuture.await()

      val changeSet = new _0092_viaNetwork_capabilities(db)
      changeSet.apply.await()

      val accessKeys = networkCollection.find().toFuture.await()
        .map(doc => {
          val network = NetworkFormat.read(doc)
          network.id -> network.entitlements
        })

        .toMap
      accessKeys shouldBe Map(
        "netId1" -> Set(
          "admin",
          "viewNetworkViaNetwork:netId1",
          "readOnlyAdmin",
          "editNetworkViaPurview:netId1",
          "viewNetworkViaNetworkType:ADMIN",
          "editNetworkViaNetwork:netId1",
          "editNetworkViaNetworkType:ADMIN",
          "viewNetworkViaPurview:netId1"
        )
      )
    }

    "add user capabilities" in {
      val userCollection: MongoCollection[User] = db.getCollection[User]("users").withCodecRegistry(UserFormat.codecRegistry)
      userCollection.insertMany(Seq(user1)).toFuture.await()

      val changeSet = new _0092_viaNetwork_capabilities(db)
      changeSet.apply.await()

      val accessKeys = userCollection.find().toFuture.await()
        .map(user => user.id -> user.entitlements)
        .toMap
      accessKeys.keySet shouldBe Set("user1")
      accessKeys.get("user1").toList.flatten should contain allOf(
        "viewUserViaParentNetwork:netId1",
        "admin",
        "readOnlyAdmin",
        "uploadUserViaGuid:user1",
        "viewUserViaGuid:user1",
        "uploadUserViaNetwork:netId1",
        "impersonateUserViaPurview:netId1",
        "impersonateUserViaNetwork:netId1",
        "uploadUserViaNetworkTypeCapability:ADMIN",
        "uploadUserViaPurview:netId1",
        "viewUserViaNetworkType:ADMIN",
        "viewUserViaPurview:netId1",
        "viewUserViaNetwork:netId1"
      )
    }
  }
}