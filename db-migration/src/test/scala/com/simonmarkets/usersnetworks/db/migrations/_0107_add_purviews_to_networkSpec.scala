package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg._
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import org.scalatest.mockito.MockitoSugar.mock
import org.scalatest.{AsyncWordSpec, BeforeAndAfterEach, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0107_add_purviews_to_networkSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  private val testNetwork = Network(
    id = simon.Id.NetworkId("networkId"),
    name = "Test Network",
    networkCode = "BLAH",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(1, "test"),
    customRolesConfig = Set(CustomRoleDefinition("DoNotTouch", Set("cap1", "cap2"))),
    purviewNetworks = Some(Set(IssuerPurview(NetworkId("network"), Set("issuer"), None, Some(Set(PurviewedDomain.Accounts)))))
  )

  lazy val networks: MongoCollection[Document] = db.getCollection[Document]("networks_new")

  override def beforeEach(): Unit = (for {
    _ <- networks.drop()
  } yield()).toFuture().await

  "_0107_add_purviews_to_network" should {
    lazy val migration = new _0107_add_purviews_to_network(db)

    "add purviews to networks" in {
      val doc = NetworkFormat.write(testNetwork)

      testNetwork.purviews should not contain Purview("network",Set(PurviewEntity("issuer",Some(NetworkCategory.Issuer),Some(Set(PurviewedDomain.Accounts)))))

      for {
        _ <- networks.insertOne(doc).toFuture
        _ <- migration.apply
        updatedNetwork <- networks.find.head.map(NetworkFormat.read)
      } yield updatedNetwork.purviews.get should contain(Purview("network",Set(PurviewEntity("issuer",Some(NetworkCategory.Issuer),Some(Set(PurviewedDomain.Accounts))))))
    }

    "add purviews to networks with issuer and wholesaler" in {
      val testNetworkWithMultipleIssuers = testNetwork.copy(
        purviewNetworks = Some(Set(IssuerPurview(NetworkId("network"), Set("issuer1"), Some(NetworkId("wholesaler")), Some(Set(PurviewedDomain.Accounts)))))
      )
      val doc = NetworkFormat.write(testNetworkWithMultipleIssuers)

      testNetworkWithMultipleIssuers.purviews should not contain Purview("network", Set(
        PurviewEntity("issuer1", Some(NetworkCategory.Issuer), Some(Set(PurviewedDomain.Accounts))),
        PurviewEntity("wholesaler", Some(NetworkCategory.Wholesaler), Some(Set(PurviewedDomain.Accounts)))
      ))

      for {
        _ <- networks.insertOne(doc).toFuture
        _ <- migration.apply
        updatedNetwork <- networks.find.head.map(NetworkFormat.read)
      } yield
        updatedNetwork.purviews.get should contain(Purview("network", Set(
          PurviewEntity("issuer1", Some(NetworkCategory.Issuer), Some(Set(PurviewedDomain.Accounts))),
          PurviewEntity("wholesaler", Some(NetworkCategory.Wholesaler), Some(Set(PurviewedDomain.Accounts)))
        )))
    }

    "correctly map IssuerPurview to Purview" in {
      val issuerPurview = IssuerPurview(
        network = NetworkId("network-id"),
        issuers = Set("issuer1"),
        wholesaler = Some(NetworkId("wholesaler1")),
        purviewedDomainsUpdated = Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))
      )
      val changeSet = new _0107_add_purviews_to_network(mock[MongoDatabase])
      val purview = changeSet.mapIssuerPurviewToPurview(issuerPurview)
      purview.networkId shouldBe "network-id"
      purview.purviewEntities should have size 2
      purview.purviewEntities.head.key shouldBe "issuer1"
      purview.purviewEntities.head.purviewType shouldBe Some(NetworkCategory.Issuer)
      purview.purviewEntities.head.domains shouldBe Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))
      purview.purviewEntities.last.key shouldBe "wholesaler1"
      purview.purviewEntities.last.purviewType shouldBe Some(NetworkCategory.Wholesaler)
      purview.purviewEntities.last.domains shouldBe Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))
    }
  }
}
