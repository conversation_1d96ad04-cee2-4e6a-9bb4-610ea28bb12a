package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.Document
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0072_reformat_paidRole_field_in_paidFeatures_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0072_reformat_paidRole_field_in_paidFeatures_spec" should {
    lazy val paidFeaturesCol = db.getCollection("users.paidFeatures")
    "apply migration and add idType UserId" in {
      // Insert PaidFeatures
      val paidFeatureDocuments = Seq(
        Document(
          "_id" -> "yo",
          "id" -> "hamster_1",
          "roles" -> List(
            Document(
              "product" -> Document("_t" -> "Architect"),
              "roleSource" -> Document("_t" -> "Manual")
            )
          )
        ),
        Document(
          "_id" -> "yo2",
          "id" -> "hamster_2",
          "roles" -> List(
            Document(
              "product" -> Document("_t" -> "Architect"),
              "roleSource" -> Document("_t" -> "Stripe")
            )
          )
        )
      )
      paidFeaturesCol.insertMany(paidFeatureDocuments).toFuture().await()
      // Apply migration
      val migration = new _0072_reformat_paidRole_field_in_paidFeatures(db)
      migration.apply.await()

      //verify networkId field was added
      val updatedDocument = paidFeaturesCol.find(Document("id" -> "hamster_1")).headOption().await()
      updatedDocument should be(
        Some(Document(
          "_id" -> "yo",
          "id" -> "hamster_1",
          "roles" -> List(
            Document(
              "product" -> "Architect",
              "roleSource" -> "Manual"
            )
          )
        )))
      val updatedDocumentTwo = paidFeaturesCol.find(Document("id" -> "hamster_2")).headOption().await()
      updatedDocumentTwo should be(
        Some(Document(
          "_id" -> "yo2",
          "id" -> "hamster_2",
          "roles" -> List(
            Document(
              "product" -> "Architect",
              "roleSource" -> "Stripe"
            )
          )
        ))
      )
    }
  }
}