package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.Custodian.{<PERSON><PERSON>, WellsFargo}
import com.goldmansachs.marquee.pipg.CustodianFaNumber
import com.simonmarkets.users.common.User
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.users.repository.encoders.UserFormat.Fields
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.mongodb.scala.model.{Filters, Updates}
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0108_default_custodianFaNumbers_spec
    extends AsyncWordSpec
    with Matchers
    with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0108_default_custodianFaNumbers_spec" should {

    "set custodianFaNumbers to Set.empty if it does not exist" in {

      val migration = new _0108_default_custodianFaNumbers(db)
      val userCol = migration.users

      val networkId = NetworkId("abc")
      val user1 = TestUser.apply(id = "1", networkId = networkId)
      val user2 = TestUser.apply(id = "2", networkId = networkId)

      val networkFilter = Filters.eq(Fields.NetworkId, networkId)

      val userInserts: Seq[Document] = Seq(user1, user2).map(UserFormat.write)
      for {
        // insert users
        _ <- userCol.insertMany(userInserts).toFuture()

        // remove the custodianFaNumbers field on all users
        _ <- userCol
          .updateMany(
            filter = networkFilter,
            update = Updates.unset(migration.CUSTODIAN_FA_NUMBERS_FIELD)
          )
          .toFuture()

        // find all documents that have the field
        numberOfDocumentsWithField <- userCol
          .countDocuments(
            filter = Filters.and(
              networkFilter,
              Filters.exists(migration.CUSTODIAN_FA_NUMBERS_FIELD)
            )
          )
          .toFuture()

        // run the migration
        _ <- migration.apply

        // get documents
        res <- userCol.find(networkFilter).toFuture()

        // clean up
        _ <- userCol.deleteMany(Filters.empty()).toFuture()

      } yield {
        numberOfDocumentsWithField shouldBe 0
        res.size shouldBe 2
        res
          .map(UserFormat.read)
          .map(_.custodianFaNumbers)
          .count(_.isEmpty) shouldBe 2
      }
    }

    "leave custodianFaNumbers alone if it already exists" in {

      val migration = new _0108_default_custodianFaNumbers(db)
      val userCol = migration.users

      val networkId = NetworkId("def")
      val networkFilter = Filters.eq(Fields.NetworkId, networkId)
      val set1 = Set.empty[CustodianFaNumber]
      val set2 = Set(CustodianFaNumber(Cetera, "123abc"))
      val set3 = Set(
        CustodianFaNumber(Cetera, "123abc"),
        CustodianFaNumber(WellsFargo, "123abc")
      )
      val userSeq: Seq[User] = Seq(
        TestUser.apply(
          id = "1",
          networkId = networkId,
          custodianFaNumbers = set1
        ),
        TestUser.apply(
          id = "2",
          networkId = networkId,
          custodianFaNumbers = set2
        ),
        TestUser.apply(
          id = "3",
          networkId = networkId,
          custodianFaNumbers = set3
        )
      )

      val userSeqAsDocuments = userSeq.map(UserFormat.write)

      for {
        // insert users
        _ <- userCol.insertMany(userSeqAsDocuments).toFuture()

        // find all documents that have the field
        numberOfDocumentsWithField <- userCol
          .countDocuments(
            filter =
              Filters.and(networkFilter, Filters.exists("custodianFaNumbers"))
          )
          .toFuture()

        // run the migration
        _ <- migration.apply

        // get documents
        res1 <- userCol.find(Filters.eq(Fields.Id, "1")).toFuture()
        res2 <- userCol.find(Filters.eq(Fields.Id, "2")).toFuture()
        res3 <- userCol.find(Filters.eq(Fields.Id, "3")).toFuture()

        // clean up
        _ <- userCol.deleteMany(Filters.empty()).toFuture()

      } yield {
        numberOfDocumentsWithField shouldBe 3
        res1.map(UserFormat.read).head.custodianFaNumbers shouldBe userSeq(0).custodianFaNumbers
        res2.map(UserFormat.read).head.custodianFaNumbers shouldBe userSeq(1).custodianFaNumbers
        res3.map(UserFormat.read).head.custodianFaNumbers shouldBe userSeq(2).custodianFaNumbers
      }
    }
  }
}
