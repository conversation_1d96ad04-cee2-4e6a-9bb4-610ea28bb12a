package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax._
import com.simonmarkets.usersnetworks.db.migrations._0034_rename_refreshRfqQuoteCapabilities._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.{Document, MongoCollection}
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0034_rename_refreshRfqQuoteCapabilities_spec extends WordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0034_rename_refreshRfqQuoteCapabilities" should {
    "update networks" in {
      val customRoleDefinition1 = CustomRoleDefinition(
        role = "role1",
        capabilities = Set("refreshRfqIdeaQuoteViaOwner", "refreshRfqIdeaQuoteViaNetwork", "refreshRfqIdeaQuoteViaNetworkIssuerPurview", "otherCapabilities")
      )
      val customRoleDefinition2 = CustomRoleDefinition(
        role = "role2",
        capabilities = Set("refreshRfqIdeaQuoteViaOwner", "otherCapabilities")
      )
      val customRoleDefinition3 = CustomRoleDefinition(
        role = "role3",
        capabilities = Set("otherCapabilities")
      )
      val updatedCustomRoleDefinition1 = customRoleDefinition1.copy(
        capabilities = Set("refreshRfqQuoteViaOwner", "refreshRfqQuoteViaNetwork", "refreshRfqQuoteViaNetworkIssuerPurview", "otherCapabilities")
      )
      val updatedCustomRoleDefinition2 = customRoleDefinition2.copy(
        capabilities = Set("refreshRfqQuoteViaOwner", "otherCapabilities")
      )

      val net1 = Network(
        id = "net1",
        customRolesConfig = Set(customRoleDefinition1, customRoleDefinition2, customRoleDefinition3)
      )
      val net2 = Network(
        id = "net2",
        customRolesConfig = Set(customRoleDefinition3)
      )

      val networkCollection: MongoCollection[Network] = db.getCollection[Network]("networks_new").withCodecRegistry(networkCodecRegistry)
      networkCollection.insertMany(Seq(net1, net2)).toFuture.await

      val migration = new _0034_rename_refreshRfqQuoteCapabilities(db)
      migration.apply.await

      val networks = networkCollection.find(Document()).toFuture.await
      networks.map(n => n.id -> n).toMap shouldBe Map(
        "net1" -> net1.copy(
          customRolesConfig = Set(updatedCustomRoleDefinition1, updatedCustomRoleDefinition2, customRoleDefinition3)
        ),
        "net2" -> net2
      )
    }
  }
}
