package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, NetworkType, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.RfqConfigsCapabilities.ViewRfqConfigsViaNetworkIssuerPurviewCapability
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0056_add_ViewRfqConfigsViaNetworkIssuerPurviewCapability_to_issuers_and_wholesalersSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idHubOrganization: IdHubOrganization = IdHubOrganization(1, "idHubOrg")
  val financialAdvisor: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("dummyCapability2"))
  val issuer: CustomRoleDefinition = CustomRoleDefinition(UserRole.Issuer.productPrefix, Set("dummyCapability2"))
  val wholesaler: CustomRoleDefinition = CustomRoleDefinition(UserRole.Wholesaler.productPrefix, Set("dummyCapability3"))

  private val otherNetwork = Network(
    id = simon.Id.NetworkId("networkId1"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    networkTypes = Some(List(NetworkType.Other)),
    customRolesConfig = Set(financialAdvisor)
  )

  private val issuerNetwork = Network(
    id = simon.Id.NetworkId("networkId2"),
    name = "networkId2",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    networkTypes = Some(List(NetworkType.Issuer)),
    customRolesConfig = Set(issuer)
  )

  private val wholesalerNetwork = Network(
    id = simon.Id.NetworkId("networkId3"),
    name = "networkId3",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    networkTypes = Some(List(NetworkType.Wholesaler)),
    customRolesConfig = Set(wholesaler)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0056_add_ViewRfqConfigsViaNetworkIssuerPurviewCapability_to_issuers_and_wholesalers" should {
    "add ViewRfqConfigsViaNetworkIssuerPurviewCapability to issuers" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, Set(ViewRfqConfigsViaNetworkIssuerPurviewCapability.name))
      val docs = Set(otherNetwork, issuerNetwork, wholesalerNetwork).map(NetworkFormat.write).toSeq
      helper.networksCol.insertMany(docs).toFuture().await()
      helper.verifyCapabilityPresent(Seq((otherNetwork, "Financial Advisor")), Set("dummyCapability1"))
      helper.verifyCapabilityPresent(Seq((issuerNetwork, "Issuer")), Set("dummyCapability2"))
      helper.verifyCapabilityPresent(Seq((wholesalerNetwork, "Wholesaler")), Set("dummyCapability3"))

      val migration = new _0056_add_ViewRfqConfigsViaNetworkIssuerPurviewCapability_to_issuers_and_wholesalers(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((otherNetwork, "Financial Advisor")), Set("dummyCapability1"))
      helper.verifyCapabilityPresent(Seq((issuerNetwork, "Issuer")), Set("dummyCapability2", ViewRfqConfigsViaNetworkIssuerPurviewCapability.name))
      helper.verifyCapabilityPresent(Seq((wholesalerNetwork, "Wholesaler")), Set("dummyCapability3", ViewRfqConfigsViaNetworkIssuerPurviewCapability.name))

      helper.verifyCapabilityAbsent(Seq((otherNetwork, "Financial Advisor")), Set(ViewRfqConfigsViaNetworkIssuerPurviewCapability.name))
      helper.verifyCapabilityAbsent(helper.allNetworks, Set(ViewRfqConfigsViaNetworkIssuerPurviewCapability.name))
    }
  }
}
