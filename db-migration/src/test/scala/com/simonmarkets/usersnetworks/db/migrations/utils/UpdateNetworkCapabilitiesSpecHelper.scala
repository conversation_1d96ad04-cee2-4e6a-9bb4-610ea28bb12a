package com.simonmarkets.usersnetworks.db.migrations.utils

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import org.mongodb.scala.model.{Filters => MongoFilters}
import org.mongodb.scala.{Document, MongoCollection, MongoDatabase}
import org.scalatest.Matchers
import simon.Id.NetworkId


/**
 * A base test class for testing migrations that implement [[UpdateNetworkCapabilities]].
 * It is also used to test [[UpdateNetworkCapabilities]] itself.
 */
class UpdateNetworkCapabilitiesSpecHelper(db: MongoDatabase) extends Matchers {

  lazy val networksCol: MongoCollection[Document] = db.getCollection[Document]("networks_new")

  // Set up some test data that sub-classes can use

  val idhubOrg = IdHubOrganization(1, "test")

  val fmCapability1 = "fmCapability1"

  val faManagerCrc = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, Set(fmCapability1, "some-capability"))
  val issuerCrc = CustomRoleDefinition(UserRole.Issuer.productPrefix, Set("common-capability1", "common-capability2", "issuer-capability"))
  val wholesalerCrc = CustomRoleDefinition(UserRole.Wholesaler.productPrefix, Set("wholesaler-capability"))
  val faCrc = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("fa-capability"))

  val faManagerNetwork = Network(
    id = simon.Id.NetworkId("networkIdFm"),
    name = "networkIdWithFMRole",
    networkCode = "ABC",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(faManagerCrc),
    landingPage = Some(LandingPage.SIMON))

  val issuerNetwork = Network(
    id = simon.Id.NetworkId("networkIdIss"),
    name = "networkIdWithIssuerRole",
    networkCode = "networkIdIss",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(issuerCrc),
    landingPage = Some(LandingPage.SIMON))

  val wholesalerNetwork = Network(
    id = simon.Id.NetworkId("networkIdWh"),
    name = "networkIdWithWholesalerRole",
    networkCode = "networkIdWh",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(wholesalerCrc),
    landingPage = Some(LandingPage.SIMON))

  val faNetwork = Network(
    id = simon.Id.NetworkId("networkIdFa"),
    name = "networkWithFARole",
    networkCode = "networkIdFa",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(faCrc),
    landingPage = Some(LandingPage.SIMON))

  val multiRoleNetwork = Network(
    id = simon.Id.NetworkId("networkIdMulti"),
    name = "networkWithMultipleroles",
    networkCode = "networkIdMulti",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(faCrc, faManagerCrc, issuerCrc),
    landingPage = Some(LandingPage.SIMON))

  val noConfigNetwork = Network(
    id = simon.Id.NetworkId("noConfigs"),
    name = "networkIdWithNoConfigs",
    networkCode = "noConfigs",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(),
    landingPage = Some(LandingPage.SIMON))

  val allNetworks: Set[Network] = Set(faManagerNetwork, issuerNetwork, wholesalerNetwork, faNetwork, noConfigNetwork, multiRoleNetwork)

  def beforeEachHelper(networks: Set[Network] = allNetworks): Unit = {
    networksCol.drop().toFuture().await()
    val docs = networks.map(NetworkFormat.write).toSeq
    networksCol.insertMany(docs).toFuture().await()
  }

  private def queryNetwork(id: NetworkId): Network = {
    val doc = networksCol.find(MongoFilters.equal("id", id.toString)).head().await()
    NetworkFormat.read(doc)
  }

  // Utility methods for sub-classes

  def verifyCapabilityPresent(originalNetworks: Set[Network], capabilities: Set[String]): Unit = {
    val queriedNetworks = originalNetworks.map(n => queryNetwork(n.id)).map(n => n.id -> n).toMap
    val originalNetworksById = originalNetworks.map(n => n.id -> n).toMap
    originalNetworksById.zip(queriedNetworks).foreach { case ((_, originalNetwork), (_, queriedNetwork)) =>
      queriedNetwork.customRolesConfig.foreach { crc =>
        crc.capabilities should contain allElementsOf capabilities
      }
      // Also verify that network has not changed in any other way by comparing all values except custom roles config
      originalNetwork.copy(customRolesConfig = Set.empty, version = 0) shouldBe queriedNetwork.copy(customRolesConfig = Set.empty, version = 0)
    }
  }

  def verifyCapabilityPresent(networkRoles: Seq[(Network, String)],
      capabilities: Set[String]): Unit = {
    networkRoles.foreach { case (originalNetwork, role) =>
      val queriedNetwork = queryNetwork(originalNetwork.id)
      queriedNetwork.customRolesConfig.find(_.role == role).foreach { crc =>
        crc.capabilities should contain allElementsOf capabilities
      }
      // Also verify that network has not changed in any other way by comparing all values except custom roles config
      originalNetwork.copy(customRolesConfig = Set.empty, version = 0) shouldBe queriedNetwork.copy(customRolesConfig = Set.empty, version = 0)
    }
  }

  def verifyCapabilityAbsent(originalNetworks: Set[Network], capabilities: Set[String]): Unit = {
    val queriedNetworks = originalNetworks.map(n => queryNetwork(n.id)).map(n => n.id -> n).toMap
    val originalNetworksById = originalNetworks.map(n => n.id -> n).toMap
    originalNetworksById.zip(queriedNetworks).foreach { case ((_, originalNetwork), (_, queriedNetwork)) =>
      queriedNetwork.customRolesConfig.foreach { crc =>
        crc.capabilities should contain noElementsOf capabilities
      }
      // Also verify that network has not changed in any other way by comparing all values except custom roles config
      originalNetwork.copy(customRolesConfig = Set.empty, version = 0) shouldBe queriedNetwork.copy(customRolesConfig = Set.empty, version = 0)
    }
  }

  def verifyCapabilityAbsent(networkRoles: Seq[(Network, String)],
      capabilities: Set[String]): Unit = {
    networkRoles.foreach { case (originalNetwork, role) =>
      val queriedNetwork = queryNetwork(originalNetwork.id)
      queriedNetwork.customRolesConfig.find(_.role == role).foreach { crc =>
        crc.capabilities should contain noElementsOf capabilities
      }
      // Also verify that network has not changed in any other way by comparing all values except custom roles config
      originalNetwork.copy(customRolesConfig = Set.empty, version = 0) shouldBe queriedNetwork.copy(customRolesConfig = Set.empty, version = 0)
    }
  }

}
