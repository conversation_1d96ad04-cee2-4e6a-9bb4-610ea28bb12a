package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0103_remove_impersonate_capabilitiesSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  private val removeCapabilities: Set[String] = Set(
    "impersonateUserViaFANumber",
    "impersonateUserViaLocation",
    "impersonateUserViaNetwork",
    "impersonateUserViaPurview"
  )

  private val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, removeCapabilities + "fa-manager")
  private val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, removeCapabilities + "fa")

  private val test1Network = Network(
    id = NetworkId("Test 1 Network"),
    name = "Test 1 Network",
    networkCode = "yZ8z",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(10616, "Name Test 1 Network"),
    customRolesConfig = Set(testFaManager, testFa)
  )
  private val iCapitalAdminNetwork = Network(
    id = NetworkId("SIMON Admin"),
    name = "iCapital Admin",
    networkCode = "pvRg",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(21727, "Training Capital Mgmt Corp"),
    customRolesConfig = Set(testFaManager, testFa)
  )
  private val raymondJamesNetwork = Network(
    id = NetworkId("Raymond James"),
    name = "Raymond James",
    networkCode = "zd2q",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = IdHubOrganization(12485, "Raymond James & Associates Inc"),
    customRolesConfig = Set(testFaManager, testFa)
  )

  override def beforeEach(): Unit = helper.beforeEachHelper()

  "_0103_remove_impersonate_capabilities" should {

    "remove impersonate capabilities from Test 1 Network" in {
      val docs = Set(test1Network, iCapitalAdminNetwork, raymondJamesNetwork).map(NetworkFormat.write).toSeq
      helper.networksCol.insertMany(docs).toFuture.await

      helper.verifyCapabilityPresent(Set(test1Network, iCapitalAdminNetwork, raymondJamesNetwork), removeCapabilities)

      val migration = new _0103_remove_impersonate_capabilities(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(Set(test1Network), removeCapabilities)
      helper.verifyCapabilityPresent(Set(iCapitalAdminNetwork, raymondJamesNetwork), removeCapabilities)
    }
  }

}