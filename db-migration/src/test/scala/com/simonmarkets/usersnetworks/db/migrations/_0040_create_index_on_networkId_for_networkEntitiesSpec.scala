package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import com.simonmarkets.syntax.futureOpsConversion
import org.scalatest.concurrent.ScalaFutures.whenReady
import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0040_create_index_on_networkId_for_networkEntitiesSpec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_0040_create_index_on_networkId_for_networkEntitiesSpec" should {
    "create index on networkId for networkEntities collection" in {
      val collection = db.getCollection("networkEntities")
      val migration = new _0040_create_index_on_networkId_for_networkEntities(db)
      migration.apply.await
      whenReady(collection.getIndexes) { indexes =>
        indexes should contain(Index("networkId_1", List(IndexedField("networkId")), unique = false))
      }
    }
  }

}
