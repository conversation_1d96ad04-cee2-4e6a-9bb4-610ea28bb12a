package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.Document
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0071_add_networkId_field_to_paidFeatures_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global
  private val AdminNetworkId = NetworkId("SIMON Admin")
  private val hamsterNetwork = NetworkId("Hamster Network")
  private val user1 = TestUser.apply(
    id = "id_1",
    networkId = AdminNetworkId,
    email = "email_1",
    idpLoginId = "not_email"
  )
  private val user2 = TestUser.apply(
    id = "id_2",
    networkId = hamsterNetwork,
    email = "email_2",
    idpLoginId = "not_email"
  )

  "_0071_add_networkId_field_to_paidFeatures_spec" should {
    lazy val paidFeaturesCol = db.getCollection("users.paidFeatures")
    lazy val usersCol = db.getCollection("users")
    "apply migration and add idType UserId" in {

      //Insert Users
      val userInserts = Seq(user1, user2).map(UserFormat.write)
      usersCol.insertMany(userInserts).toFuture().await
      // Insert PaidFeatures
      val paidFeatureDocuments = Seq(
        Document("_id" -> "6581bcb2850d114a73e2a1d1", "id" -> "id_1", "version" -> 1),
        Document("_id" -> "23f23g23b3g3efwfewef23", "id" -> "id_2", "version" -> 1)
      )
      paidFeaturesCol.insertMany(paidFeatureDocuments).toFuture().await()
      // Apply migration
      val migration = new _0071_add_networkId_field_to_paidFeatures(db)
      migration.apply.await()

      //verify networkId field was added
      val updatedDocument = paidFeaturesCol.find(Document("id" -> "id_1")).headOption().await()
      updatedDocument should be(
        Some(Document(
          "_id" -> "6581bcb2850d114a73e2a1d1",
          "id" -> "id_1",
          "networkId" -> "SIMON Admin",
          "version" -> 1
        )))

      val updatedDocumentTwo = paidFeaturesCol.find(Document("id" -> "id_2")).headOption().await()
      updatedDocumentTwo should be(
        Some(Document(
          "_id" -> "23f23g23b3g3efwfewef23",
          "id" -> "id_2",
          "networkId" -> "Hamster Network",
          "version" -> 1
        )))
    }
  }
}