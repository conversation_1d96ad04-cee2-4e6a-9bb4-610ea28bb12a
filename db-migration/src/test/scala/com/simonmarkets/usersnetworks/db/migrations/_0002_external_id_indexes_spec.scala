package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext


class _0002_external_id_indexes_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_2_external_id_indexes" should {

    lazy val externalIdTypeColl = db.getCollection("users.externalIdTypes")
    lazy val usersColl = db.getCollection("users")

    "create indexes" in {

      val migration = new _0002_external_id_indexes(db)
      migration.apply.await()

      whenReady(externalIdTypeColl.getIndexes) { indexes =>
        indexes should contain(Index("name_1", List(IndexedField("name")), unique = true))
      }

      whenReady(usersColl.getIndexes) { indexes =>
        indexes should contain(Index(
          "externalIds.subject_1_externalIds.id_1",
          List(IndexedField(migration.ExternalId_subject), IndexedField(migration.ExternalId_id)),
          unique = true)
        ).and(contain(Index(
          "distributorId_1_networkId_1",
          List(IndexedField(migration.DistributorId), IndexedField(migration.NetworkId)),
          unique = true)
        )).and(contain(Index(
          "omsId_1_networkId_1",
          List(IndexedField(migration.OmsId), IndexedField(migration.NetworkId)),
          unique = true)
        ))
      }
    }
  }
}
