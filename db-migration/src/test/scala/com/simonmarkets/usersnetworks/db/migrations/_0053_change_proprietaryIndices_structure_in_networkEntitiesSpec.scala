package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.usersnetworks.db.data.networkEntity.{ProprietaryIndexIdentifiers, ProprietaryIndexNewSchema, ProprietaryIndicesInfo}
import com.simonmarkets.usersnetworks.db.migrations._0053_change_proprietaryIndices_structure_in_networkEntities.{NetworkEntities, ProprietaryIndex}
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0053_change_proprietaryIndices_structure_in_networkEntitiesSpec extends WordSpec with EmbeddedMongoLike with Matchers with ScalaFutures {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private lazy val migration = new _0053_change_proprietaryIndices_structure_in_networkEntities(db)

  "_0053_change_proprietaryIndices_structure_in_networkEntitiesSpec" should {
    "update proprietary indices" in {

      val networkEntitiesWithNoMatchingKeysEmptyPropIndices = NetworkEntities(
        "networkId1",
        None
      )
      val networkEntitiesWithMatchingKeysEmptyPropIndices = NetworkEntities(
        "ddad7181-06c8-4c3f-a9c0-1ecd651ca26d",
        None
      )
      val networkEntitiesWithNoMatchingKeysPropIndicesDefined = NetworkEntities(
        "networkId3",
        Some(List(ProprietaryIndex("Dummy short name", "MqId1")))
      )
      val networkEntitiesWithMatchingKeysPropIndicesDefined = NetworkEntities(
        "Citi",
        Some(List(ProprietaryIndex("Citi Dynamic Asset Selector 5 Er", "MAX7H8BQQS4QKMSA")))
      )

      migration.collectionExisting.insertMany(Seq(networkEntitiesWithNoMatchingKeysEmptyPropIndices, networkEntitiesWithMatchingKeysEmptyPropIndices, networkEntitiesWithNoMatchingKeysPropIndicesDefined, networkEntitiesWithMatchingKeysPropIndicesDefined)).toFuture.await
      migration.apply.await()

      val results = migration.collectionUpdated.find().toFuture().await
      results.map { result =>
        if (result.networkId == networkEntitiesWithNoMatchingKeysEmptyPropIndices.networkId) {
          result.proprietaryIndices should equal(Some(List(ProprietaryIndexNewSchema("ShortName-DBmig", ProprietaryIndexIdentifiers("MQID-DBmig", "BBID-DBmig")))))
        }
        else if (result.networkId == networkEntitiesWithMatchingKeysEmptyPropIndices.networkId) {
          result.proprietaryIndices should equal(ProprietaryIndicesInfo.networkIdToProprietaryIndicesMapProd.get("ddad7181-06c8-4c3f-a9c0-1ecd651ca26d"))
        }
        else if (result.networkId == networkEntitiesWithNoMatchingKeysPropIndicesDefined.networkId) {
          result.proprietaryIndices should equal(Some(List(ProprietaryIndexNewSchema("ShortName-DBmig", ProprietaryIndexIdentifiers("MQID-DBmig", "BBID-DBmig")))))
        }
        else if (result.networkId == networkEntitiesWithMatchingKeysPropIndicesDefined.networkId) {
          result.proprietaryIndices should equal(ProprietaryIndicesInfo.networkIdToProprietaryIndicesMapProd.get("Citi"))
        }
      }
      results.size should equal(4)
    }
  }
}

