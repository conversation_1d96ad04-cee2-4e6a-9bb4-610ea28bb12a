package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, NetworkType, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.OfferingsV1Capabilities
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0047_remove_tradeOfferingViaTradePayoffEntitlement_from_issuersSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idHubOrganization: IdHubOrganization = IdHubOrganization(1, "idHubOrg")
  val issuer: CustomRoleDefinition = CustomRoleDefinition(UserRole.Issuer.productPrefix, Set("dummyCapability1", OfferingsV1Capabilities.TradeOfferingViaTradePayoffEntitlement))
  val financialAdvisor: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("dummyCapability2", OfferingsV1Capabilities.TradeOfferingViaTradePayoffEntitlement))

  private val network = Network(
    id = simon.Id.NetworkId("networkId1"),
    name = "networkId1",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idHubOrganization,
    networkTypes = Some(List(NetworkType.Issuer)),
    customRolesConfig = Set(issuer, financialAdvisor)
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0044_add_ViewRfqConfigsViaNetworkCapability_to_issuers" should {
    "remove OfferingsV1Capabilities.TradeOfferingViaTradePayoffEntitlement from issuers" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, Set(OfferingsV1Capabilities.TradeOfferingViaTradePayoffEntitlement))
      val document = NetworkFormat.write(network)
      helper.networksCol.insertOne(document).toFuture().await()
      helper.verifyCapabilityPresent(Seq((network, "Issuer")), Set("dummyCapability1", OfferingsV1Capabilities.TradeOfferingViaTradePayoffEntitlement))
      helper.verifyCapabilityPresent(Seq((network, "Financial Advisor")), Set("dummyCapability2", OfferingsV1Capabilities.TradeOfferingViaTradePayoffEntitlement))

      val migration = new _0047_remove_tradeOfferingViaTradePayoffEntitlement_from_issuers(db)
      migration.apply.await

      helper.verifyCapabilityAbsent(Seq((network, "Issuer")), Set(OfferingsV1Capabilities.TradeOfferingViaTradePayoffEntitlement))
      helper.verifyCapabilityAbsent(Seq((network, "Financial Advisor")), Set(OfferingsV1Capabilities.TradeOfferingViaTradePayoffEntitlement))
    }
  }
}
