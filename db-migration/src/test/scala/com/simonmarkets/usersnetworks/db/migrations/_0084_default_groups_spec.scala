package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.model.{Filters, Updates}
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.{ExecutionContext, Future}

class _0084_default_groups_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val user1 = TestUser.apply(id = "1", networkId = NetworkId("1"))
  private val user2 = user1.copy(id = "2", groups = Map("group1" -> Set("role1")), passport = Map("domain" -> 1))


  "_0084_default_groups_spec" should {
    "add empty maps to users without them" in {
      val migration = new _0084_default_groups(db)
      val fieldsToCheck =
        Seq(
          migration.EventInfoField,
          migration.LoginModeField,
          migration.Passport,
          migration.UserTypeField,
          migration.Groups,
        ) ++ migration.setFields ++ migration.boolFields ++ migration.listFields

      for {
        _ <- migration.userCol.insertMany(Seq(user1, user2)).toFuture
        _ <- Future.sequence {
          fieldsToCheck.map { field =>
            migration.userCol.updateOne(
              filter = Filters.eq(migration.Id, user1.id),
              update = Updates.unset(field)
            ).toFuture
          }
        }
        before <- migration
          .userCol
          .countDocuments(
            Filters.and(Filters.eq(migration.Id, user1.id), Filters.exists(migration.EventInfoField, exists = false))
          ).toFuture
        _ = before shouldBe 1
        _ <- migration.apply
        updated1 <- migration.userCol.find(Filters.eq(migration.Id, user1.id)).toFuture
        updated2 <- migration.userCol.find(Filters.eq(migration.Id, user2.id)).toFuture
      } yield {
        updated1.head.groups shouldBe user1.groups
        updated1.head.passport shouldBe user1.passport
        updated2.head.groups shouldBe user2.groups
        updated2.head.passport shouldBe user2.passport
      }
    }
  }
}
