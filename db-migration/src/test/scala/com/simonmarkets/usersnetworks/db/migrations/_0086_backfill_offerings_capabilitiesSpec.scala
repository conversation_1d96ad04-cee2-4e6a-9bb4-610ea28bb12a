package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.RatesSPOfferingsCapabilities.ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability
import com.simonmarkets.capabilities.SPOfferingsCapabilities.ViewNonRegSSPPastOfferingsViaViewPayoffEntitlementsCapability
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0086_backfill_offerings_capabilitiesSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("viewNonRegSSPPastOffering"))
  val testFaManager: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFAManager.productPrefix, Set(ViewNonRegSSPPastOfferingsViaViewPayoffEntitlementsCapability.name))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa)
  )

  private val testNetwork2 = Network(
    id = simon.Id.NetworkId("networkIdYes2"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFaManager)
  )

  val newCapabilities: Set[String] = Set(
    ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability.name,
    ViewNonRegSSPPastOfferingsViaViewPayoffEntitlementsCapability.name
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0086_backfill_offerings_capabilitiesSpec" should {
    "add new capabilities when certain capabilities already exist" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilities)
      val doc = NetworkFormat.write(testNetwork1)
      val doc2 = NetworkFormat.write(testNetwork2)
      helper.networksCol.insertMany(Seq(doc, doc2)).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testNetwork1, UserRole.EqPIPGFA.productPrefix)), Set("viewNonRegSSPPastOffering"))
      helper.verifyCapabilityPresent(Seq((testNetwork2, UserRole.EqPIPGFAManager.productPrefix)), Set(ViewNonRegSSPPastOfferingsViaViewPayoffEntitlementsCapability.name))

      val migration = new _0086_backfill_offerings_capabilities(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testNetwork1, UserRole.EqPIPGFA.productPrefix)), Set("viewNonRegSSPPastOffering") ++ newCapabilities)
      helper.verifyCapabilityPresent(Seq((testNetwork2, UserRole.EqPIPGFAManager.productPrefix)),
        Set(ViewNonRegSSPPastOfferingsViaViewPayoffEntitlementsCapability.name) ++ newCapabilities)
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilities)
    }
  }
}
