package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.util.DBCollectionOps.{DBCollectionOps, Index, IndexedField}
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.model.Indexes.ascending
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}

import scala.concurrent.ExecutionContext

class _0005_network_index_cleanup_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike {

  implicit val ec: ExecutionContext = ExecutionContext.global

  "_5_network_index_cleanup" should {

    "drop networkCode index" in {

      val migration = new _0005_network_index_cleanup(db)

      //add index to mimic live db setup
      migration.networksCol.createIndex(
        key = ascending("networkCode"),
        options = new IndexOptions().unique(true)
      ).toFuture.await

      //confirm is present as in live db
      whenReady(migration.networksCol.getIndexes) { indexes =>
        indexes should contain(Index(migration.indexName, List(IndexedField("networkCode")), unique = true))
      }

      migration.apply.await
      //confirm migration clears it
      whenReady(migration.networksCol.getIndexes) { indexes =>
        indexes.map(_.name) should not contain migration.indexName
      }
    }
  }

}
