package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CustomRoleDefinition, IdHubOrganization, UserRole}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.capabilities.SimonUICapabilities
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.{EventInfo, Network}
import com.simonmarkets.syntax._
import com.simonmarkets.users.common.LoginMode
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}

import scala.concurrent.ExecutionContext

class _0065_backfill_rfq_tab_capabilities_for_home_office_fa_wholesaler_capabilitiesSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {
  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext
  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val idhubOrg: IdHubOrganization = IdHubOrganization(1, "test")
  val testFa: CustomRoleDefinition = CustomRoleDefinition(UserRole.EqPIPGFA.productPrefix, Set("viewUIRfqHomeOffice", "viewUIRfqFA", "viewUIRfqWholesaler"))

  private val testNetwork1 = Network(
    id = simon.Id.NetworkId("networkIdYes"),
    name = "networkIdYes",
    networkCode = "XYZ",
    eventInfo = EventInfo.Default,
    loginMode = LoginMode.UsernamePassword,
    idHubOrganization = idhubOrg,
    customRolesConfig = Set(testFa)
  )

  val newCapabilities = Set(
    SimonUICapabilities.ViewUIRfqActiveRfqsTabCapability.name,
    SimonUICapabilities.ViewUIRfqActiveRftsTabCapability.name
  )

  override def beforeEach(): Unit = {
    helper.beforeEachHelper()
  }

  "_0065_backfill_rfq_tab_capabilities_for_home_office_fa_wholesaler_capabilitiesSpec" should {
    "add new capabilities when certain capabilities already exist" in {
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilities)
      val doc = NetworkFormat.write(testNetwork1)
      helper.networksCol.insertOne(doc).toFuture().await()
      helper.verifyCapabilityPresent(Seq((testNetwork1, UserRole.EqPIPGFAManager.productPrefix)), Set("viewUIRfqHomeOffice", "viewUIRfqFA", "viewUIRfqWholesaler"))

      val migration = new _0065_backfill_rfq_tab_capabilities_for_home_office_fa_wholesaler_capabilities(db)
      migration.apply.await

      helper.verifyCapabilityPresent(Seq((testNetwork1, UserRole.EqPIPGFA.productPrefix)), Set("viewUIRfqHomeOffice", "viewUIRfqFA", "viewUIRfqWholesaler") ++ newCapabilities)
      helper.verifyCapabilityAbsent(helper.allNetworks, newCapabilities)
    }
  }
}
