package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{IssuerPurview, PurviewedDomain}
import com.gs.marquee.util.CurrentThreadExecutionContext
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.syntax.futureOpsConversion
import org.mongodb.scala.model.{Filters => MongoFilters}
import com.simonmarkets.usersnetworks.db.migrations.utils.UpdateNetworkCapabilitiesSpecHelper
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.scalatest.{BeforeAndAfterEach, Matchers, WordSpec}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0081_add_OrderAccountsPurviewDomainSpec extends WordSpec with Matchers with EmbeddedMongoLike with BeforeAndAfterEach {

  private implicit val ec: ExecutionContext = CurrentThreadExecutionContext

  lazy val helper = new UpdateNetworkCapabilitiesSpecHelper(db)

  val purviewNetworks1 = Some(Set(
    IssuerPurview(NetworkId("testNetwork2"), Set("userId1", "userId2"), None, Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))),
    IssuerPurview(NetworkId("noConfigs"), Set("userId1"), None, Some(Set(PurviewedDomain.Users)))))
  val purviewNetworks2 = Some(Set(
    IssuerPurview(NetworkId("testNetwork1"), Set("userId1"), None, Some(Set(PurviewedDomain.Accounts, PurviewedDomain.Users))),
    IssuerPurview(NetworkId("noConfigs"), Set("userId1"), None, Some(Set(PurviewedDomain.Accounts)))))
  private lazy val testNetwork1 = helper.noConfigNetwork.copy(id = NetworkId("testNetwork1"), name = "testNetwork1", purviewNetworks = purviewNetworks1)
  private lazy val testNetwork2 = helper.noConfigNetwork.copy(id = NetworkId("testNetwork2"), name = "testNetwork2", purviewNetworks = purviewNetworks2)

  override def beforeEach(): Unit = helper.beforeEachHelper()

  "_0081_add_OrderAccountsPurviewDomainSpec" should {
    "add OrderAccountsCapabilities to roles" in {
      val doc1 = NetworkFormat.write(testNetwork1)
      val doc2 = NetworkFormat.write(testNetwork2)
      lazy val doc3 = NetworkFormat.write(helper.noConfigNetwork)
      helper.networksCol.insertMany(Seq(doc1, doc2, doc3)).toFuture.await
      lazy val old1 = NetworkFormat
        .read(helper.networksCol.find(MongoFilters.equal("id", testNetwork1.id.toString)).head.await)
        .purviewNetworks.getOrElse(Set.empty)
      lazy val old2 = NetworkFormat
        .read(helper.networksCol.find(MongoFilters.equal("id", testNetwork2.id.toString)).head.await)
        .purviewNetworks.getOrElse(Set.empty)
      lazy val old3 = NetworkFormat
        .read(helper.networksCol.find(MongoFilters.equal("id", helper.noConfigNetwork.id.toString)).head.await)
        .purviewNetworks.getOrElse(Set.empty)
      Set(old1, old2, old3).foreach { n =>
        n.foreach { s =>
          s.purviewedDomainsUpdated.getOrElse(Set.empty) should contain noElementsOf Set(PurviewedDomain.OrderAccounts)
        }
      }

      val migration = new _0081_add_OrderAccountsPurviewDomain(db)
      migration.apply.await

      lazy val purview1 = NetworkFormat
        .read(helper.networksCol.find(MongoFilters.equal("id", testNetwork1.id.toString)).head.await)
        .purviewNetworks.getOrElse(Set.empty)
      lazy val purview2 = NetworkFormat
        .read(helper.networksCol.find(MongoFilters.equal("id", testNetwork2.id.toString)).head.await)
        .purviewNetworks.getOrElse(Set.empty)
      lazy val purview3 = NetworkFormat
        .read(helper.networksCol.find(MongoFilters.equal("id", helper.noConfigNetwork.id.toString)).head.await)
        .purviewNetworks.getOrElse(Set.empty)
      purview1.foreach { s =>
        if (s.purviewedDomainsUpdated.getOrElse(Set.empty).contains(PurviewedDomain.Accounts))
          s.purviewedDomainsUpdated.getOrElse(Set.empty) should contain allElementsOf Set(PurviewedDomain.OrderAccounts)
        else
          s.purviewedDomainsUpdated.getOrElse(Set.empty) should contain noElementsOf Set(PurviewedDomain.OrderAccounts)
      }
      purview2.foreach { s =>
        s.purviewedDomainsUpdated.getOrElse(Set.empty) should contain allElementsOf Set(PurviewedDomain.OrderAccounts)
      }
      purview3.foreach {
        s => s.purviewedDomainsUpdated.getOrElse(Set.empty) should contain noElementsOf Set(PurviewedDomain.OrderAccounts)
      }
    }
  }
}
