package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.CustomConfig
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.users.common.User
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.usersnetworks.db.migrations.utils.TestUser
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.bson.Document
import org.scalatest.concurrent.ScalaFutures.whenReady
import org.scalatest.{AsyncWordSpec, Matchers}
import simon.Id.NetworkId

import scala.concurrent.ExecutionContext

class _0076_fix_mixpanel_ids_spec extends AsyncWordSpec with Matchers with EmbeddedMongoLike with CustomConfig {

  implicit val ec: ExecutionContext = ExecutionContext.global
  System.setProperty("simon.env", "test")

  private val network1 = NetworkId("netId1")

  private val user1 = TestUser.apply(
    id = "id_1",
    networkId = network1,
    maskedIds = Set.empty
  )
  private val user2 = TestUser.apply(
    id = "id_2",
    networkId = network1,
    maskedIds = Set(
      MaskedId(
        target = "NotMix", id = "abcd"
      )
    )
  )
  private val user3 = TestUser.apply(
    id = "id_3",
    networkId = network1,
    maskedIds = Set(
      MaskedId(
        target = "NotMix", id = "abcd"
      ),
      MaskedId(
        target = "MixPanel", id = "abcd3"
      )
    )
  )
  private val user4 = TestUser.apply(
    id = "id_4",
    networkId = network1,
    maskedIds = Set(
      MaskedId(
        target = "NotMix", id = "abcd"
      ),
      MaskedId(
        target = "MixPanel", id = "bad-id"
      )
    )
  )
  private val user5 = TestUser.apply(
    id = "id_5",
    networkId = network1,
    maskedIds = Set(
      MaskedId(
        target = "NotMix", id = "abcd"
      ),
      MaskedId(
        target = "MixPanel", id = "bad-id"
      )
    )
  )


  "_0076_fix_mixpanel_ids_spec" should {

    "add mixpanel ids to users without them" in {

      val migration = new _0076_fix_mixpanel_ids(db)

      val userInserts: Seq[Document] = Seq(user1, user2, user3, user4, user5).map(UserFormat.write)

      migration.users.insertMany(userInserts).toFuture.await

      migration.apply.await

      whenReady(migration.users.find().toFuture) { userDocs =>

        val migratedUsers = userDocs.map(UserFormat.read)

        migratedUsers.map(TestUser.cleanDates) should contain(TestUser.cleanDates(user3))
        getMixId(migratedUsers, user1) should not be empty
        getMixId(migratedUsers, user2) should not be empty
        migratedUsers.find(_.id == user2.id).get.maskedIds.find(_.target == "NotMix") should not be empty
        getMixId(migratedUsers, user4) should not be Some("bad-id")
        getMixId(migratedUsers, user5) should not be Some("bad-id")
        getMixId(migratedUsers, user1) should not be
          getMixId(migratedUsers, user2) should not be
          getMixId(migratedUsers, user3) should not be
          getMixId(migratedUsers, user4) should not be
          getMixId(migratedUsers, user5)
      }
    }

    def getMixId(in: Seq[User], user: User): Option[String] =
      in
        .find(_.id == user.id)
        .get
        .maskedIds
        .find(_.target == "MixPanel")
        .map(_.id)

  }

}