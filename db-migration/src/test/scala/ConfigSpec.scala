import com.simonmarkets.db.migration.config.MigrationConfig
import com.simonmarkets.utils.config.Resolvers._
import com.simonmarkets.utils.config.resolvers.ConfigResolver
import com.typesafe.config.ConfigFactory
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import java.io.File
import pureconfig.generic.auto._

class ConfigSpec extends WordSpec with Matchers with MockitoSugar {

  class TestResolver extends ConfigResolver {
    override def prefix: String = "sm"

    override def resolve(path: String): String =
      path match {
        case "applicationconfig-mongo-auth-pipg" =>
          """
            |   {
            |      url = "mongoUrl"
            |      authentication {
            |        type = "password"
            |        user = "alpha-pipg-user"
            |        password = "password"
            |        database = "admin"
            |      }
            |    }""".stripMargin
        case other => other
      }

    override def resolveBinary(path: String): Array[Byte] = ???
  }

  "Configs" should {
    "not blow up" in {
      val configNames = Set("local.conf", "alpha.conf", "qa.conf", "prod.conf")
      val configNamesToFile = configNames.map(name => getClass.getResource(name).getPath)
      System.setProperty("SM_CREDENTIAL_PROVIDER", "webIdentityProvider")
      ConfigFactory.invalidateCaches()
      configNamesToFile.foreach { rawConfig =>
        val config = ConfigFactory.parseFile(new File(rawConfig))
        val resolvedConfig = config.resolveSecrets(List(new TestResolver))
        pureconfig.loadConfigOrThrow[MigrationConfig](resolvedConfig)
      }
    }
  }
}
