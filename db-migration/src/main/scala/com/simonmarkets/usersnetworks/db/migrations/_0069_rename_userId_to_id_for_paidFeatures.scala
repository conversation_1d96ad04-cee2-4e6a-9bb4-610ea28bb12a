package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala._
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates._

import scala.concurrent.{ExecutionContext, Future}

class _0069_rename_userId_to_id_for_paidFeatures(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0069_rename_userId_to_id_for_paidFeatures")
  log.info(s"Starting migration _0069_rename_userId_to_id_for_paidFeatures")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val collection = mongoDB.getCollection("users.paidFeatures")

    for {
      //Drop userId index that was added in _0063_create_index_on_userId_for_userPaidFeatures
      _ <- collection.dropIndex("userId_1").toFuture
      updateResult <- collection.updateMany(
        exists("userId", true),
        rename("userId", "id")
      ).toFuture()
      _ = log.info(s"Renamed ${updateResult.getModifiedCount} documents in users.paidFeatures collection: userId to id")
    } yield ()
  }
}