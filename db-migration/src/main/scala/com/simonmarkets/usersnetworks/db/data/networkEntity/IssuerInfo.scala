package com.simonmarkets.usersnetworks.db.data.networkEntity

import com.goldmansachs.marquee.pipg.ContactInfo

case class IssuerInfo(
    issuerShortName: String,
    contractTypeWrapper: String,
    issuerKey: String,
    issuer: String,
    issuerSymbol: String,
    contactInfo: Option[ContactInfo] = None,
    regS: Boolean = false,  //whether this issuer is used for reg-S notes
    legacy: Boolean = false, //if true, this issuer isn't expected to be used for future issuances
    is3a2: Boolean = false, //Whether the issuances from this issuer are 3a2
    isNonNativeIssuerKey: Boolean = false, //for NBC_GS and all
)

object IssuerMapping {
  val siEntityToProdNetworkIdMapping = Map(
    Entity.goldmanSachs -> "Goldman Sachs",
    Entity.jpm -> "J.P.Morgan",
    Entity.cs -> "06529f39-abff-4e5b-be87-e7e5195cbdc8",
    Entity.barclays -> "Barclays",
    Entity.hsbc -> "HSBC",
    Entity.wellsFargo -> "Wells Fargo",
    Entity.morganStanley -> "ddad7181-06c8-4c3f-a9c0-1ecd651ca26d",

    // new issuers
    Entity.bnp -> "50f43249-c68c-4ab1-88e9-ba2608b2bc10",
    Entity.creditAgricole -> "430f3652-9bf5-4504-b3d2-a3c8cc8abd26",
    Entity.cibc -> "a2b53545-ac84-47e2-9ece-0686d23936fb",
    Entity.citi -> "Citi",
    Entity.nbc -> "103f29df-1320-4975-b4b0-024ed21678e0",
    Entity.ubs -> "c5038e14-849d-40a9-b9ef-696f01eb07c1",
    Entity.socGen -> "0016bebb-b7d5-4ce8-9b08-0a4f8f1bbec3",
    Entity.rbc -> "0eb2a247-5532-4895-b278-e70aebe8d3ed",
    Entity.td  -> "TD Securities",


    Entity.bns -> "044bc89f-5cb9-476c-a3fb-5ca749493620",
    Entity.bankOfAmerica -> "c3f05ca7-d081-4219-afab-0400bd99d92e",
    Entity.bmo -> "cb7451d3-e0bb-4fb6-a43a-4921af596025",
    Entity.jefferies -> "e7798b43-dd1b-4ec3-ad54-a06e630e121a",
    Entity.bbva -> "3280f90e-923e-449e-a1cf-726128d4d13e",
    //test issuer
    Entity.testEntity -> "Test Barclays",
    Entity.simon -> "Test GS",
    Entity.spTestEntity -> "Test MS",

    // distributor
    Entity.mariner -> "********-2fb9-4929-8ba7-546932eb91c4"
  )

  val siEntityToQaNetworkIdMapping = Map(
    Entity.goldmanSachs -> "Goldman Sachs",
    Entity.jpm -> "JPM",
    Entity.cs -> "78e3e88c-b79d-45ae-b57d-02f1e3dd7e96",
    Entity.barclays -> "Barclays",
    Entity.hsbc -> "HSBC",
    Entity.wellsFargo -> "Wells Fargo",
    Entity.morganStanley -> "Morgan Stanley",

    // new issuers
    Entity.bnp -> "BNP",
    Entity.creditAgricole -> "784cfda9-bd19-4b37-8623-e61473f2c023",
    Entity.cibc -> "0bf364f2-7bf1-4da7-b774-ee77db5d0130",
    Entity.citi -> "Citi",
    Entity.nbc -> "9098bc73-9ceb-4030-a056-9a4bd2b20a61",
    Entity.ubs -> "68d552be-35af-4bf0-9047-7fba2c181c4c",
    Entity.socGen -> "d94dd81b-3e38-489c-9400-c2f20cdaf56b",
    Entity.rbc -> "abe6baa6-d88e-48f5-8a7c-d13890374640",
    Entity.td  -> "c32e641c-c475-4edb-8239-f8f75616749b",


    Entity.bns -> "0d482f86-9f80-42ee-8224-dc2cc2d1a0ad",
    Entity.bankOfAmerica -> "61c34a8d-dfef-4da4-8f97-************",
    Entity.bmo -> "92ebb132-53a9-46c6-a8ef-1c686124b8c5",
    Entity.jefferies -> "1ddd2159-09cd-402e-a081-cebca24d53e6",
    Entity.bbva -> "c06b2463-3741-43e4-a810-882d26df14a6",
    //test issuer
    Entity.testEntity -> "Test Issuer 1",
    Entity.simon -> "Test Issuer 2",
    Entity.spTestEntity -> "c6cb195e-246f-47bf-a9df-b00ab737a95a",

    // distributor
    Entity.mariner -> "5948a1d9-e74f-428c-9ae1-9b1b7f6f6388"
  )
  val issuerKeysByEntity: Map[String, List[String]] = Map(
    Entity.goldmanSachs -> List(IssuerKeys.gsGroup, IssuerKeys.gsfc, IssuerKeys.gsBank, IssuerKeys.gsi, IssuerKeys.gsfcUploadedByWF, IssuerKeys.gsBankUploadedByWF, IssuerKeys.gsfcUploadedByAxio, IssuerKeys.gsBankUploadedByAxio, IssuerKeys.gspwm),
    Entity.jpm -> List(IssuerKeys.jpmf, IssuerKeys.jpm, IssuerKeys.jpmBank, IssuerKeys.jpmBankUploadedByWF, IssuerKeys.jpmBankUploadedByAxio, IssuerKeys.jpmfUploadedByAxio),
    Entity.cs -> List(IssuerKeys.cs, IssuerKeys.csUploadedByWF),
    Entity.barclays -> List(IssuerKeys.bcs, IssuerKeys.bcsBank, IssuerKeys.bcsUploadedByWF),
    Entity.hsbc -> List(IssuerKeys.hsbc, IssuerKeys.hsbcBank, IssuerKeys.hbeu, IssuerKeys.hsbcUploadedByWF, IssuerKeys.hsbcBankUploadedByWF),
    Entity.wellsFargo -> List(IssuerKeys.wfCo, IssuerKeys.wfBank, IssuerKeys.wfFinance),
    Entity.morganStanley -> List(IssuerKeys.msf, IssuerKeys.msBank, IssuerKeys.ms, IssuerKeys.msBv, IssuerKeys.msPBNA, IssuerKeys.msBankUploadedByWF, IssuerKeys.msUploadedByWF, IssuerKeys.msBankUploadedByAxio, IssuerKeys.msfUploadedByAxio),

    // new issuers
    Entity.bnp -> List(IssuerKeys.bnp, IssuerKeys.bankOfTheWest, IssuerKeys.bnpBV, IssuerKeys.bowUploadedByWF),
    Entity.creditAgricole -> List(IssuerKeys.creditAgricole, IssuerKeys.creditAgricoleFS),
    Entity.cibc -> List(IssuerKeys.cibc, IssuerKeys.cibcUploadedByGS, IssuerKeys.cibcUploadedByWF),
    Entity.citi -> List(IssuerKeys.citi, IssuerKeys.citiBank, IssuerKeys.citiGlobal, IssuerKeys.citiGlobalMarketsFundingLuxembourg, IssuerKeys.citiGlobalUploadedByWF, IssuerKeys.citiGlobalUploadedByAxio, IssuerKeys.citiBankUploadedByAxio),
    Entity.nbc -> List(IssuerKeys.nbc, IssuerKeys.nbcUploadedByGS, IssuerKeys.nbcUploadedByCS),
    Entity.ubs -> List(IssuerKeys.ubs, IssuerKeys.ubsAG),
    Entity.socGen -> List(IssuerKeys.socGen),
    Entity.rbc -> List(IssuerKeys.rbc, IssuerKeys.rbcUploadedByWF),
    Entity.td  -> List(IssuerKeys.td, IssuerKeys.tdUploadedByGS, IssuerKeys.tdUploadedByWF, IssuerKeys.tdUploadedByAxio),


    Entity.bns -> List(IssuerKeys.bankOfNovaScotia, IssuerKeys.bankOFNovaScotiaUploadedByGS, IssuerKeys.bankOfNovaScotiaUploadedByWF),
    Entity.bankOfAmerica -> List(IssuerKeys.bankOfAmerica, IssuerKeys.bOfAFinance, IssuerKeys.merrillLynchBV, IssuerKeys.bOfANA, IssuerKeys.bOfAFinanceUploadedByAxio, IssuerKeys.bOfANAUploadedByAxio),
    Entity.bmo -> List(IssuerKeys.bmo, IssuerKeys.bmoUploadedByWF, IssuerKeys.bmoHarris),
    Entity.jefferies -> List(IssuerKeys.jefferies),
    Entity.bbva -> List(IssuerKeys.bbvaMarkets, IssuerKeys.bbvaSecurities),
    //test issuer
    Entity.testEntity -> List(IssuerKeys.testEntityNote, IssuerKeys.testEntityRegSNote, IssuerKeys.testEntityCD),
    Entity.simon -> List(IssuerKeys.simonNote, IssuerKeys.simonCD),
    Entity.spTestEntity -> List(IssuerKeys.spTestEntityNote, IssuerKeys.spTestEntityCD),

    // distributor
    Entity.mariner -> List(IssuerKeys.marinerNote, IssuerKeys.marinerCD)
  )
}
object IssuerLookup {

  val lookup = Map(
    IssuerKeys.simonNote -> IssuerInfo(
      issuerShortName = "SIMON",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.simonNote,
      issuer = "SIMON Markets LLC",
      issuerSymbol = "SIMON",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>"))),
      regS = true
    ),
    IssuerKeys.simonCD -> IssuerInfo(
      issuerShortName = "SIMON",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.simonCD,
      issuer = "SIMON Markets LLC",
      issuerSymbol = "SIMON",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>")))
    ),

    IssuerKeys.spTestEntityNote -> IssuerInfo(
      issuerShortName = "spTestEntity",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.spTestEntityNote,
      issuer = "SP Test issuer",
      issuerSymbol = "SPTEST"
    ),
    IssuerKeys.spTestEntityCD -> IssuerInfo(
      issuerShortName = "spTestEntity",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.spTestEntityCD,
      issuer = "SP Test issuer",
      issuerSymbol = "SPTEST"
    ),

    IssuerKeys.testEntityNote -> IssuerInfo(
      issuerShortName = "testEntity",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.testEntityNote,
      issuer = "Test issuer",
      issuerSymbol = "TEST",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>")))
    ),
    IssuerKeys.testEntityRegSNote -> IssuerInfo(
      issuerShortName = "testEntity",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.testEntityRegSNote,
      issuer = "Test issuer",
      issuerSymbol = "TEST",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      regS = true
    ),
    IssuerKeys.testEntityCD -> IssuerInfo(
      issuerShortName = "testEntity",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.testEntityCD,
      issuer = "Test issuer",
      issuerSymbol = "TEST"
    ),
    IssuerKeys.msf -> IssuerInfo(
      issuerShortName = "Morgan Stanley",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.msf,
      issuer = "MORGAN STANLEY FINANCE LLC",
      issuerSymbol = "MS",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>")))
    ),
    IssuerKeys.ms -> IssuerInfo(
      issuerShortName = "Morgan Stanley",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.ms,
      issuer = "MORGAN STANLEY",
      issuerSymbol = "MS",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>"))),
      legacy = true
    ),
    IssuerKeys.msBank -> IssuerInfo(
      issuerShortName = "Morgan Stanley",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.msBank,
      issuer = "MORGAN STANLEY BANK, NATIONAL ASSOCIATION",
      issuerSymbol = "MS",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>")))
    ),
    IssuerKeys.msBv -> IssuerInfo(
      issuerShortName = "Morgan Stanley",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.msBv,
      issuer = "Morgan Stanley B.V.",
      issuerSymbol = "MS",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>"))),
      regS = true
    ),
    IssuerKeys.msPBNA -> IssuerInfo(
      issuerShortName = "Morgan Stanley Private Bank",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.msPBNA,
      issuer = "Morgan Stanley Private Bank, N.A.",
      issuerSymbol = "MSPBNA",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true //tbd on where this will be housed
    ),
    IssuerKeys.citi -> IssuerInfo(
      issuerShortName = "CITI",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.citi,
      issuer = "CITIGROUP INC.",
      issuerSymbol = "CITI",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      legacy = true
    ),
    IssuerKeys.citiGlobal -> IssuerInfo(
      issuerShortName = "CITI",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.citiGlobal,
      issuer = "CITIGROUP GLOBAL MARKETS HOLDINGS INC.",
      issuerSymbol = "CITI",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>")))
    ),
    IssuerKeys.citiGlobalMarketsFundingLuxembourg -> IssuerInfo(
      issuerShortName = "CITI",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.citiGlobalMarketsFundingLuxembourg,
      issuer = "CITIGROUP GLOBAL MARKETS FUNDING LUXEMBOURG S.C.A.",
      issuerSymbol = "CITI",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      regS = true,
    ),
    IssuerKeys.citiBank -> IssuerInfo(
      issuerShortName = "CITI",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.citiBank,
      issuer = "Citibank",
      issuerSymbol = "CITI",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>")))
    ),
    IssuerKeys.ubsAG -> IssuerInfo(
      issuerShortName = "UBS",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.ubsAG,
      issuer = "UBS AG",
      issuerSymbol = "UBS"
    ),
    IssuerKeys.ubs -> IssuerInfo(
      issuerShortName = "UBS",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.ubs,
      issuer = "UBS",
      issuerSymbol = "UBS",
      legacy = true
    ),
    IssuerKeys.jefferies -> IssuerInfo(
      issuerShortName = "Jefferies",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.jefferies,
      issuer = "Jefferies Financial Group Inc",
      issuerSymbol = "JEF"
    ),
    IssuerKeys.jpmBank -> IssuerInfo(
      issuerShortName = "JP Morgan",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.jpmBank,
      issuer = "JPMorgan Chase Bank NA",
      issuerSymbol = "JPM"
    ),
    IssuerKeys.jpmf -> IssuerInfo(
      issuerShortName = "JP Morgan",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.jpmf,
      issuer = "JPMORGAN CHASE FINANCIAL COMPANY LLC",
      issuerSymbol = "JPM"
    ),
    IssuerKeys.jpm -> IssuerInfo(
      issuerShortName = "JP Morgan",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.jpm,
      issuer = "JPMORGAN CHASE and CO.",
      issuerSymbol = "JPM",
      legacy = true
    ),
    IssuerKeys.gsfc -> IssuerInfo(
      issuerShortName = "Goldman Sachs",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.gsfc,
      issuer = "(GS) GS FINANCE CORP.",
      issuerSymbol = "GS",
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************")))
    ),
    IssuerKeys.gsBank -> IssuerInfo(
      issuerShortName = "Goldman Sachs",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.gsBank,
      issuer = "(GS) GOLDMAN SACHS BANK U S A",
      issuerSymbol = "GS",
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************")))
    ),
    IssuerKeys.gsGroup -> IssuerInfo(
      issuerShortName = "Goldman Sachs",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.gsGroup,
      issuer = "(GS) GOLDMAN SACHS GROUP, INC. (THE)",
      issuerSymbol = "GS",
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************"))),
      legacy = true
    ),
    IssuerKeys.gsi -> IssuerInfo(
      issuerShortName = "Goldman Sachs",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.gsi,
      issuer = "(GS) GOLDMAN SACHS INTERNATIONAL",
      issuerSymbol = "GS",
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************"))),
      regS = true
    ),
    IssuerKeys.gspwm -> IssuerInfo(
      issuerShortName = "Goldman Sachs PWM",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.gspwm,
      issuer = "(GS) GOLDMAN SACHS Private Wealth Management",
      issuerSymbol = "GS",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.cibc -> IssuerInfo(
      issuerShortName = "Canadian Imperial",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.cibc,
      issuer = "CANADIAN IMPERIAL BANK OF COMMERCE",
      issuerSymbol = "CIBC"
    ),
    IssuerKeys.cibcUploadedByGS -> IssuerInfo(
      issuerShortName = "Canadian Imperial",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.cibcUploadedByGS,
      issuer = "CANADIAN IMPERIAL BANK OF COMMERCE",
      issuerSymbol = "CIBC",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.bankOfAmerica -> IssuerInfo(
      issuerShortName = "Bank of America",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = "BAC",
      issuer = "BANK OF AMERICA CORPORATION",
      issuerSymbol = "BAC"
    ),
    IssuerKeys.bOfAFinance -> IssuerInfo(
      issuerShortName = "Bank of America",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = "BAFin",
      issuer = "B of A Finance LLC",
      issuerSymbol = "BAC",
      legacy = true
    ),
    IssuerKeys.merrillLynchBV -> IssuerInfo(
      issuerShortName = "Bank of America",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = "MLBV",
      issuer = "Merrill Lynch BV",
      issuerSymbol = "BAC",
      legacy = true
    ),
    IssuerKeys.bOfANA -> IssuerInfo(
      issuerShortName = "Bank of America",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = "BANA",
      issuer = "Bank of America NA",
      issuerSymbol = "BAC",
      legacy = true
    ),
    IssuerKeys.bankOfNovaScotia -> IssuerInfo(
      issuerShortName = "Bank Nova Scotia",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bankOfNovaScotia,
      issuer = "BANK OF NOVA SCOTIA (THE)",
      issuerSymbol = "BNS"
    ),
    IssuerKeys.bankOFNovaScotiaUploadedByGS -> IssuerInfo(
      issuerShortName = "Bank Nova Scotia",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bankOFNovaScotiaUploadedByGS,
      issuer = "BANK OF NOVA SCOTIA (THE)",
      issuerSymbol = "BNS",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.wfCo -> IssuerInfo(
      issuerShortName = "Wells Fargo",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.wfCo,
      issuer = "WELLS FARGO and COMPANY",
      issuerSymbol = "WF",
      legacy = true,
    ),
    IssuerKeys.wfFinance -> IssuerInfo(
      issuerShortName = "Wells Fargo",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.wfFinance,
      issuer = "WELLS FARGO FINANCE LLC",
      issuerSymbol = "WF"
    ),
    IssuerKeys.wfBank -> IssuerInfo(
      issuerShortName = "Wells Fargo",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.wfBank,
      issuer = "WELLS FARGO BANK, NATIONAL ASSOCIATION",
      issuerSymbol = "WF"
    ),
    IssuerKeys.bcs -> IssuerInfo(
      issuerShortName = "BARCLAYS",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bcs,
      issuer = "BARCLAYS BANK PLC",
      issuerSymbol = "BARC"
    ),
    IssuerKeys.bcsBank -> IssuerInfo(
      issuerShortName = "BARCLAYS",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.bcsBank,
      issuer = "BARCLAYS BANK DELAWARE",
      issuerSymbol = "BARC"
    ),
    IssuerKeys.hsbc -> IssuerInfo(
      issuerShortName = "HSBC",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.hsbc,
      issuer = "HSBC USA INC.",
      issuerSymbol = "HSBC",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>")))
    ),
    IssuerKeys.hbeu -> IssuerInfo( // reg-s issuer
      issuerShortName = "HSBC",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.hbeu,
      issuer = "HSBC BANK PLC",
      issuerSymbol = "HSBC",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      regS = true
    ),
    IssuerKeys.cs -> IssuerInfo(
      issuerShortName = "Credit Suisse",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.cs,
      issuer = "CREDIT SUISSE AG",
      issuerSymbol = "CS",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>")))
    ),
    IssuerKeys.td -> IssuerInfo(
      issuerShortName = "Toronto Dominion",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.td,
      issuer = "TORONTO-DOMINION BANK (THE)",
      issuerSymbol = "TD"
    ),
    IssuerKeys.tdUploadedByGS -> IssuerInfo(
      issuerShortName = "Toronto Dominion",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.tdUploadedByGS,
      issuer = "TORONTO-DOMINION BANK (THE)",
      issuerSymbol = "TD",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.rbc -> IssuerInfo(
      issuerShortName = "Royal Bank of Canada",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.rbc,
      issuer = "ROYAL BANK OF CANADA",
      issuerSymbol = "RBC"
    ),
    IssuerKeys.nbc -> IssuerInfo(
      issuerShortName = "National Bank of Canada",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.nbc,
      issuer = "NATIONAL BANK OF CANADA",
      issuerSymbol = "NBC",
      is3a2 = true
    ),
    IssuerKeys.nbcUploadedByGS -> IssuerInfo(
      issuerShortName = "National Bank of Canada",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.nbcUploadedByGS,
      issuer = "NATIONAL BANK OF CANADA",
      issuerSymbol = "NBC",
      is3a2 = true,
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.nbcUploadedByCS -> IssuerInfo(
      issuerShortName = "National Bank of Canada",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.nbcUploadedByCS,
      issuer = "NATIONAL BANK OF CANADA",
      issuerSymbol = "NBC",
      is3a2 = true,
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.hsbcBank -> IssuerInfo(
      issuerShortName = "HSBC",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.hsbcBank,
      issuer = "HSBC BANK USA, NATIONAL ASSOCIATION-HONG KONG BRANCH",
      issuerSymbol = "HSBC",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>")))
    ),
    IssuerKeys.bmo -> IssuerInfo(
      issuerShortName = "Bank of Montreal",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bmo,
      issuer = "BANK OF MONTREAL",
      issuerSymbol = "BMO"
    ),

    IssuerKeys.bmoHarris -> IssuerInfo(
      issuerShortName = "Bank of Montreal",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.bmoHarris,
      issuer = "BMO Harris Bank N.A.",
      issuerSymbol = "BMO"
    ),

    IssuerKeys.socGen -> IssuerInfo(
      issuerShortName = "Societe Generale",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.socGen,
      issuer = "SOCIETE GENERALE", // still need to confirm
      issuerSymbol = "SG",
      is3a2 = true
    ),

    IssuerKeys.creditAgricole -> IssuerInfo(
      issuerShortName = "Credit Agricole CIB",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.creditAgricole,
      issuer = "Credit Agricole Corporate and Investment Bank",
      issuerSymbol = "CA-CIB",
      is3a2 = true,
      isNonNativeIssuerKey = true
    ),

    IssuerKeys.creditAgricoleFS -> IssuerInfo(
      issuerShortName = "Credit Agricole CIB",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.creditAgricoleFS,
      issuer = "Credit Agricole CIB Financial Solutions",
      issuerSymbol = "CA-CIB",
      regS = true,
      isNonNativeIssuerKey = true
    ),

    IssuerKeys.bnp -> IssuerInfo(
      issuerShortName = "BNP Paribas",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bnp,
      issuer = "BNP Paribas",
      issuerSymbol = "BNP",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      is3a2 = true
    ),
    IssuerKeys.bankOfTheWest -> IssuerInfo( // BNP's CD issuing entity
      issuerShortName = "Bank of the West",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.bankOfTheWest,
      issuer = "Bank of the West, San Francisco, California",
      issuerSymbol = "BoW",
      is3a2 = true
    ),

    IssuerKeys.bnpBV -> IssuerInfo( // Reg-S
      issuerShortName = "BNP Paribas",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bnpBV,
      issuer = "BNP Paribas Issuance B.V.",
      issuerSymbol = "BNP",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      regS = true,
      is3a2 = true
    ),

    // distributors
    // set up for Mariner to test SP product ideas, solicit interest, and then remove from platform
    IssuerKeys.marinerNote -> IssuerInfo(
      issuerShortName = "Mariner",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.marinerNote,
      issuer = "Mariner Wealth Advisors",
      issuerSymbol = "MARINER",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.marinerCD -> IssuerInfo(
      issuerShortName = "Mariner",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.marinerCD,
      issuer = "Mariner Wealth Advisors",
      issuerSymbol = "MARINER",
      isNonNativeIssuerKey = true
    ),


    // annuities carriers
    IssuerKeys.prudential -> IssuerInfo(
      issuerShortName = "Pruco Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.prudential,
      issuer = "Pruco Life Insurance Company",
      issuerSymbol = IssuerKeys.prudential
    ),

    IssuerKeys.symetra -> IssuerInfo(
      issuerShortName = "Symetra Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.symetra,
      issuer = "Symetra Life Insurance Company",
      issuerSymbol = IssuerKeys.symetra
    ),

    IssuerKeys.greatAmerican -> IssuerInfo(
      issuerShortName = "Great American Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.greatAmerican,
      issuer = "Great American Life Insurance Company",
      issuerSymbol = IssuerKeys.greatAmerican
    ),

    IssuerKeys.equitable -> IssuerInfo(
      issuerShortName = "Equitable Financial Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.equitable,
      issuer = "Equitable Financial Life Insurance Company",
      issuerSymbol = IssuerKeys.equitable
    ),

    IssuerKeys.jacksonNational -> IssuerInfo(
      issuerShortName = "Jackson National Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.jacksonNational,
      issuer = "Jackson National Life Insurance Company",
      issuerSymbol = IssuerKeys.jacksonNational
    ),

    IssuerKeys.globalAtlantic -> IssuerInfo(
      issuerShortName = "Forethought Life Insurance Company, A Global Atlantic Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.globalAtlantic,
      issuer = "Forethought Life Insurance Company, A Global Atlantic Company",
      issuerSymbol = IssuerKeys.globalAtlantic
    ),

    IssuerKeys.aigSunAmerica -> IssuerInfo(
      issuerShortName = "American General Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.aigSunAmerica,
      issuer = "American General Life Insurance Company",
      issuerSymbol = IssuerKeys.aigSunAmerica
    ),

    IssuerKeys.aigAmericanGen -> IssuerInfo(
      issuerShortName = "American General Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.aigAmericanGen,
      issuer = "American General Life Insurance Company",
      issuerSymbol = IssuerKeys.aigAmericanGen
    ),

    IssuerKeys.aigWestNational -> IssuerInfo(
      issuerShortName = "American General Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.aigWestNational,
      issuer = "American General Life Insurance Company",
      issuerSymbol = IssuerKeys.aigWestNational
    ),

    IssuerKeys.brighthouse -> IssuerInfo(
      issuerShortName = "Brighthouse Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.brighthouse,
      issuer = "Brighthouse Life Insurance Company",
      issuerSymbol = IssuerKeys.brighthouse
    ),

    IssuerKeys.sammons -> IssuerInfo(
      issuerShortName = "Sammons Retirement Solutions/Midland Retirement Distributors",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.sammons,
      issuer = "Sammons Retirement Solutions/Midland Retirement Distributors",
      issuerSymbol = IssuerKeys.sammons
    ),

    IssuerKeys.northAmerican -> IssuerInfo(
      issuerShortName = "North American Company Life",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.northAmerican,
      issuer = "North American Company Life",
      issuerSymbol = IssuerKeys.northAmerican
    ),

    IssuerKeys.eagleLife -> IssuerInfo(
      issuerShortName = "Eagle Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.eagleLife,
      issuer = "Eagle Life Insurance Company",
      issuerSymbol = IssuerKeys.eagleLife
    ),

    IssuerKeys.americanEquity -> IssuerInfo(
      issuerShortName = "American Equity Investment Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.americanEquity,
      issuer = "American Equity Investment Life Insurance Company",
      issuerSymbol = IssuerKeys.americanEquity
    ),

    IssuerKeys.pacLife -> IssuerInfo(
      issuerShortName = "Pacific Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.pacLife,
      issuer = "Pacific Life Insurance Company",
      issuerSymbol = IssuerKeys.pacLife
    ),

    IssuerKeys.pacificLife -> IssuerInfo(
      issuerShortName = "Pacific Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.pacificLife,
      issuer = "Pacific Life Insurance Company",
      issuerSymbol = IssuerKeys.pacLife
    ),

    IssuerKeys.allianz -> IssuerInfo(
      issuerShortName = "Allianz Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.allianz,
      issuer = "Allianz Life Insurance Company",
      issuerSymbol = IssuerKeys.allianz
    ),

    IssuerKeys.fgLife -> IssuerInfo(
      issuerShortName = "Fidelity & Guaranty Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.fgLife,
      issuer = "Fidelity & Guaranty Life Insurance Company",
      issuerSymbol = IssuerKeys.fgLife
    ),

    IssuerKeys.standard -> IssuerInfo(
      issuerShortName = "Standard Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.standard,
      issuer = "Standard Insurance Company",
      issuerSymbol = IssuerKeys.standard
    ),

    IssuerKeys.lincoln -> IssuerInfo(
      issuerShortName = "Lincoln Financial Group",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.lincoln,
      issuer = "Lincoln Financial Group",
      issuerSymbol = IssuerKeys.lincoln
    ),

    IssuerKeys.nationwide -> IssuerInfo(
      issuerShortName = "Nationwide Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.nationwide,
      issuer = "Nationwide Life Insurance Company",
      issuerSymbol = IssuerKeys.nationwide
    ),

    IssuerKeys.massMutual -> IssuerInfo(
      issuerShortName = "Massachusetts Mutual Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.massMutual,
      issuer = "Massachusetts Mutual Life Insurance Company",
      issuerSymbol = IssuerKeys.massMutual
    ),

    IssuerKeys.nyLife -> IssuerInfo(
      issuerShortName = "New York Life Insurance and Annuity Corp.",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.nyLife,
      issuer = "New York Life Insurance and Annuity Corp.",
      issuerSymbol = IssuerKeys.nyLife
    ),

    IssuerKeys.delawareLife -> IssuerInfo(
      issuerShortName = "Delaware Life",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.delawareLife,
      issuer = "Delaware Life Insurance Company US",
      issuerSymbol = IssuerKeys.delawareLife
    ),

    IssuerKeys.athene -> IssuerInfo(
      issuerShortName = "Athene Annuity and Life Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.athene,
      issuer = "Athene Annuity and Life Company",
      issuerSymbol = IssuerKeys.athene
    ),

    IssuerKeys.westernSouthernIntegrityLife -> IssuerInfo(
      issuerShortName = "Western & Southern Financial Group",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.westernSouthernIntegrityLife,
      issuer = "Western & Southern Financial Group",
      issuerSymbol = IssuerKeys.westernSouthernIntegrityLife
    ),

    IssuerKeys.westernSouthern -> IssuerInfo(
      issuerShortName = "Western & Southern Financial Group",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.westernSouthern,
      issuer = "Western & Southern Financial Group",
      issuerSymbol = IssuerKeys.westernSouthern
    ),
    IssuerKeys.transamerica -> IssuerInfo(
      issuerShortName = "Transamerica Life Insurance Company",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.transamerica,
      issuer = "Transamerica Life Insurance Company",
      issuerSymbol = IssuerKeys.transamerica
    ),

    // Test carriers
    IssuerKeys.northSouth -> IssuerInfo(
      issuerShortName = "North South",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.northSouth,
      issuer = "North South Corporation",
      issuerSymbol = "NSCO"
    ),

    IssuerKeys.angelwing -> IssuerInfo(
      issuerShortName = "Angelwing",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.angelwing,
      issuer = "Angelwing Financial",
      issuerSymbol = IssuerKeys.angelwing
    ),

    // Target Outcome ETFs
    IssuerKeys.firstTrust -> IssuerInfo(
      issuerShortName = "First Trust",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.firstTrust,
      issuer = "First Trust Advisors L.P.",
      issuerSymbol = "FT"
    ),

    IssuerKeys.innovator -> IssuerInfo(
      issuerShortName = "Innovator",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.innovator,
      issuer = "Innovator Capital Management, LLC",
      issuerSymbol = "INNOV"
    ),

    IssuerKeys.allianzIM -> IssuerInfo(
      issuerShortName = "Allianz",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.allianzIM,
      issuer = "Allianz Investment Management LLC",
      issuerSymbol = "ALLIANZ"
    ),

    IssuerKeys.invesco -> IssuerInfo(
      issuerShortName = "Invesco",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.invesco,
      issuer = "Invesco Distributors, Inc. - Investments and Solutions",
      issuerSymbol = "INVESCO"
    ),

    // Test issuers
    IssuerKeys.delta -> IssuerInfo(
      issuerShortName = "Delta",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.delta,
      issuer = "Delta Investment Management LLC",
      issuerSymbol = "DELTA"
    ),

    IssuerKeys.beta -> IssuerInfo(
      issuerShortName = "Beta",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.beta,
      issuer = "Beta Advisors L.P.",
      issuerSymbol = "BETA"
    ),
    // For all deals sold into Wells Fargo Advisors (the distributor side), Wells Fargo (the issuer side) acts as
    // underwriter for all deals across all issuers.  As such, Wells Fargo (the issuer) will be uploading all of the
    // deals that they underwrite on behalf of all of the issuers.  In order to facilitate this, we need to create new
    // issuer keys for deals underwritten by Wells Fargo.

    IssuerKeys.bmoUploadedByWF -> IssuerInfo(
      issuerShortName = "Bank of Montreal",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bmoUploadedByWF,
      issuer = "BANK OF MONTREAL",
      issuerSymbol = "BMO",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.bowUploadedByWF -> IssuerInfo( // BNP's CD issuing entity
      issuerShortName = "Bank of the West",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.bowUploadedByWF,
      issuer = "Bank of the West, San Francisco, California",
      issuerSymbol = "BoW",
      is3a2 = true,
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.bcsUploadedByWF -> IssuerInfo(
      issuerShortName = "BARCLAYS",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bcsUploadedByWF,
      issuer = "BARCLAYS BANK PLC",
      issuerSymbol = "BARC",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.cibcUploadedByWF -> IssuerInfo(
      issuerShortName = "Canadian Imperial",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.cibcUploadedByWF,
      issuer = "CANADIAN IMPERIAL BANK OF COMMERCE",
      issuerSymbol = "CIBC",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.citiGlobalUploadedByWF -> IssuerInfo(
      issuerShortName = "CITI",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.citiGlobalUploadedByWF,
      issuer = "CITIGROUP GLOBAL MARKETS HOLDINGS INC.",
      issuerSymbol = "CITI",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.csUploadedByWF -> IssuerInfo(
      issuerShortName = "Credit Suisse",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.csUploadedByWF,
      issuer = "CREDIT SUISSE AG",
      issuerSymbol = "CS",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.gsBankUploadedByWF -> IssuerInfo(
      issuerShortName = "Goldman Sachs",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.gsBankUploadedByWF,
      issuer = "(GS) GOLDMAN SACHS BANK U S A",
      issuerSymbol = "GS",
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.gsfcUploadedByWF -> IssuerInfo(
      issuerShortName = "Goldman Sachs",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.gsfcUploadedByWF,
      issuer = "(GS) GS FINANCE CORP.",
      issuerSymbol = "GS",
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.hsbcBankUploadedByWF -> IssuerInfo(
      issuerShortName = "HSBC",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.hsbcBankUploadedByWF,
      issuer = "HSBC BANK USA, NATIONAL ASSOCIATION-HONG KONG BRANCH",
      issuerSymbol = "HSBC",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.hsbcUploadedByWF -> IssuerInfo(
      issuerShortName = "HSBC",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.hsbcUploadedByWF,
      issuer = "HSBC USA INC.",
      issuerSymbol = "HSBC",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.jpmBankUploadedByWF -> IssuerInfo(
      issuerShortName = "JP Morgan",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.jpmBankUploadedByWF,
      issuer = "JPMorgan Chase Bank NA",
      issuerSymbol = "JPM",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.msBankUploadedByWF -> IssuerInfo(
      issuerShortName = "Morgan Stanley",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.msBankUploadedByWF,
      issuer = "MORGAN STANLEY",
      issuerSymbol = "MS",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>"))),
      legacy = true,
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.msBankUploadedByWF -> IssuerInfo(
      issuerShortName = "Morgan Stanley",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.msBankUploadedByWF,
      issuer = "MORGAN STANLEY BANK, NATIONAL ASSOCIATION",
      issuerSymbol = "MS",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.rbcUploadedByWF -> IssuerInfo(
      issuerShortName = "Royal Bank of Canada",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.rbcUploadedByWF,
      issuer = "ROYAL BANK OF CANADA",
      issuerSymbol = "RBC",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.bankOfNovaScotiaUploadedByWF -> IssuerInfo(
      issuerShortName = "Bank Nova Scotia",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bankOfNovaScotiaUploadedByWF,
      issuer = "BANK OF NOVA SCOTIA (THE)",
      issuerSymbol = "BNS",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.tdUploadedByWF -> IssuerInfo(
      issuerShortName = "Toronto Dominion",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.tdUploadedByWF,
      issuer = "TORONTO-DOMINION BANK (THE)",
      issuerSymbol = "TD",
      isNonNativeIssuerKey = true
    ),

    // Axio (the wholesaler) will be uploading some deals on behalf of their issuers.  In order to control for
    // which deals are uploaded by Axio vs which deals are uploaded by the issuer directly, we need to create
    // new issuer keys for these Axio deals.
    IssuerKeys.bOfAFinanceUploadedByAxio -> IssuerInfo(
      issuerShortName = "Bank of America",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bOfAFinanceUploadedByAxio,
      issuer = "B of A Finance LLC",
      issuerSymbol = "BAC",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.bOfANAUploadedByAxio -> IssuerInfo(
      issuerShortName = "Bank of America",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.bOfANAUploadedByAxio,
      issuer = "Bank of America NA",
      issuerSymbol = "BAC",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.tdUploadedByAxio -> IssuerInfo(
      issuerShortName = "Toronto Dominion",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.tdUploadedByAxio,
      issuer = "TORONTO-DOMINION BANK (THE)",
      issuerSymbol = "TD",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.jpmBankUploadedByAxio -> IssuerInfo(
      issuerShortName = "JP Morgan",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.jpmBankUploadedByAxio,
      issuer = "JPMorgan Chase Bank NA",
      issuerSymbol = "JPM",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.jpmfUploadedByAxio -> IssuerInfo(
      issuerShortName = "JP Morgan",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.jpmfUploadedByAxio,
      issuer = "JPMORGAN CHASE FINANCIAL COMPANY LLC",
      issuerSymbol = "JPM",
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.gsfcUploadedByAxio -> IssuerInfo(
      issuerShortName = "Goldman Sachs",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.gsfcUploadedByAxio,
      issuer = "(GS) GS FINANCE CORP.",
      issuerSymbol = "GS",
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.gsBankUploadedByAxio -> IssuerInfo(
      issuerShortName = "Goldman Sachs",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.gsBankUploadedByAxio,
      issuer = "(GS) GOLDMAN SACHS BANK U S A",
      issuerSymbol = "GS",
      contactInfo = Some(ContactInfo(email = Some("<EMAIL>"), phone = Some("(*************"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.msBankUploadedByAxio -> IssuerInfo(
      issuerShortName = "Morgan Stanley",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.msBankUploadedByAxio,
      issuer = "MORGAN STANLEY BANK, NATIONAL ASSOCIATION",
      issuerSymbol = "MS",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.msfUploadedByAxio -> IssuerInfo(
      issuerShortName = "Morgan Stanley",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.msfUploadedByAxio,
      issuer = "MORGAN STANLEY FINANCE LLC",
      issuerSymbol = "MS",
      contactInfo = Some(ContactInfo(phone = None, email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.citiGlobalUploadedByAxio -> IssuerInfo(
      issuerShortName = "CITI",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.citiGlobalUploadedByAxio,
      issuer = "CITIGROUP GLOBAL MARKETS HOLDINGS INC.",
      issuerSymbol = "CITI",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.citiBankUploadedByAxio -> IssuerInfo(
      issuerShortName = "CITI",
      contractTypeWrapper = ContractTypeWrappers.cd,
      issuerKey = IssuerKeys.citiBankUploadedByAxio,
      issuer = "Citibank",
      issuerSymbol = "CITI",
      contactInfo = Some(ContactInfo(phone = Some("(*************"), email = Some("<EMAIL>"))),
      isNonNativeIssuerKey = true
    ),
    IssuerKeys.bbvaSecurities -> IssuerInfo(
      issuerShortName = "BBVA",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bbvaSecurities,
      issuer = "BBVA Global Securities B.V.",
      issuerSymbol = "BBVA",
      regS = true
    ),
    IssuerKeys.bbvaMarkets -> IssuerInfo(
      issuerShortName = "BBVA",
      contractTypeWrapper = ContractTypeWrappers.note,
      issuerKey = IssuerKeys.bbvaMarkets,
      issuer = "BBVA Global Markets B.V.",
      issuerSymbol = "BBVA",
      is3a2 = true
    )
  )
}
