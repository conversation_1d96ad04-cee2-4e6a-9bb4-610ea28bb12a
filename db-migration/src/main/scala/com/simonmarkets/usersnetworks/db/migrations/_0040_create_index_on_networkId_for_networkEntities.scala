package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0040_create_index_on_networkId_for_networkEntities(
    db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0040_create_index_on_networkId_for_networkEntities")
  log.info("Starting migration _0040_create_index_on_networkId_for_networkEntities")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val collection = mongoDB.getCollection("networkEntities")
    collection.createIndex(ascending("networkId"), new IndexOptions().unique(false)).toFuture.map(_ => ())
  }

}
