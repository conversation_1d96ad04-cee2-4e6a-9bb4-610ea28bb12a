package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.RfqTemplatesCapabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.migrations._0078_add_RfqTemplateCapabilities._
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters.{equal, in}
import org.mongodb.scala.model.Updates.{combine, set}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class _0078_add_RfqTemplateCapabilities(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0078_add_RfqTemplateCapabilities")

  log.info("Starting migration")

  val networkCollection: MongoCollection[Network] = db.getCollection[Network]("networks_new").withCodecRegistry(networkCodecRegistry)
  val oldCapabilities: Seq[String] = Seq("editRfqViaNetwork", "editRfqViaOwner", "viewNetworkTemplateRfqViaNetworkCapability", "viewRfqViaOwner")
  val capabilityField: String = "customRolesConfig.capabilities"

  private def getUpdatedCustomRolesConfig(customRolesConfig: Set[CustomRoleDefinition]): Set[CustomRoleDefinition] = {
    customRolesConfig.map(roleCapabilities => {
      val additionalCapabilities = Seq(
        if (roleCapabilities.capabilities.contains("editRfqViaNetwork")) Some(RfqTemplatesCapabilities.EditRfqTemplateViaNetworkCapability.name) else None,
        if (roleCapabilities.capabilities.contains("editRfqViaOwner")) Some(RfqTemplatesCapabilities.EditRfqTemplateViaUserCreatedCapability.name) else None,
        if (roleCapabilities.capabilities.contains("viewNetworkTemplateRfqViaNetworkCapability")) Some(RfqTemplatesCapabilities.ViewRfqTemplateViaNetworkCapability.name) else None,
        if (roleCapabilities.capabilities.contains("viewRfqViaOwner")) Some(RfqTemplatesCapabilities.ViewRfqTemplateViaUserCreatedCapability.name) else None
      ).flatten
      roleCapabilities.copy(capabilities = roleCapabilities.capabilities ++ additionalCapabilities)
    })
  }

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      networks <- networkCollection.find(in(capabilityField, oldCapabilities: _*)).toFuture

      updates <- Future.traverse(networks) { network => {
        val updatedCustomRolesConfig = getUpdatedCustomRolesConfig(network.customRolesConfig)
        log.info(s"Updating network ${network.id}")

        networkCollection.updateOne(
          equal("id", network.id),
          combine(
            set("customRolesConfig", updatedCustomRolesConfig)
          )
        ).toFuture.transformWith {
          case Success(result) => Future.successful(result.getModifiedCount)
          case Failure(ex) =>
            log.error(s"Error occurred while updating network ${network.id}", ex)
            Future.successful(0L)
        }
      }}
    } yield updates.sum
  }
}

object _0078_add_RfqTemplateCapabilities {
  case class Network(
      id: String,
      customRolesConfig: Set[CustomRoleDefinition] = Set.empty,
  )

  case class CustomRoleDefinition(
      role: String,
      capabilities: Set[String],
  )

  val networkCodecRegistry: CodecRegistry = fromRegistries(
    fromProviders(
      createCodecProviderIgnoreNone[Network],
      classOf[CustomRoleDefinition]
    ),
    DEFAULT_CODEC_REGISTRY
  )
}
