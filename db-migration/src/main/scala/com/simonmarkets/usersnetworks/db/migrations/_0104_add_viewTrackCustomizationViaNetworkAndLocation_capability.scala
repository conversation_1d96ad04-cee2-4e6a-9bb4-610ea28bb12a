package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.LearnV2TrackCustomizationsCapabilities._
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0104_add_viewTrackCustomizationViaNetworkAndLocation_capability(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0104_add_viewTrackCustomizationViaNetworkAndLocation_capability")

  def capabilities: Set[Capability] = Set(
    ViewTrackCustomizationViaNetworkAndLocationCapability.name
  )

  private val oldTrackCustomizationCapabilities: Set[Capability] = Set(
    ViewTrackCustomizationViaNetworkCapability.name
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig =>
    customRole == rolesConfig.role &&
    rolesConfig.capabilities.intersect(oldTrackCustomizationCapabilities).nonEmpty)
}
