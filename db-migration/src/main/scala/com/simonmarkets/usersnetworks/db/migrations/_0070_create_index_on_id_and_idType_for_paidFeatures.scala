package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0070_create_index_on_id_and_idType_for_paidFeatures(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0070_create_index_on_id_and_idType_for_paidFeatures")
  log.info("Starting migration _0070_create_index_on_id_and_idType_for_paidFeatures")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val collection = mongoDB.getCollection("users.paidFeatures")
    for {
      //Create new Index
      _ <- collection.createIndex(ascending("id", "idType"), new IndexOptions().unique(true)).toFuture
    } yield()
  }
}
