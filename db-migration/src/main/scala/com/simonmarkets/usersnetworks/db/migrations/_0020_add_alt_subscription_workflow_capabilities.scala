package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.SimonUICapabilities.ViewUIIAltsSubscriptionWorkflowCapability
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.migrations._0020_add_alt_subscription_workflow_capabilities.{ALTS_FA_MANAGER_ROLE, ALTS_FA_ROLE}
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0020_add_alt_subscription_workflow_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0020_add_alt_subscription_workflow_capabilities")

  def capabilities: Set[Capability] = Set(ViewUIIAltsSubscriptionWorkflowCapability.name)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = customRole == ALTS_FA_MANAGER_ROLE || customRole == ALTS_FA_ROLE

}

object _0020_add_alt_subscription_workflow_capabilities {
  val ALTS_FA_MANAGER_ROLE = "AltsFAManager"
  val ALTS_FA_ROLE = "AltsFA"
}