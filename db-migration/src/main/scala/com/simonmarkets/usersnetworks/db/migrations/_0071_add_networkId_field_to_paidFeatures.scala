package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoDatabase, _}
import org.mongodb.scala.model.UpdateOneModel

import scala.concurrent.{ExecutionContext, Future}

class _0071_add_networkId_field_to_paidFeatures(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0071_add_networkId_field_to_paidFeatures")
  log.info(s"Starting migration _0071_add_networkId_field_to_paidFeatures")


  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val paidFeaturesCollection: MongoCollection[Document] = mongoDB.getCollection("users.paidFeatures")
    val userCollection: MongoCollection[Document] = mongoDB.getCollection("users")

    for {
      // Step 1: Query for all paidFeatures
      paidFeatures <- paidFeaturesCollection.find().toFuture()
      updateModelsFutures = paidFeatures.map { paidFeatureDocument =>
        val id = paidFeatureDocument.getString("id")
        // Step 2: Query the users collection to get the user item using the "id"
        val userItemOptionFuture = userCollection.find(equal("id", id)).headOption()
        // Step 3: Grab the value "networkId" from the user item
        userItemOptionFuture.map {
          case Some(userItem) =>
            val networkId = userItem.getString("networkId")
            val filter = equal("id", id)
            val update = combine(set("networkId", networkId))
            //Step 4: Create UpdateOneModel
            Some(new UpdateOneModel[Document](filter, update))
          case None =>
            None
        }
      }
      updateModels <- Future.sequence(updateModelsFutures).map(_.flatten)
      // Step 5: bulkWrite on updates
      _ <- paidFeaturesCollection.bulkWrite(updateModels).toFuture()
    } yield ()
  }
}