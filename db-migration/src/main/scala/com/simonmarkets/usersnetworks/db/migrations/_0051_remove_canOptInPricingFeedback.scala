package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.model.Filters.{equal, exists}
import org.mongodb.scala.model.{ReplaceOneModel, ReplaceOptions}
import org.mongodb.scala.{Document, MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0051_remove_canOptInPricingFeedback(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0051_remove_canOptInPricingFeedback")
  val networkCollection: MongoCollection[Document] = db.getCollection[Document]("networks_new").withCodecRegistry(DEFAULT_CODEC_REGISTRY)

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      networks <- networkCollection.find(exists("capabilities.canOptInPricingFeedback")).toFuture()
        .map(docs => docs.map(doc => NetworkFormat.read(doc)))
      _ = log.info(s"Found ${networks.size} to update")
      databaseWrites = networks.map(network => {
        val updatedNetwork = network.copy(capabilities = network.capabilities - "canOptInPricingFeedback")
        ReplaceOneModel(equal("id", network.id), NetworkFormat.write(updatedNetwork), ReplaceOptions().upsert(false))
      })
      bulkWrites <- networkCollection.bulkWrite(databaseWrites).toFuture()
      _ = log.info(s"Successfully updated capabilities in ${bulkWrites.getModifiedCount} networks")
    } yield ()
  }

}
