package com.simonmarkets.usersnetworks.db.migrations


import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.model.Indexes.ascending
import org.mongodb.scala.MongoDatabase

import scala.concurrent.{ExecutionContext, Future}

class _0021_networks_snapshot_index(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0021_networks_snapshot_index")
  log.info("Starting migration")

  val networksSnapshotColl = mongoDB.getCollection("networks_new.snapshots")

  val id = "id"

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
    _ <- networksSnapshotColl.createIndex(key = ascending(id)).toFuture()
    _ = log.info(s"Networks snapshot collection index created on $id")
    } yield ()
  }
}