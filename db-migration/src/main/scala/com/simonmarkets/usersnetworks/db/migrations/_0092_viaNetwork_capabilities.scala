package com.simonmarkets.usersnetworks.db.migrations

import akka.actor.ActorSystem
import akka.stream.scaladsl.{Sink, Source}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.service.NetworkAcceptedAccessKeysGenerator
import com.simonmarkets.users.common.User
import com.simonmarkets.users.repository.encoders.UserFormat
import com.simonmarkets.users.service.UserAcceptedAccessKeysGenerator
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.model.UpdateOneModel
import org.mongodb.scala.model.Updates.{combine, set}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}
import scala.reflect.ClassTag

class _0092_viaNetwork_capabilities(
    mongoDB: MongoDatabase) extends ChangeSet(mongoDB) with JsonCodecs with TraceLogging {

  private implicit val traceId: TraceId = TraceId("_0092_viaNetwork_capabilities")
  implicit val ac: ActorSystem = ActorSystem("networks_capabilities")

  private val STREAM_GROUP = 1000
  private val STREAM_PARALLELISM = 5
  private val ENTITLEMENTS = "entitlements"
  private val ID = "id"
  private val NETWORK = "networks_new"
  private val USER = "users"


  object NetworkKeyGenerator extends NetworkAcceptedAccessKeysGenerator {

  }

  object UserKeyGenerator extends UserAcceptedAccessKeysGenerator {

  }


  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      _ <- updateAccessKeysGeneric(
        mongoDB.getCollection[Document](NETWORK),
        ENTITLEMENTS,
        NetworkKeyGenerator.getAcceptedAccessKeys,
        (network: Network) => network.id.toString,
        (doc: Document) => NetworkFormat.read(doc)
      )
      _ <- updateAccessKeysGeneric(
        mongoDB.getCollection[User](USER).withCodecRegistry(UserFormat.codecRegistry),
        ENTITLEMENTS,
        UserKeyGenerator.getAcceptedAccessKeys,
        (user: User) => user.id,
        (user: User) => user
      )
    } yield ()
  }

  def updateAccessKeysGeneric[T: ClassTag, W: ClassTag](
      collection: MongoCollection[W],
      accessKeyFieldName: String,
      keyGeneratorFunction: T => Set[String],
      idFunction: T => String,
      conversion: W => T
  )(implicit ec: ExecutionContext, ac: ActorSystem): Future[Unit] = {
    for {
      _ <- Source
        .fromPublisher(collection.find)
        .grouped(STREAM_GROUP)
        .mapAsync(STREAM_PARALLELISM) { docs =>
          val updates = docs.map {
            doc =>
              val docObj = conversion(doc)
              val updatedAccessKeys = keyGeneratorFunction(docObj)
              UpdateOneModel(
                filter = equal(ID, idFunction(docObj)),
                update = combine(
                  set(accessKeyFieldName, updatedAccessKeys)
                )
              )
          }
          log.info(s"Updating ${updates.size} ${collection.namespace.getCollectionName}")
          if (updates.nonEmpty) collection.bulkWrite(updates).toFuture.map(_ => ())
          else Future.successful(())
        }
        .runWith(Sink.seq)
    } yield ()
  }

}