package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0061_remove_spectrumLegacy_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0061_remove_viewUIOptimizationToolAlts_viewUISpectrumLC2Alts_viewUIOptimizationToolAltsPDF_viewUISpectrumAltsPDF_capabilities")

  def capabilities: Set[Capability] = Set("viewUIOptimizationToolAlts", "viewUISpectrumLC2Alts", "viewUIOptimizationToolAltsPDF", "viewUISpectrumAltsPDF")

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network, customRole: Role): Boolean = true

}
