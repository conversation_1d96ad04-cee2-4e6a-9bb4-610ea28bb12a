package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0063_create_index_on_userId_for_userPaidFeatures(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0063_create_index_on_userId_for_users.paidFeatures")
  log.info("Starting migration _0063_create_index_on_userId_for_users.paidFeatures")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val collection = mongoDB.getCollection("users.paidFeatures")
    collection.createIndex(ascending("userId"), new IndexOptions().unique(true)).toFuture.map(_ => ())
  }
}
