package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.UserRole.EqPIPGFAManager
import com.simonmarkets.capabilities.LearnV2TrackCustomizationsCapabilities._
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0064_add_ViewTrackCustomizationViaNetworkCapability_to_FA_Managers(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0064_add_ViewTrackCustomizationViaNetworkCapability_to_FA_Managers")

  def capabilities: Set[Capability] = Set(ViewTrackCustomizationViaNetworkCapability.name)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig =>
    customRole == rolesConfig.role && rolesConfig.role == EqPIPGFAManager.productPrefix)
}