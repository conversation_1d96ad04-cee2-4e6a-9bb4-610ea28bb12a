package com.simonmarkets.usersnetworks.db.data.networkEntity

object ContractTypeWrappers {
  val note = "Note"
  val cd = "CD"
}

object Entity {
  // confirm these in onboarding tool in QA and Prod
  val ubs = "UBS"
  val rbc = "RBC"
  val nbc = "NBC"
  val creditAgricole = "Credit Agricole"
  val socGen = "SocGen"
  val bnp = "BNP"
  val citi = "Citi"
  val td = "TD"
  val cibc = "CIBC"
  /////////////////////////

  val rayJ = "RJ"
  val simon = "SIMON"
  val gsfc = "GSFC"
  val simonAdmin = "SIMON Admin"
  val testEntity = "testEntity"
  val spTestEntity = "SP TestEntity"
  val testGS = "Test GS"
  val goldmanSachs = "Goldman Sachs"
  val gspwm = "GS PWM"
  val hsbc = "HSBC"
  val barclays = "Barclays"
  val jpm = "JPM"
  val jefferies = "Jefferies"
  val wellsFargo = "Wells Fargo"
  val morganStanley = "Morgan Stanley"
  val cs = "CS"
  val bns = "BNS"
  val bankOfAmerica = "Bank of America"
  val simonEntities = List(simonAdmin, testGS)
  val bmo = "Bank of Montreal"
  val bmoHarris = "BMO Harris"
  val bbva = "BBVA"

  // distributors
  val mariner = "Mariner Wealth"

  val PlusSubscribe = "Plus Subscribe"
}

object IssuerKeys {
  // structured products issuers
  val simonNote = "simonNote"
  val simonCD = "simonCD"
  val testEntityNote = "TestEntityNote"
  val testEntityRegSNote = "TestEntityRegSNote"
  val testEntityCD = "TestEntityCD"
  val spTestEntityNote = "SPTestEntityNote"
  val spTestEntityCD = "SPTestEntityCD"
  val gsfc = "GSFC"
  val gsBank = "GSBank"
  val gsGroup = "GSGroup"
  val gsi = "GSILuxK"
  //  GSPWM is an issuer of BetaPlus while they are the distributor in SI
  val gspwm = "GSPWM"
  val td = "TD"
  val tdUploadedByGS = "TD_GS"
  val cibc = "CIBC"
  val cibcUploadedByGS = "CIBC_GS"
  val wfCo = "WFCo"
  val wfBank = "WFBank"
  val wfFinance = "WFFinance"
  val bcs = "BCS"
  val bcsBank = "BCSBank"
  val cs = "CS"
  val jpm = "JPM"
  val jefferies = "Jefferies"
  val jpmf = "JPMF"
  val jpmBank = "JPMBank"
  val hsbc = "HSBC"
  val hsbcBank = "HSBCBank"
  val hbeu = "HBEU"
  val msf = "MSF"
  val ms = "MS"
  val msBank = "MSBank"
  val msBv = "MSBV"
  val msPBNA = "MSPBNA"
  val citi = "CITI"
  val citiGlobal = "CitiGlobal"
  val citiBank = "Citibank"
  val citiGlobalMarketsFundingLuxembourg = "CGMFL" // reg-S issuer
  val ubs = "UBS"
  val ubsAG = "UBSAG"
  val socGen = "SocGen"
  val creditAgricole = "CreditAgricoleCIB"
  val creditAgricoleFS = "CreditAgricoleCIB_FS"
  val bnp = "BNP"
  val bankOfTheWest = "BOW"
  val bnpBV = "BNPBV"

  val bankOfAmerica = "BAC"
  val bOfAFinance = "BAFin"
  val merrillLynchBV = "MLBV"
  val bOfANA = "BANA"
  val bankOfNovaScotia = "BNS"
  val bankOFNovaScotiaUploadedByGS = "BNS_GS"
  val rbc = "RBC"
  val nbc = "NBC"
  val nbcUploadedByGS = "NBC_GS"
  val nbcUploadedByCS = "NBC_CS"
  val bmo = "BMO"
  val bmoHarris = "BMOHarris"
  val bbvaSecurities = "BBVASecurities"
  val bbvaMarkets = "BBVAMarkets"

  val bmoUploadedByWF = "BMO_WF"
  val bowUploadedByWF = "BOW_WF"
  val bcsUploadedByWF = "BCS_WF"
  val cibcUploadedByWF = "CIBC_WF"
  val citiGlobalUploadedByWF = "CitiGlobal_WF"
  val csUploadedByWF = "CS_WF"
  val gsBankUploadedByWF = "GSBank_WF"
  val gsfcUploadedByWF = "GSFC_WF"
  val hsbcBankUploadedByWF = "HSBCBank_WF"
  val hsbcUploadedByWF = "HSBC_WF"
  val jpmBankUploadedByWF = "JPMBank_WF"
  val msUploadedByWF = "MS_WF"
  val msBankUploadedByWF = "MSBank_WF"
  val rbcUploadedByWF = "RBC_WF"
  val bankOfNovaScotiaUploadedByWF = "BNS_WF"
  val tdUploadedByWF = "TD_WF"

  // uploaded by Axio
  val bOfAFinanceUploadedByAxio = "BAFin_Axio"
  val bOfANAUploadedByAxio = "BANA_Axio"
  val tdUploadedByAxio = "TD_Axio"
  val jpmBankUploadedByAxio = "JPMBank_Axio"
  val jpmfUploadedByAxio = "JPMF_Axio"
  val gsfcUploadedByAxio = "GSFC_Axio"
  val gsBankUploadedByAxio = "GSBank_Axio"
  val msBankUploadedByAxio = "MSBank_Axio"
  val msfUploadedByAxio = "MSF_Axio"
  val citiGlobalUploadedByAxio = "CitiGlobal_Axio"
  val citiBankUploadedByAxio = "CitiBank_Axio"

  // distributors
  val marinerNote = "MarinerNote"
  val marinerCD = "MarinerCD"

  val issuerKeysSeq = Seq(
    gsfc,
    gsBank,
    gsGroup,
    gsi,
    gspwm,
    td,
    cibc,
    wfCo,
    wfBank,
    wfFinance,
    bcs,
    bcsBank,
    cs,
    jefferies,
    jpm,
    jpmf,
    jpmBank,
    hsbc,
    hsbcBank,
    hbeu,
    msf,
    ms,
    msBank,
    msBv,
    msPBNA,
    citi,
    citiBank,
    citiGlobal,
    citiGlobalMarketsFundingLuxembourg,
    ubs,
    ubsAG,
    bankOfTheWest,
    bankOfNovaScotia,
    bnp,
    bnpBV,
    rbc,
    nbc,
    socGen,
    creditAgricole,
    creditAgricoleFS,
    bmo,
    testEntityNote,
    testEntityRegSNote,
    testEntityCD,
    bankOfAmerica,
    bOfAFinance,
    merrillLynchBV,
    bOfANA,
    spTestEntityCD,
    spTestEntityNote,
    simonCD,
    simonNote,
    marinerNote,
    marinerCD,
    bbvaSecurities,
    bbvaMarkets
  )

  // Annuities carriers
  val aigAmericanGen = "AMERICAN_GENERAL"
  val aigSunAmerica = "SUNAMERICA"
  val aigWestNational = "WESTERN_NATIONAL"
  val allianz = "ALLIANZ"
  val americanEquity = "AMERICAN_EQUITY"
  val athene = "ATHENE"
  val brighthouse = "METLIFE"
  val delawareLife = "DELAWARELIFE"
  val eagleLife = "EAGLE_LIFE_INSURANCE_COMPANY"
  val equitable = "EQUITABLE"
  val fgLife = "FGLLIFE"
  val globalAtlantic = "FORETHOUGHT_LIFE"
  val greatAmerican = "GALIC"
  val jacksonNational = "JACKSONNATIONAL"
  val lincoln = "LINCOLN"
  val massMutual = "MASS_MUTUAL"
  val nationwide = "NATIONWIDE"
  val northAmerican = "NORTH_AMERICAN"
  val nyLife = "NYL"
  val pacLife = "PACLIFE"
  val pacificLife = "PACIFIC_LIFE"
  val prudential = "PRU"
  val sammons = "MIDLAND_NATIONAL"
  val standard = "STANDARD"
  val symetra = "SYMETRA"
  val transamerica = "AEGON"
  val westernSouthern = "WESTERN_SOUTHERN"
  val westernSouthernIntegrityLife = "INTEGRITY_LIFE"

  // Annuities test carriers
  val northSouth = "NorthSouth"
  val angelwing = "ANGELWING_FINANCIAL"

  val annuitiesIssuersKeysSeq = Seq(
    aigAmericanGen,
    aigSunAmerica,
    aigWestNational,
    allianz,
    americanEquity,
    athene,
    brighthouse,
    delawareLife,
    eagleLife,
    equitable,
    fgLife,
    globalAtlantic,
    greatAmerican,
    jacksonNational,
    lincoln,
    massMutual,
    nationwide,
    northAmerican,
    nyLife,
    pacLife,
    pacificLife,
    prudential,
    sammons,
    standard,
    symetra,
    transamerica,
    westernSouthern,
    westernSouthernIntegrityLife,
    //Test Carrier
    northSouth,
    angelwing
  )

  // Target Outcome ETFs Issuers
  val firstTrust = "FirstTrust"
  val innovator = "Innovator"
  val allianzIM = "AllianzIM"
  val invesco = "Invesco"

  // Target Outcome ETFs Test Issuers
  val beta = "Beta"
  val delta = "Delta"

  val targetOutcomeETFsIssuersKeysSeq = Seq(
    firstTrust,
    innovator,
    allianzIM,
    invesco
  )

  // Alts Issuers
  val plusSubscribe = "PLUS_SUBSCRIBE"
  val altsIssuersKeys = Seq(
    plusSubscribe
  )

  // Alts Test Issuers
  val altsTestIssuer = "AltsTestIssuer"

  // TODO: remove after issuers complete migration
  val issuersSkipValidationSet = Set(
    socGen,
    bnp,
    ms,
    msf,
    msBank,
    bankOfTheWest,
    bnpBV
  )

}

