package com.simonmarkets.usersnetworks.db.utils

import com.goldmansachs.marquee.pipg.CustomRoleDefinition
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.Network
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.networks.common.encoders.NetworkFormat.CustomRoleDefinitionFormat
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.model.UpdateOneModel
import org.mongodb.scala.model.Updates.{combine, set}
import org.mongodb.scala.{BulkWriteResult, Document, MongoDatabase}
import simon.Id.NetworkId

import scala.concurrent.Future

/**
 * Base trait for database migrations that
 * (a) adds or removes (see `shouldAdd()`) ...
 * (b) some capabilities (see `capabilities())`) ...
 * (b) to all or some networks, for all or some custom roles (see `shouldUpdate(network, customRole)`)
 */
trait UpdateNetworkCapabilities extends TraceLogging {

  val db: MongoDatabase

  val networksCollection = db.getCollection[Document]("networks_new")

  implicit val traceId: TraceId

  type Capability = String
  type Role = String

  /**
   * Capabilities to add or remove
   */
  def capabilities: Set[Capability]

  /**
   * Whether to add or remove the specified capabilities
   *
   * @return true if this migration will add capabilities. false if it will remove them
   */
  def shouldAdd: Boolean

  /**
   * Whether the given network / custom role should be updated
   *
   * @return true if this migration will update capabilities for given network / role. false if it will remove them
   */
  def shouldUpdate(network: Network, customRole: Role): Boolean

  /**
   * Accumulates capabilities updates to networks
   *
   * @param allCustomRoleDefns     All the [[CustomRoleDefinition]]s in the network.
   *                               We update full networks instead of individual custom role definitions so these need to
   *                               be accumulated.
   * @param updatedCustomRoleDefns [[CustomRoleDefinition]]s that need to be updated. If, after accumulation, this set is
   *                               empty, it indicates that the network does not need to be updated.
   */
  case class NetworkUpdate(allCustomRoleDefns: Set[CustomRoleDefinition],
      updatedCustomRoleDefns: Set[CustomRoleDefinition]) {

    def withUpdate(customRoleDefn: CustomRoleDefinition, capabilitiesToChange: Set[Capability],
        add: Boolean): NetworkUpdate = {
      val newCapabilities =
        if (add) {
          customRoleDefn.capabilities ++ capabilitiesToChange
        } else {
          customRoleDefn.capabilities -- capabilitiesToChange
        }
      val newCustomRoleDefn = customRoleDefn.copy(capabilities = newCapabilities)
      NetworkUpdate(allCustomRoleDefns + newCustomRoleDefn, updatedCustomRoleDefns + newCustomRoleDefn)
    }

    def withNoUpdate(customRoleDefn: CustomRoleDefinition) =
      NetworkUpdate(allCustomRoleDefns + customRoleDefn, updatedCustomRoleDefns)

  }

  object NetworkUpdate {
    def initial: NetworkUpdate = NetworkUpdate(allCustomRoleDefns = Set(), updatedCustomRoleDefns = Set())
  }

  def apply(implicit executionContext : scala.concurrent.ExecutionContext): Future[Unit] = {
    log.info(s"Applying network update: add=$shouldAdd capabilities='$capabilities'")
    for {
      networks <- networksCollection.find().toFuture()
      updates = networks.map { dbo =>
        val network = NetworkFormat.read(dbo)
        val networkUpdate: NetworkUpdate = network
          .customRolesConfig
          .foldLeft(NetworkUpdate.initial) { case (networkUpdate: NetworkUpdate, currentCustomRoleDefn: CustomRoleDefinition) =>
            if (shouldUpdate(network, currentCustomRoleDefn.role)) {
              networkUpdate.withUpdate(currentCustomRoleDefn, this.capabilities, this.shouldAdd)
            } else {
              networkUpdate.withNoUpdate(currentCustomRoleDefn)
            }
          }
        if (networkUpdate.updatedCustomRoleDefns.nonEmpty) {
          val updatedRoles = networkUpdate.updatedCustomRoleDefns.map(_.role).mkString(",")
          log.info(s"Updating network: networkId=${network.id} networkName=${network.name} " +
            s"add=$shouldAdd capabilities='$capabilities' updatedRoles='$updatedRoles'")

          val customRolesList = networkUpdate.allCustomRoleDefns

          val setQuery = combine(
            set("customRolesConfig", customRolesList.map(definition => CustomRoleDefinitionFormat.write(definition)).toList),
            set("version", network.version + 1)
          )
          Some(UpdateOneModel(equal("id", NetworkId.unwrap(network.id)), setQuery))
        } else {
          log.info(s"Skipping network update: networkId=${network.id} networkName=${network.name}")
          None
        }
      }
      result <- if (updates.flatten.nonEmpty) networksCollection.bulkWrite(updates.flatten).toFuture else Future.successful(())
      _ = result match {
        case res: BulkWriteResult => log.info(s"Total network updates made: ${res.getModifiedCount}")
        case _ => log.info("No network updates were made")
      }
    } yield ()
  }

}
