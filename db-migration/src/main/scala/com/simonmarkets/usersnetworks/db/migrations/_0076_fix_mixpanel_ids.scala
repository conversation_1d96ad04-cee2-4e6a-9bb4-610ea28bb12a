package com.simonmarkets.usersnetworks.db.migrations

import akka.actor.ActorSystem
import akka.stream.scaladsl.{Sink, Source}
import com.simonmarkets.db.migration.model.{ChangeSet, CustomConfig}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.migrations._0075_add_mixpanel_ids.MaskedId
import com.simonmarkets.usersnetworks.db.migrations._0076_fix_mixpanel_ids.IdConfig
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoClient, MongoDatabase}
import pureconfig.generic.auto._

import java.util.UUID

import scala.concurrent.{ExecutionContext, Future}

class _0076_fix_mixpanel_ids(db: MongoDatabase) extends ChangeSet(db) with TraceLogging with CustomConfig {

  implicit val traceId: TraceId = TraceId("_0076_fix_mixpanel_ids")
  implicit val ac: ActorSystem = ActorSystem("mix_panel_ids")

  private val badId = loadConfig[IdConfig]

  //collections
  val users = mongoDB
    .getCollection("users")
    .withCodecRegistry(
      fromRegistries(
        MongoClient.DEFAULT_CODEC_REGISTRY,
        fromProviders(
          Macros.createCodecProvider[MaskedId]
        )
      )
    )

  //fields
  val MixPanel = "MixPanel"
  val MaskedIds = "maskedIds"
  val MaskedIdId = "maskedIds.id"

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {

      //clean up any users who have the bad/dupe id
      _ <- users
        .updateMany(
          filter = equal(MaskedIdId, badId.id),
          update = pull(MaskedIds, MaskedId(MixPanel, badId.id))
        )
        .toFuture

      //set any new users that don't have an id
      _ <-
        Source
          .fromPublisher(users.find(nin("maskedIds.target", MixPanel)))
          .mapAsync(1) { userDoc =>
            users
              .updateOne(
                equal("id", userDoc.getString("id")),
                push(MaskedIds, MaskedId(MixPanel, UUID.randomUUID().toString))
              )
              .toFuture
          }
          .runWith(Sink.seq)

    } yield ()
  }

}

object _0076_fix_mixpanel_ids {

  case class MaskedId(
      target: String,
      id: String
  )

  case class IdConfig(
      id: String
  )

}
