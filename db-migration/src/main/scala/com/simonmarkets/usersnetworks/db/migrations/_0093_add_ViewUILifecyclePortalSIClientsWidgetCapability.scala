package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.SimonUICapabilities.{ViewUILifecyclePortalSIClientsWidgetCapability, ViewNewSILifecycleDashboardCapability}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0093_add_ViewUILifecyclePortalSIClientsWidgetCapability(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0093_add_ViewUILifecyclePortalSIClientsWidgetCapability")

  def capabilities: Set[Capability] = Set(ViewUILifecyclePortalSIClientsWidgetCapability.name)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(_.capabilities.exists(_ == ViewNewSILifecycleDashboardCapability.name))
}
