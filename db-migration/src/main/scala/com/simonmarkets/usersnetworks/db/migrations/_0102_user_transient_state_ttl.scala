package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala._
import org.mongodb.scala.model.IndexOptions
import org.mongodb.scala.model.Indexes._

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}

class _0102_user_transient_state_ttl(mongoDB: MongoDatabase) extends ChangeSet(mongoDB) with TraceLogging {

  private implicit val traceId: TraceId = TraceId("_0102_user_transient_state_ttl")

  private val ttlSeconds = 600
  private val CreatedAt = "createdAt"
  private val UserId = "userId"
  private val indexOptions = IndexOptions().expireAfter(ttlSeconds, SECONDS)
  val col: MongoCollection[Document] = mongoDB.getCollection[Document]("users.transientState")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      _ <- col.createIndex(ascending(CreatedAt), indexOptions).toFuture()
      _ = log.info("ttl index created")
      _ <- col.createIndex(ascending(UserId), IndexOptions().unique(true)).toFuture()
      _ = log.info("userId index created")
    } yield ()
  }
}