package com.simonmarkets.usersnetworks.db.migrations

import akka.actor.ActorSystem
import akka.stream.scaladsl.{Sink, Source}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.common.{EventInfo, LoginMode, User, UserType}
import com.simonmarkets.users.repository.encoders.UserFormat
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Filters
import org.mongodb.scala.model.Updates._

import scala.concurrent.{ExecutionContext, Future}

class _0084_default_groups(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0084_default_groups")
  implicit val ac: ActorSystem = ActorSystem("migration")

  //collections
  val userCol = mongoDB
    .getCollection[User]("users")
    .withCodecRegistry(
      UserFormat.codecRegistry
    )

  //fields
  val Id: String = "id"

  val setFields = Set(
    "roles",
    "customRoles",
    "dynamicRoles",
    "entitlements",
    "locations",
    "faNumbers",
    "maskedIds",
    "licenses",
    "cusips",
    "purviewLicenses",
    "purviewNsccCodes",
    "icnGroups",
    "icnRoles",
  )

  val boolFields = Set(
    "tradewebEligible",
    "regSEligible",
    "isActive",
  )

  val listFields = Set(
    "externalIds"
  )

  //  special
  val EventInfoField = "eventInfo"
  val LoginModeField = "loginMode"
  val Passport = "passport"
  val UserTypeField = "userType"
  val Groups = "groups"
  val ContextLastUpdatedAt = "context.lastUpdatedAt"


  /**
   * Because we are switching from manual formatter for users to auto we need to set defaults for fields that were
   * formerly being decided in the formatter logic
   */
  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {

      //empty defaults for sets
      _ <- Source(setFields ++ listFields)
        .mapAsync(1) { field =>
          userCol.updateMany(
            Filters.exists(field, exists = false),
            set(field, Set.empty[String])
          ).toFuture
        }
        .runWith(Sink.seq)

      //empty defaults for bools
      _ <- Source(boolFields)
        .mapAsync(1) { field =>
          userCol.updateMany(
            Filters.exists(field, exists = false),
            set(field, false)
          ).toFuture
        }
        .runWith(Sink.seq)

      //empty defaults for special fields
      _ <- userCol.updateMany(
        Filters.exists(EventInfoField, exists = false),
        set(EventInfoField, EventInfo.Default)
      ).toFuture
      _ <- userCol.updateMany(
        Filters.exists(LoginModeField, exists = false),
        set(LoginModeField, LoginMode.EnumNotFound)
      ).toFuture
      _ <- userCol.updateMany(
        Filters.exists(Passport, exists = false),
        set(Passport, Map.empty[String, String])
      ).toFuture
      _ <- userCol.updateMany(
        Filters.exists(UserTypeField, exists = false),
        set(UserTypeField, UserType.EnumNotFound)
      ).toFuture
      _ <- userCol.updateMany(
        Filters.exists(Groups, exists = false),
        set(Groups, Map.empty[String, Seq[String]])
      ).toFuture
      _ <- userCol.updateMany(
        Filters.empty,
        unset(ContextLastUpdatedAt)
      ).toFuture

    } yield ()

  }

}
