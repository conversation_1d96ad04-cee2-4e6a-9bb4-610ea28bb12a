package com.simonmarkets.usersnetworks.db.migrations
import com.simonmarkets.db.migration.model.{ChangeSet, CustomConfig}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.repository.encoders.UserFormat
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.{Filters, UpdateOneModel}
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import pureconfig.generic.auto._

import scala.concurrent.{ExecutionContext, Future}

class _0043_remove_duplicate_idpIds (db: MongoDatabase) extends ChangeSet(db) with TraceLogging with CustomConfig {

  case class DuplicateIdList(ids: Set[String])
  private val duplicateIdList = loadConfig[DuplicateIdList]

  implicit val traceId: TraceId = TraceId("_0043_remove_duplicate_idpIds")
  log.info("Starting migration")

  //field names
  private val Id = "id"
  private val IdpId = "idpId"

  val usersCollection: MongoCollection[Document] = db.getCollection("users")


  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      usersToUpdate <- usersCollection.find(
        Filters.in(Id, duplicateIdList.ids.toSeq: _*),
      ).map(UserFormat.read).toFuture
      _ = log.info(s"Updating idpIds for ids ${usersToUpdate.map(_.id).mkString("\n")}")
      updates = usersToUpdate.map { u =>
        UpdateOneModel(
          equal(Id, u.id),
          unset(IdpId)
        )
      }
      _ <-
        if (updates.nonEmpty)
          usersCollection.bulkWrite(updates).toFuture
        else Future.unit
    } yield ()
  }
}
