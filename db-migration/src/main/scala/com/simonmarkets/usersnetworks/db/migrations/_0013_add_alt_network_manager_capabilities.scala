package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.AltOfferingCapabilities._
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.migrations._0013_add_alt_network_manager_capabilities.ALTS_FA_MANAGER_ROLE
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0013_add_alt_network_manager_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0013_add_alt_network_manager_capabilities")

  def capabilities: Set[Capability] = Set(ViewAltOfferingViaNetworkManager, ApproveAltOfferingViaNetworkManager, ViewAltOfferingViaPurviewNetworkManager, ApproveAltOfferingViaPurviewNetworkManager)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    customRole == ALTS_FA_MANAGER_ROLE)

}

object _0013_add_alt_network_manager_capabilities {
  val ALTS_FA_MANAGER_ROLE = "AltsFAManager"
}