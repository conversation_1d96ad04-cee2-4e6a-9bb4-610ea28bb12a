package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.NetworkType
import com.simonmarkets.capabilities.RfqConfigsCapabilities.ViewRfqConfigsViaNetworkIssuerPurviewCapability
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0056_add_ViewRfqConfigsViaNetworkIssuerPurviewCapability_to_issuers_and_wholesalers(
    val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0056_add_ViewRfqConfigsViaNetworkIssuerPurviewCapability_to_issuers_and_wholesalers")

  def capabilities: Set[Capability] = Set(ViewRfqConfigsViaNetworkIssuerPurviewCapability.name)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network,
      customRole: Role): Boolean = network.networkTypes.exists(networkType => networkType.contains(NetworkType.Issuer) || networkType.contains(NetworkType.Wholesaler))
}
