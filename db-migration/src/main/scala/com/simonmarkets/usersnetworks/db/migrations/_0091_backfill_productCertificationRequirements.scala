package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{CertificationAlternativesForProduct, ProductCertificationRequirements}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import com.simonmarkets.shared.ProductType
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.model.{ReplaceOneModel, ReplaceOptions}
import org.mongodb.scala.{Document, MongoCollection, MongoDatabase}
import org.mongodb.scala.model.Filters._

import scala.concurrent.{ExecutionContext, Future}

class _0091_backfill_productCertificationRequirements(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0091_backfill_productCertificationRequirements")
  val networkCollection: MongoCollection[Document] = db.getCollection[Document]("networks_new").withCodecRegistry(DEFAULT_CODEC_REGISTRY)

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    log.info("Starting migration _0091_backfill_productCertificationRequirements")
    for {
      networks <- networkCollection.find().toFuture
        .map(docs => docs.map(doc => NetworkFormat.read(doc)))
      networksCount = networks.size
      _ = log.info(s"Found $networksCount networks")

      updateModels = networks.map { network =>
        val productTypeReqs = combineCertificationRequirements(
          network.siCertificationRequirements,
          network.annuityCertificationRequirements,
          network.definedOutcomeETFCertificationRequirements,
          network.altCertificationRequirements)
        val updatedNetwork = network.copy(productTypeCertificationRequirements = Some(productTypeReqs))
        ReplaceOneModel(equal("id", network.id), NetworkFormat.write(updatedNetwork), ReplaceOptions().upsert(false))
      }
      bulkWrites <- networkCollection.bulkWrite(updateModels).toFuture()
      _ = log.info(s"Successfully updated productTypeCertificationRequirements field in ${bulkWrites.getModifiedCount} networks")
    } yield ()
  }

  def combineCertificationRequirements(
      siCertRequirements: Seq[CertificationAlternativesForProduct],
      annuityCertRequirements: Seq[CertificationAlternativesForProduct],
      definedOutcomeETFCertRequirements: Seq[CertificationAlternativesForProduct],
      altCertRequirements: Option[Seq[CertificationAlternativesForProduct]]
  ): Seq[ProductCertificationRequirements] = {
    def convertToOutput(reqs: Seq[CertificationAlternativesForProduct],
        productType: ProductType): Seq[ProductCertificationRequirements] = {
      Seq(ProductCertificationRequirements(productType, reqs))
    }

    val certifications = Seq(
      (siCertRequirements, ProductType.StructuredProduct),
      (annuityCertRequirements, ProductType.Annuities),
      (definedOutcomeETFCertRequirements, ProductType.TargetReturnETF),
    ) ++ altCertRequirements.map(reqs => (reqs, ProductType.AlternativeInvestment))

    certifications.flatMap { case (reqs, productType) =>
      convertToOutput(reqs, productType)
    }
  }
}