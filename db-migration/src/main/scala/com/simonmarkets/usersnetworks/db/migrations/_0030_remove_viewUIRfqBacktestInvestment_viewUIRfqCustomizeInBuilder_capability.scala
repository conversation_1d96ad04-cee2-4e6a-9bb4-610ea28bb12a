package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0030_remove_viewUIRfqBacktestInvestment_viewUIRfqCustomizeInBuilder_capability(val db: MongoDatabase)
    extends ChangeSet(db)
    with UpdateNetworkCapabilities {

  implicit val traceId: TraceId = TraceId("_0027_add_viewUIBacktestingComparisonCapability_capability")

  def capabilities: Set[Capability] = Set("viewUIRfqBacktestInvestment", "viewUIRfqCustomizeInBuilder")

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network, customRole: Role): Boolean = true

}


