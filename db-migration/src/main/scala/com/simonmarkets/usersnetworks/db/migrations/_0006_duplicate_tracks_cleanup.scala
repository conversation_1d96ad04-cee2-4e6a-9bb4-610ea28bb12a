package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.migrations._0006_duplicate_tracks_cleanup._
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import org.mongodb.scala.model.Filters.{equal, in}
import org.mongodb.scala.model.Updates.{combine, set, unset}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class _0006_duplicate_tracks_cleanup(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_6_duplicate_tracks_cleanup")

  val networkCollection: MongoCollection[Network] = db.getCollection[Network]("networks_new").withCodecRegistry(networkCodecRegistry)

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    getConfig match {
      case Some(config) =>
        updateNetworkTracks(config).map { updateCount =>
          log.info(s"Updated $updateCount networks.")
          ()
        }

      case None =>
        log.info("Unknown environment, aborting migration")
        Future.unit
    }
  }

  private def getConfig: Option[Map[String, List[String]]] = {
    val envOpt = Option(System.getProperty("simon.env"))
    val configOpt = envOpt.flatMap { env =>
      log.info(s"Environment is '$env'")
      env.toLowerCase match {
        case "qa" => Some(qaConfig)
        case "prod" => Some(prodConfig)
        case "test" => Some(testConfig)
        case _ => None
      }
    }
    configOpt
  }

  private def updateNetworkTracks(migrationConfig: Map[String, List[String]])(implicit executionContext: ExecutionContext) = {
    val duplicateIds = migrationConfig.values.flatten.toSeq
    val reverseMapping = migrationConfig.flatMap { case (trackId, duplicateIds) => duplicateIds.map(d => d -> trackId) }

    def getDeduplicatedLearnTracks(learnTracksV2: Seq[LearnTrack]) =
      learnTracksV2.map { learnTrack =>
        val id = reverseMapping.getOrElse(learnTrack.trackId, learnTrack.trackId)
        learnTrack.copy(trackId = id)
      }.distinct

    def getDeduplicatedRequirements(requirements: Seq[CertificationAlternativesForProduct]) =
      requirements.map { req =>
        val alternatives = req.certificationAlternatives
          .map(trackIds => trackIds.map(id => reverseMapping.getOrElse(id, id)).distinct)
          .distinct
        req.copy(certificationAlternatives = alternatives)
      }

    for {
      networks <- networkCollection.find(in("learnTracksV2.trackId", duplicateIds: _*)).toFuture
      updates <- Future.traverse(networks) { network =>
        val learnTracks = getDeduplicatedLearnTracks(network.learnTracksV2)
        val si = getDeduplicatedRequirements(network.siCertificationRequirements)
        val annuity = getDeduplicatedRequirements(network.annuityCertificationRequirements)
        val etf = getDeduplicatedRequirements(network.definedOutcomeETFCertificationRequirements)
        val alts = network.altCertificationRequirements.map(getDeduplicatedRequirements)

        log.info(s"Updating network ${network.id}")
        networkCollection
          .updateOne(
            equal("id", network.id),
            combine(
              set("learnTracksV2", learnTracks),
              set("siCertificationRequirements", si),
              set("annuityCertificationRequirements", annuity),
              set("definedOutcomeETFCertificationRequirements", etf),
              alts match {
                case Some(value) => set("altCertificationRequirements", value)
                case None => unset("altCertificationRequirements")
              }
            )
          )
          .toFuture
          .transformWith {
            case Success(result) => Future.successful(result.getModifiedCount)
            case Failure(ex) =>
              log.error(s"Error occurred while updating network ${network.id}", ex)
              Future.successful(0L)
          }
      }
    } yield updates.sum
  }

}

object _0006_duplicate_tracks_cleanup {

  case class Network(
      id: String,
      learnTracksV2: Seq[LearnTrack] = Seq.empty,
      siCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      annuityCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      definedOutcomeETFCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
      altCertificationRequirements: Option[Seq[CertificationAlternativesForProduct]] = None,
  )

  case class LearnTrack(
      trackId: String,
      isActive: Boolean
  )

  case class CertificationAlternativesForProduct(
      productId: String,
      productName: String,
      certificationAlternatives: Seq[Seq[String]]
  )

  val networkCodecRegistry: CodecRegistry =
    fromRegistries(
      fromProviders(
        createCodecProviderIgnoreNone[Network],
        classOf[LearnTrack],
        classOf[CertificationAlternativesForProduct]
      ),
      DEFAULT_CODEC_REGISTRY
    )

  val qaConfig: Map[String, List[String]] = Map(
    "2ed3b38a-5061-4582-899e-04406edfb2d3" -> List("9084cd6c-a781-4c97-962a-d8a36c6f0f39"),  // Symetra Advisory Edge 5 Shell_100101
    "2ee473ac-4f71-4ebe-a1b9-0b1f8ffd9e07" -> List("ed174f7e-7217-4c33-8b3f-295e2c914877"),  // Symetra Advantage Income (NY) Shell_100521
    "a5c584c6-9b95-4a05-9db4-629e781e53a3" -> List("e982fde1-2186-451d-9cee-8745538acece"),  // AIG Shell American Pathway 126401
    "7407293f-5b72-43dd-9b97-64362f8484ec" -> List("8d2aa089-33dc-4959-ac8b-23c23f2273fd"),  // Symetra Advisory Edge 7 Shell_130101
    "fd6709f7-7eea-4aac-b1a7-fe8d1ad2ddeb" -> List("133be3b9-59e4-4aaa-9a5d-d2bfc62c6e77"),  // PacShellE27089-17D
    "13782792-1c92-46c1-9004-2439e6ba31ea" -> List("e27e88ba-ca92-4749-a981-c95c06525926"),  // Nationwide Destination Freedom Plus
    "c0266d64-3cfd-43e1-b19f-42cd04305462" -> List("fa9e384d-d629-462f-8656-bc12ea2ac00d"),  // AIG Power Index Preferred & Power Protector
    "8f87c9b3-19e9-4b38-951b-0cfac18e371c" -> List("9d45b39d-813c-463d-812d-4f3a7fcbaa8a"),  // Nationwide Income Promise Select
    "c504337d-ec40-405e-89ac-000b0d2814e2" -> List("60e1e386-3b2a-42e1-baec-db7da0289935"),  // Athene MYG
    "bc834565-2741-4ab9-8de2-8db5edba9874" -> List("30843eff-5ab7-4262-a25b-3b7031e67ea7"),  // AIG Shell R5026PTR.10
    "c13598a7-1fac-496e-9116-ce16da75310b" -> List("e3491dc9-c576-4143-bedd-f455d8cacc41"),  // AIG Shell R5026PTR.4
    "f111fc77-1017-433d-a4ef-a4d9bf62a323" -> List("9e51ac06-7293-4e3d-a225-35a5f3f8d826"),  // AIG Shell R5026PTR.5
    "35cbeb80-5651-4ddb-bc78-6cb7376f8c3c" -> List("7d023c42-750f-4e75-a7d3-c19511925f89"),  // AIG Shell R5026PTR.6
    "96e7e680-701b-497e-b1b0-6d1c4f103516" -> List("00bb3f3c-83ed-4ced-b6dc-de1c43e5d688"),  // AIG Shell R5026PTR.7
    "909e173a-e019-4119-b20a-05e408c00473" -> List("0e0bd477-1493-4225-b401-10a02146f8a1"),  // AIG Shell R5026PTR.8
    "a9b2dba6-67c1-410e-b41b-e8374d40d205" -> List("f5a6258f-7ff1-4500-8f63-78e558c50397"),  // Pacific Life Variable Annuities Product Training
    "5673d639-d407-4531-8768-d58cbcd9f604" -> List("355ffc66-794f-4991-816f-224b6fc18011"),  // Symetra Select 5 (NY) Shell_100512
    "3f1a83b8-fb58-493f-bfe4-25f7b8e43190" -> List("8e3870db-69ce-47fc-96d0-ec182c33e64b"),  // Nationwide Jefferson Nationals's Monument Advisor
  )

  val prodConfig: Map[String, List[String]] = Map(
    "568302ce-fa4e-4469-8ea1-1e4e778e67c0" -> List("c766e270-2a4d-4a28-abd3-867a6d473bd1"),  // Symetra Advantage Income Training
    "2ed3b38a-5061-4582-899e-04406edfb2d3" -> List("9084cd6c-a781-4c97-962a-d8a36c6f0f39"),  // Symetra Advisory Edge 5 Shell_100101
    "20f68a32-d822-4ebf-b812-74131f982e6d" -> List("c4f11314-fe47-4591-b248-7d147562d832"),  // Symetra Select 5 & 7 Training
    "9487e112-6167-43d4-9c93-fcae67c4706c" -> List("bacb00db-093c-4831-9d9e-5bde092241cd"),  // Symetra Income Edge 7 Shell_100174
    "2ee473ac-4f71-4ebe-a1b9-0b1f8ffd9e07" -> List("ed174f7e-7217-4c33-8b3f-295e2c914877"),  // Symetra Advantage Income (NY) Shell_100521
    "cd98947e-3987-48bc-90b0-e18a13d0de95" -> List("0b77faaa-fd42-44c6-bfad-e18b8506cb30"),  // AIG shell 12AG_01
    "7407293f-5b72-43dd-9b97-64362f8484ec" -> List("8d2aa089-33dc-4959-ac8b-23c23f2273fd"),  // Symetra Advisory Edge 7 Shell_130101
    "290a6355-6210-45a0-be88-e0589daae0ed" -> List("a3b41b0f-5e36-46c7-8d76-5c297c6ada22"),  // Sammons Midland LiveWell Variable Annuity
    "3df43bd7-62f4-40e4-a4b7-fc2599d56e59" -> List("65dcaa0c-1df9-486c-9390-75e9674b32e4"),  // AIG Shell 97802
    "fd6709f7-7eea-4aac-b1a7-fe8d1ad2ddeb" -> List("133be3b9-59e4-4aaa-9a5d-d2bfc62c6e77"),  // PacShellE27089-17D
    "a7ab9a2f-739d-4edc-84fe-87d48bb820d2" -> List("13fe7173-4fd2-43cc-967e-ddfae2455530"),  // Equitable SCS Shell EQH_SCS_16
    "f5c7fd97-ea36-440c-9926-a39bcc274617" -> List("a146aa01-7776-46e9-8b95-86fbe6f8cc89"),  // PacShell F27041-21D
    "36729665-d19d-4c88-b1bc-60f8fbbbaea3" -> List("e9815f15-89bb-4839-885d-dbe30cd352b0"),  // AIG Assured Edge Income Achiever Training
    "190339b1-34b1-432d-bdc5-d646de23fc6a" -> List("3c58252f-624f-40a0-a690-0b03287be338"),  // AIG Assured Edge Income Builder Training
    "cd5f0e8e-f6a2-4d51-b0a2-8a6f09606417" -> List("dfd3376f-7be6-4d8a-a8ce-fa998c2c1e56"),  // AIG Index Annuity Training I5281CB.14
    "43b5f020-35e9-45e9-91f7-ab0dbda7fba4" -> List("2e3d62c4-2ac6-48a0-9290-f05ba9de6b1c"),  // AIG Shell I5281PIA.3
    "b162a7eb-7001-4e89-ab18-7396e19c0368" -> List("a86a4180-b396-4ca6-8ca3-24abb385a2e7"),  // AIG Index Annuity Training I5281RJ.11
    "258f9adc-890d-428d-9f58-67edaffffb19" -> List("a1bb741d-cc3f-4511-aa71-17c7f2ad13b8"),  // AIG Index Annuity Training  J5281AD.4
    "7c98b52c-b473-4489-aec2-f95b9012c46c" -> List("865cbd74-24f7-469d-b2be-54dfe70e8dbb"),  // Lincoln Shell NAICFIA5.0
    "5cb6eb4d-a775-4145-941c-87522bcbb951" -> List("ab3db122-36c7-4894-8cf3-b54918d66cc4"),  // Lincoln Shell Covered Choice S_S_FIA12.0
    "b5a4ee09-fd4a-46dc-8fcf-a1a4aa36df4d" -> List("46c3e583-3b2f-4eb8-8859-c8e4550744b4"),  // Lincoln Covered Choice Shell S_FIA13.0
    "e4495c1a-95b1-4d63-862b-82841831e90e" -> List("5359a625-6ca2-455f-bd45-16e42ba4c6e6"),  // Lincoln Fixed and Indexed Annuity Training Feb 2021
    "47bf6d82-cc0e-4bd0-81a2-991d973456d9" -> List("7cd0b4a9-d60e-43e4-911d-d35ca7f1e058"),  // Lincoln Level Advantage Shell S_IVA.4.0
    "fcb96811-d3ec-40bb-8266-9dfaa51cd90e" -> List("e2f9d342-08ee-4d46-87cc-9c3cfe45d8a7"),  // Lincoln Shell Level Advantage S_IVARJ2.20
    "7730e989-b0a0-4077-a4d3-a0d609bd64d8" -> List("1c5d388a-7031-4b4b-ba2b-c3b2a714cb8f"),  // Lincoln Level Advantage Design S_IVARJ5.20
    "cdd81618-ec14-4e1b-a804-97803da8d7c2" -> List("1654f17b-6093-41ae-abc9-b24a17828b07"),  // Lincoln Level Advantage Design Shell S_IVARJ7.20
    "57368397-bd53-4b11-859c-ee03000cb7c3" -> List("38feb689-f43a-49df-b17b-1943103dfb1a"),  // Lincoln American Legacy Target Date Income Training
    "0b2a3b5f-f57c-423f-8be2-e911ad71a73b" -> List("c5c5f92a-bd9c-4691-a2b5-e677147d95ec"),  // PacShell V27041-21B
    "af61281d-f1bc-4998-b990-cafab3054d74" -> List("64995c37-f90a-462f-bc90-5a9f1cf40ce3", "fe20c3ab-1963-4320-93ab-adfca2b1a511"),  // Symetra Freedom Income Training DIA
    "5673d639-d407-4531-8768-d58cbcd9f604" -> List("355ffc66-794f-4991-816f-224b6fc18011"),  // Symetra Select 5 (NY) Shell_100512
    "4b98a3f4-49a9-485a-a45f-ccc3b99e4973" -> List("34516b99-2c97-44fe-a53d-751ae429be38"),  // Nationwide Monument Advisor
    "0f3859eb-5a22-483a-bb44-4ae83dca50c0" -> List("2edf6b23-e8b3-4742-ab85-51eee2f68814"),  // New York Life - Clear Income Fixed Annuity - FP Series Training
  )

  val testConfig: Map[String, List[String]] = Map(
    "track-1" -> List("track-2", "track-3"),
    "track-5" -> List("track-6"),
  )
}
