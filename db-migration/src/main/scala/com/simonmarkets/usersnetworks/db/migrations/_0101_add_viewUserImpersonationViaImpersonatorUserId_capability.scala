package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.UsersCapabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0101_add_viewUserImpersonationViaImpersonatorUserId_capability(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0101_add_impersonation_capabilities")

  // Capability to add
  def capabilities: Set[Capability] = Set("viewUserImpersonationViaImpersonatorUserId")

  // Yes, add the specified capabilities
  def shouldAdd: Boolean = true

  // AdminCapability, ImpersonateUserViaPurviewCapability, ImpersonateUserViaNetworkCapability, ImpersonateUserViaLocationCapability, & ImpersonateUserViaFANumberCapability
  val capabilitiesToCheck: Set[Role] = UsersCapabilities.DetailedImpersonateCapabilities.map(_.name)

  def shouldUpdate(network: Network, customRole: Role): Boolean = {
    network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
      rolesConfig.capabilities.exists(capabilitiesToCheck.contains))
  }
}
