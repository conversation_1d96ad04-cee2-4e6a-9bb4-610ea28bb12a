package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.networks.common.encoders.{BsonFormat, NetworkFormat}
import com.simonmarkets.networks.common.encoders.NetworkFormat.Fields
import org.mongodb.scala.{BulkWriteResult, Document, MongoDatabase}
import com.goldmansachs.marquee.pipg.{IssuerPurview, PurviewedDomain}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.model.Filters.{equal, in}
import org.mongodb.scala.model.UpdateOneModel
import org.mongodb.scala.model.Updates.set
import com.simonmarkets.mongodb.bson.ScalaBsonDocumentsOps.Ops
import simon.Id.NetworkId

import scala.concurrent.Future

class _0081_add_OrderAccountsPurviewDomain(val db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  private object IssuerPurviewFormat extends BsonFormat[IssuerPurview] {
    object Fields {
      val Net = "network"
      val Issuers = "issuers"
      val Wholesaler = "wholesaler"
      val PurviewedDomains = "purviewedDomains"
    }

    override def read(d: Document): IssuerPurview = {
      import Fields._
      IssuerPurview(
        network = simon.Id.NetworkId(d.getString(Net)),
        issuers = d.getStringSetOpt(Issuers).getOrElse(Set.empty),
        wholesaler = d.getStringOpt(Wholesaler).map(simon.Id.NetworkId(_)),
        purviewedDomainsUpdated = d.getStringSetOpt(PurviewedDomains).map(_.map(PurviewedDomain(_)))
      )
    }

    override def write(v: IssuerPurview): Document = {
      import Fields._
      Document(Net -> simon.Id.NetworkId.unwrap(v.network),
        Issuers -> v.issuers.toList,
        Wholesaler -> v.wholesaler.map(i => simon.Id.NetworkId.unwrap(i)),
        PurviewedDomains -> v.purviewedDomainsUpdated.map(_.map(_.productPrefix).toList)
      )
    }
  }

  implicit val tid = TraceId("add_OrderAccountsPurviewDomain")
  val purviewedDomainsField: String = s"${Fields.PurviewNetworks}.${IssuerPurviewFormat.Fields.PurviewedDomains}"

  val networksCollection = db.getCollection[Document]("networks_new")

  def apply(implicit executionContext: scala.concurrent.ExecutionContext): Future[Unit] = {
    for {
      networks <- networksCollection.find(in(purviewedDomainsField, PurviewedDomain.Accounts.productPrefix)).toFuture
      updates = networks.map { dbo =>
        val network = NetworkFormat.read(dbo)
        val newPurviewNetworks = network.purviewNetworks.getOrElse(Set.empty[IssuerPurview]).map { issuerPurview =>
          if (issuerPurview.purviewedDomainsUpdated.getOrElse(Set.empty).contains(PurviewedDomain.Accounts))
          issuerPurview.copy(
            purviewedDomainsUpdated = issuerPurview.purviewedDomainsUpdated.map(_ ++ Set(PurviewedDomain.OrderAccounts)))
          else issuerPurview
        }

        newPurviewNetworks.foreach(issuerPurview =>
          log.info(s"Updating : networkId=${network.id} networkName=${network.name} " +
            s"purviewedNetwork=${issuerPurview.network} newPurviewedDomains=${issuerPurview.purviewedDomainsUpdated.getOrElse(Set.empty)}"))
        val setQuery = set(NetworkFormat.Fields.PurviewNetworks, newPurviewNetworks.map(definition => IssuerPurviewFormat.write(definition)).toList)
        UpdateOneModel(equal("id", NetworkId.unwrap(network.id)), setQuery)
      }
      result <- if (updates.nonEmpty) networksCollection.bulkWrite(updates).toFuture else Future.unit
      _ = result match {
        case res: BulkWriteResult => log.info(s"Total network updates made: ${res.getModifiedCount}")
        case _ => log.info("No network updates were made")
      }
    } yield ()
  }
}
