
package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.repository.MongoUserImpersonationRepository.userImpersonationCodecRegistry
import org.mongodb.scala.bson.Document
import org.mongodb.scala.model.{Filters, Updates}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0099_add_entitlements_field_to_impersonation(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0099__add_entitlements_field_to_impersonation")

  // MongoDB Collection
  val coll: MongoCollection[Document] = mongoDB.getCollection("users.impersonation").withCodecRegistry(userImpersonationCodecRegistry)

  // Fields
  val Entitlements = "entitlements"

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    log.info("Start to add pipg.users.impersonation with empty entitlements")
    for {
      userImpersonations <- coll
        .updateMany(
          Filters.exists(Entitlements, exists = false),
          Updates.set(Entitlements, Set.empty))
        .toFuture()

      _ = log.info(s"Updated ${userImpersonations.getModifiedCount} with entitlement field")
    } yield ()
  }
}