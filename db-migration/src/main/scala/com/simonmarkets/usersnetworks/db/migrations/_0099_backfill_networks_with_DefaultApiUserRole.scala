package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.bson.Document
import org.mongodb.scala.model.{Filters, Updates}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0099_backfill_networks_with_DefaultApiUserRole(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0099_backfill_networks_with_DefaultApiUserRole")

  lazy val networks: MongoCollection[Document] = db.getCollection[Document]("networks_new")

  def apply(implicit executionContext: ExecutionContext): Future[Unit] =
    (for {
      _ <- networks.updateMany(
        Filters.empty(),
        Updates.push("customRolesConfig", Document("role" -> "DefaultApiUserRole")))
    } yield ()).toFuture().map(_ => ())
}
