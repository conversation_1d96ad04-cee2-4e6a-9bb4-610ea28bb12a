package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.domain.ImpersonationStatus
import com.simonmarkets.users.repository.MongoUserImpersonationRepository.userImpersonationCodecRegistry
import org.mongodb.scala.{Document, MongoCollection, MongoDatabase}
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.model.Indexes.{ascending, compoundIndex}

import scala.concurrent.{ExecutionContext, Future}

class _0032_status_index(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_32_status_index")
  log.info("Starting migration")

  //collection
  val impersonationColl: MongoCollection[Document] = mongoDB.getCollection("users.impersonation").withCodecRegistry(userImpersonationCodecRegistry)

  //fields
  val ImpersonatorUserId = "impersonatorUserId"
  val Status = "status"

  //index options
  private val partialStatus = new IndexOptions()
    .unique(true)
    .partialFilterExpression(
      equal(Status, ImpersonationStatus.Pending)
    )

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      _ <- impersonationColl.createIndex(compoundIndex(ascending(Status), ascending(ImpersonatorUserId)), partialStatus).toFuture()
      _ = log.info("Status index created")
    } yield ()
  }
}