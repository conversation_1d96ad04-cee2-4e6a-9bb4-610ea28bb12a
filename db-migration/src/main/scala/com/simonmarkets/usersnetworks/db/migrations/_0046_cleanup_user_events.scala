package com.simonmarkets.usersnetworks.db.migrations


import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.common.UserDomainEvent
import com.simonmarkets.users.repository.encoders.UserFormat
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.UpdateOneModel
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0046_cleanup_user_events(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0046_cleanup_user_events")
  log.info("Starting migration")

  //fields
  private val Id = "id"
  private val EventInfoType = "eventInfo.eventType"

  private val targetEvents = List(
    UserDomainEvent.UserActivatedSendEmail,
    UserDomainEvent.UserPasswordReset
  ).map(_.productPrefix)

  val usersColl: MongoCollection[Document] = db
    .getCollection("users")

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      users <- usersColl.find(in(EventInfoType, targetEvents: _*)).map(UserFormat.read).toFuture
      _ = log.info("Updating users", "userIds" -> users.map(_.id))
      updates = users.map { user =>
        UpdateOneModel(
          filter = equal(Id, user.id),
          update = combine(
            set("eventInfo.eventType", UserDomainEvent.UserStateTransitioned.productPrefix),
            set("eventInfo.correlationId", traceId.toString),
            set("eventInfo.triggeredBy", "_0046_cleanup_user_events_migration")
          )
        )
      }
      _ <- if (updates.nonEmpty)
        usersColl
          .bulkWrite(updates)
          .toFuture
          .map(r => log.info("Bulk update result", r))
      else Future.unit
    } yield ()
  }

}
