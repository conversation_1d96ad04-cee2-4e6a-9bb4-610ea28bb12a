package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.NetworkCategory.Issuer
import com.goldmansachs.marquee.pipg.UserRole.{EqPIPGFA, EqPIPGFAManager, Wholesaler}
import com.simonmarkets.capabilities.LearnV2ContentCustomizationsCapabilities._
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.migrations._0077_add_ViewContentCustomizationViewNetworkCapability_to_FA_Managers.userRoles
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0077_add_ViewContentCustomizationViewNetworkCapability_to_FA_Managers(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0077_add_ViewContentCustomizationViewNetworkCapability_to_FA_Managers")

  def capabilities: Set[Capability] = Set(ViewContentCustomizationViaNetworkCapability.name)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig =>
    customRole == rolesConfig.role && userRoles.contains(rolesConfig.role))
}

object _0077_add_ViewContentCustomizationViewNetworkCapability_to_FA_Managers {
  val userRoles: Set[String] = Set(
    EqPIPGFAManager.productPrefix,
    EqPIPGFA.productPrefix,
    Wholesaler.productPrefix,
    Issuer.productPrefix
  )
}