package com.simonmarkets.usersnetworks.db.migrations

import akka.actor.ActorSystem
import akka.stream.Materializer
import akka.stream.scaladsl.Source
import com.goldmansachs.marquee.pipg.{ContactInfo, DistributionList}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.data.networkEntity.{IssuerInfo, IssuerLookup, IssuerMapping}
import com.simonmarkets.usersnetworks.db.migrations._0009_networkEntities_populating_SI._
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import java.time.Instant

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}
import collection.JavaConverters._

class _0009_networkEntities_populating_SI(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_9_networkEntities_populating_SI")
  implicit val system: ActorSystem = ActorSystem("networkEntities_populating_SI")
  implicit val mat: Materializer = Materializer(system)

  val networkEntitiesCollection: MongoCollection[NetworkEntities] = db.getCollection[NetworkEntities]("networkEntities").withCodecRegistry(networkEntityCodecs)

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    getConfig match {
      case Some(config) =>
        backFillSINetworkEntityInfo(config).map { creationCount =>
          log.info(s"Created $creationCount network entities.")
          ()
        }

      case None =>
        log.info("Unknown environment, aborting migration")
        Future.unit
    }
  }

  private def getConfig: Option[Map[String, String]] = {
    val env_key = "ENV_KEY"
    val osLookup = System.getenv().asScala.toMap
    val env = osLookup.getOrElse(env_key, "prod")
    log.info(s"Environment is '$env'")
    env.toLowerCase match {
      case "qa" => Some(IssuerMapping.siEntityToQaNetworkIdMapping)
      case "prod" => Some(IssuerMapping.siEntityToProdNetworkIdMapping)
      case "alpha" => Some(IssuerMapping.siEntityToQaNetworkIdMapping)
      case _ => None
    }
  }

  private def backFillSINetworkEntityInfo(migrationConfig: Map[String, String])
    (implicit executionContext: ExecutionContext): Future[Long] = {
    val now = Instant.now()
    val createdNetworkEntities = migrationConfig.flatMap {
      case (entity, networkId) =>
        val allNetworkIssuerKeys = IssuerMapping.issuerKeysByEntity.get(entity)
        val allConvertedEntitiesForNetwork = allNetworkIssuerKeys.map(keys => keys.flatMap(IssuerLookup.lookup.get(_).map(issuerInfoToEntity)))
        allConvertedEntitiesForNetwork.map(entities =>
          NetworkEntities(
            networkId = networkId,
            entities = entities.toSet,
            userCreated = "DBMigration",
            timeCreated = now,
            userLastUpdated = "DBMigration",
            timeLastUpdated = now,
            version = 1
          )
        )
    }

    for {
      updates <- Source(createdNetworkEntities).mapAsyncUnordered(parallelism = 1) { networkEntities =>
        networkEntitiesCollection.insertOne(networkEntities)
          .toFuture
          .transformWith {
            case Success(_) =>
              log.info(s"Successfully created network entities for ${networkEntities.networkId}")
              Future.successful(1L)
            case Failure(ex) =>
              log.error(s"Error occurred while creating network entities for ${networkEntities.networkId}", ex)
              Future.successful(0L)
          }
      }.runFold(0L)(_ + _)
    } yield updates
  }
}

object _0009_networkEntities_populating_SI {
  case class NetworkEntities(
      networkId: String,
      entities: Set[Entity],
      userCreated: String,
      timeCreated: Instant,
      userLastUpdated: String,
      timeLastUpdated: Instant,
      version: Int
  )

  case class Entity(
      key: String,
      symbol: String,
      contractTypeWrapper: Option[String] = None,
      fullName: Option[String] = None,
      shortName: Option[String] = None,
      isRegS: Boolean = false,
      is3a2: Boolean = false,
      isNonNativeIssuerKeyOrLegacy: Boolean = false, //for NBC_GS and all
      contactInfo: Option[ContactInfo] = None
  )

  val networkEntityCodecs: CodecRegistry =
    fromRegistries(
      fromProviders(
        createCodecProviderIgnoreNone[NetworkEntities],
        classOf[Entity],
        classOf[DistributionList],
        classOf[ContactInfo]
      ),
      DEFAULT_CODEC_REGISTRY
    )

  def issuerInfoToEntity(issuerInfo: IssuerInfo): Entity =
    Entity(
      key = issuerInfo.issuerKey,
      symbol = issuerInfo.issuerSymbol,
      contractTypeWrapper = Some(issuerInfo.contractTypeWrapper),
      fullName = Some(issuerInfo.issuer),
      shortName = Some(issuerInfo.issuerShortName),
      isRegS = issuerInfo.regS,
      is3a2 = issuerInfo.is3a2,
      isNonNativeIssuerKeyOrLegacy = issuerInfo.legacy || issuerInfo.isNonNativeIssuerKey,
      contactInfo = issuerInfo.contactInfo
    )
}


