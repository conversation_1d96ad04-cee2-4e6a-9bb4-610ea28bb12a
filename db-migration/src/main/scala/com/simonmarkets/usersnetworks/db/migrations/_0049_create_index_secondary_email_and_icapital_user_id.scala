package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.model.Indexes.ascending
import org.mongodb.scala.{Document, MongoCollection, MongoDatabase}
import com.simonmarkets.users.repository.encoders.UserFormat.Fields
import org.mongodb.scala.model.IndexOptions

import scala.concurrent.{ExecutionContext, Future}

class _0049_create_index_secondary_email_and_icapital_user_id(db: MongoDatabase) extends ChangeSet(db) with TraceLogging{
  implicit val traceId = TraceId("create-index-secondary-email-and-icapital-user-id")
  val usersCollection: MongoCollection[Document] = db.getCollection("users")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    log.info("Starting migration")
    for {
      _ <- usersCollection.createIndex(ascending(Fields.SecondaryEmail)).toFuture()
      _ <- usersCollection.createIndex(ascending(Fields.ICapitalUserId), new IndexOptions().unique(true).sparse(true)).toFuture()
    } yield ()
  }
}
