package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.bson.MacrosFormat
import com.simonmarkets.util.{EnumEntry, ProductEnums}
import org.bson.codecs.Codec
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.UpdateOneModel
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoDatabase, _}

import scala.concurrent.{ExecutionContext, Future}

class _0072_reformat_paidRole_field_in_paidFeatures(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0072_reformat_paidRole_field_in_paidFeatures")
  log.info(s"Starting migration _0072_reformat_paidRole_field_in_paidFeatures")


  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val paidFeaturesCollection: MongoCollection[Document] = mongoDB.getCollection("users.paidFeatures")
    for {
      // Step 1: Query for all paidFeatures
      paidFeatures <- paidFeaturesCollection.find().toFuture()
      // Step 2: Generate List of UpdateOneModels
      updateModels = paidFeatures.map { paidFeatureDocument =>
        val paidFeatures = PaidFeaturesFormat.read(paidFeatureDocument)
        val roleDocs = paidFeatures.roles.map(role => {
          Document("product" -> role.product.toString, "roleSource" -> role.roleSource.toString)
        })
        val filter = equal("id", paidFeatures.id)
        val update = combine(set("roles", roleDocs))
        new UpdateOneModel[Document](filter, update)
      }
      // Step 3: BulkWrite the updateModels
      _ <- paidFeaturesCollection.bulkWrite(updateModels).toFuture()
    } yield ()
  }
}

object PaidFeaturesFormat extends MacrosFormat[PaidFeatures] {
  override protected def entityCodecProvider: Codec[PaidFeatures] = Macros.createCodec[PaidFeatures](codecRegistry)

  lazy val codecRegistry: CodecRegistry = fromRegistries(
    MongoClient.DEFAULT_CODEC_REGISTRY,
    fromProviders(
      Macros.createCodecProviderIgnoreNone[PaidFeatures](),
      Macros.createCodecProviderIgnoreNone[PaidRole](),
      Macros.createCodecProviderIgnoreNone[ICapitalProduct](),
      Macros.createCodecProviderIgnoreNone[RoleSource](),
    ))
}

//Formatter
sealed trait RoleSource extends EnumEntry

object RoleSource extends ProductEnums[RoleSource] {
  case object Manual extends RoleSource

  case object Stripe extends RoleSource

  override def Values: Seq[RoleSource] = Seq(Manual, Stripe)
}

sealed trait ICapitalProduct extends EnumEntry

object ICapitalProduct extends ProductEnums[ICapitalProduct] {
  case object Architect extends ICapitalProduct

  override def Values: Seq[ICapitalProduct] = Seq(Architect)
}

case class PaidRole(
    product: ICapitalProduct,
    roleSource: RoleSource
)

case class PaidFeatures(
    id: String,
    roles: List[PaidRole]
)