package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.migrations._0019_add_rejection_reasons_kovack._
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.annotations.BsonProperty
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import org.mongodb.scala.bson.codecs.Macros._
import java.time.Instant

import scala.concurrent.{ExecutionContext, Future}
import collection.JavaConverters._
import java.util.UUID

import scala.util.{Failure, Success}

class _0019_add_rejection_reasons_kovack(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0019_add_rejection_reasons_kovack")

  val rejectionReasonsColl: MongoCollection[RejectionReasons] = db.getCollection[RejectionReasons]("rejectionReasons").withCodecRegistry(rejectionReasonsCodec)

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    getNetwork match {
      case ("prod") =>
        log.info("adding rejection reasons in prod")
        addRejectionReasons("66a9e263-d4c4-4485-b798-ef75bc3b5a14")
      case ("qa") =>
        log.info("adding rejection reasons in qa")
        addRejectionReasons("a241613c-8fcb-4e01-bdf4-c8d6bc21abf3")
      case ("alpha") =>
        log.info("adding rejection reasons in alpha")
        addRejectionReasons("a241613c-8fcb-4e01-bdf4-c8d6bc21abf3")
    }
  }

  private def getNetwork: String = {
    val env_key = "ENV_KEY"
    val osLookup = System.getenv().asScala.toMap
    osLookup.getOrElse(env_key, "prod")
  }

  private def addRejectionReasons(network: String)
    (implicit executionContext: ExecutionContext): Future[Unit] = {
    val now: Instant = Instant.now()

    val reasons = Seq(
      Reasons(
        "Client’s Investment Experience Not Aligned",
        "Suitability Issue",
        now
      ),
      Reasons(
        "Client’s Annual Income Not Aligned",
        "Suitability Issue",
        now
      ),
      Reasons(
        "Client’s Net Worth/Liquid Net Worth Not Aligned",
        "Suitability Issue",
        now
      ),
      Reasons(
        "Client’s Investment Objectives/Risk Tolerance Not Aligned",
        "Suitability Issue",
        now
      ),
      Reasons(
        "Client’s Liquidity Needs Not Aligned",
        "Suitability Issue",
        now
      ),
      Reasons(
        "Client’s Age Not Aligned",
        "Suitability Issue",
        now
      ),
      Reasons(
        "Client Acknowledgement/Disclosure Documents Not on File",
        "NIGO",
        now
      ),
      Reasons(
        "Invalid Account Number",
        "NIGO",
        now
      ),
      Reasons(
        "Account Restricted",
        "NIGO",
        now
      ),
      Reasons(
        "Possible Duplicate",
        "NIGO",
        now
      ),
      Reasons(
        "Advisor Certification Not Complete",
        "NIGO",
        now
      ),
      Reasons(
        "Trade Desk Rejected",
        "NIGO",
        now
      )
    )

    val rejectionReasons = RejectionReasons(
      network = network,
      reasons = reasons
    )

    log.info("Inserting data into mongo")
    rejectionReasonsColl.insertOne(rejectionReasons)
      .toFuture
      .transformWith {
        case Success(_) =>
          log.info(s"Successfully added rejection reasons for $network")
          Future.unit
        case Failure(exception) =>
          log.error(s"Error occurred while adding rejection reasons for $network", exception)
          Future.unit
      }
  }
}

object _0019_add_rejection_reasons_kovack {
  case class RejectionReasons(
      network: String,
      reasons: Seq[Reasons]
  )

  case class Reasons(
      @BsonProperty("_id")
      id: String,
      reason: String,
      group: String,
      userCreated: String,
      timeCreated: Instant,
      userLastUpdated: String,
      timeLastUpdated: Instant,
      traceId: String,
      version: Int,
  )

  object Reasons {
    def apply(reason: String, group: String, now: Instant): Reasons =
      Reasons(
        id = UUID.randomUUID().toString,
        reason = reason,
        group = group,
        userCreated = "DBMigration",
        timeCreated = now,
        userLastUpdated = "DBMigration",
        timeLastUpdated = now,
        traceId = "migration-0019",
        version = 1,
      )
  }

  val rejectionReasonsCodec: CodecRegistry =
    fromRegistries(
      fromProviders(
        classOf[RejectionReasons],
        classOf[Reasons]
      ),
      DEFAULT_CODEC_REGISTRY
    )
}
