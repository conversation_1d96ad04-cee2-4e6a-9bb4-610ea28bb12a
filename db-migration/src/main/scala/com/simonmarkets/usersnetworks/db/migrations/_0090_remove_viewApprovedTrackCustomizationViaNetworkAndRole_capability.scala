package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0090_remove_viewApprovedTrackCustomizationViaNetworkAndRole_capability (val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0090_remove_viewApprovedTrackCustomizationViaNetworkAndRole_capabilty")

  def capabilities: Set[Capability] = Set("viewApprovedTrackCustomizationViaNetworkAndRole")

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network, customRole: Role): Boolean = true
}
