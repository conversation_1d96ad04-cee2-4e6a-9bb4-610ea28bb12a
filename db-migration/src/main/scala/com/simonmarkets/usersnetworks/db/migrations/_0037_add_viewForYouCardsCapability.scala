package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.ForYouCardsCapabilities.{ViewForYouCardsViaNetworkCapability, ViewForYouCardsViaPurviewCapability, ViewForYouCardsViaUserCapability}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0037_add_viewForYouCardsCapability(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0037_add_viewForYouCardsCapability")

  def capabilities: Set[Capability] = Set(
    ViewForYouCardsViaUserCapability.name,
    ViewForYouCardsViaNetworkCapability.name,
    ViewForYouCardsViaPurviewCapability.name
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = true
}