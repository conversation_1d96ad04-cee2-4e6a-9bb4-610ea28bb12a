package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Indexes.ascending
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0015_networkEntities_unique_index(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0015_networkEntities_unique_index")
  log.info("Starting migration")

  val coll: MongoCollection[Document] = db.getCollection("networkEntities")

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val unique = new IndexOptions().unique(true)
    for {
      _ <- coll.createIndex(key = ascending("entities.key"), options = unique).toFuture()
    } yield ()
  }
}
