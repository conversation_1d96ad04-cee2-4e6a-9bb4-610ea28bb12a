package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.Capabilities.AdminCapability
import com.simonmarkets.capabilities.OrdersCapabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0089_add_cancelAfterBooksClose_capability(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0089_add_cancelAfterBooksClose_capability")

  def capabilities: Set[Capability] = Set("cancelOrderAfterBooksClose")

  val capabilitiesToCheck = (OrdersCapabilities.DetailedCancelCapabilities -- Set(AdminCapability)).map(_.name)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    rolesConfig.capabilities.exists(capabilitiesToCheck.contains))
}
