package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.SimonUICapabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0065_backfill_rfq_tab_capabilities_for_home_office_fa_wholesaler_capabilities(
    val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0065_backfill_rfq_tab_capabilities_for_home_office_fa_wholesaler_capabilities")

  def capabilities: Set[Capability] = Set(
    SimonUICapabilities.ViewUIRfqActiveRfqsTabCapability.name,
    SimonUICapabilities.ViewUIRfqActiveRftsTabCapability.name
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network,
      customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    (rolesConfig.capabilities.contains(SimonUICapabilities.ViewUIRfqHomeOfficeCapability.name) ||
      rolesConfig.capabilities.contains(SimonUICapabilities.ViewUIRfqFACapability.name) ||
      rolesConfig.capabilities.contains(SimonUICapabilities.ViewUIRfqWholesalerCapability.name)))
}
