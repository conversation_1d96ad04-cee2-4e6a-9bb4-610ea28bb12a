package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.migrations._0075_add_mixpanel_ids.MaskedId
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoClient, MongoDatabase}

import java.util.UUID

import scala.concurrent.{ExecutionContext, Future}

class _0075_add_mixpanel_ids(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0075_add_mixpanel_ids")

  //collections
  val users = mongoDB
    .getCollection("users")
    .withCodecRegistry(
      fromRegistries(
        MongoClient.DEFAULT_CODEC_REGISTRY,
        fromProviders(
          Macros.createCodecProvider[MaskedId]
        )
      )
    )

  //fields
  val MixPanel = "MixPanel"

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      _ <- users
        .updateMany(
          nin("maskedIds.target", MixPanel),
          push("maskedIds", MaskedId(MixPanel, UUID.randomUUID().toString))
        )
        .toFuture
    } yield ()
  }

}

object _0075_add_mixpanel_ids {

  case class MaskedId(
      target: String,
      id: String
  )

}
