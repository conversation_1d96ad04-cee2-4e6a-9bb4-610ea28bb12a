package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.PaidFeaturesCapabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0065_add_ViewUserPaidFeatureViaOwnerCapability(
    val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0065_add_ViewUserPaidFeatureViaOwnerCapability")

  def capabilities: Set[Capability] = Set(PaidFeaturesCapabilities.ViewUserPaidFeatureViaOwner)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = true
}