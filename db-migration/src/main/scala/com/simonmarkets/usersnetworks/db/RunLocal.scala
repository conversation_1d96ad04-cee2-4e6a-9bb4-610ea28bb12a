package com.simonmarkets.usersnetworks.db

import com.simonmarkets.db.migration.MigrationDriver
import com.simonmarkets.db.migration.config.MigrationConfig
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.mongodb.Client
import com.simonmarkets.usersnetworks.db.migrations._0084_default_groups
import com.simonmarkets.utils.config.Resolvers.SecretsConfigOps
import com.typesafe.config.ConfigFactory
import org.mongodb.scala.MongoDatabase

import scala.concurrent.ExecutionContext

/**
 * Can be used to test migrations against local
 */
object RunLocal extends App with TraceLogging {
  implicit val traceId: TraceId = TraceId("migration-controller")
  implicit val ec: ExecutionContext = ExecutionContext.global

  log.info("Loading config and resolving secrets")
  private val config = ConfigFactory.load().resolveSecrets()
  log.info("Loading config after resolving secrets")
  private val migrationConfig: MigrationConfig = MigrationConfig(config)
  log.info("Connecting to mongo")
  private val mongoDB: MongoDatabase = Client
    .create(migrationConfig.mongoDB.connection)
    .getDatabase(migrationConfig.mongoDB.databaseName)
  private val mongoDriver = new MigrationDriver(mongoDB, migrationConfig.mongoDB.changeSetCollectionName, None)

  log.info("Starting migration")
  log.info(mongoDriver.runMigration(List(
    classOf[_0084_default_groups],
  )).status.toString)
  log.info(s"Finished")
}