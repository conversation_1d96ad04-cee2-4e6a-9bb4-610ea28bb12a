package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0068_add_developerhub_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0068_add_developerhub_capabilities")

  def capabilities: Set[Capability] = Set(
    "viewUIDeveloperHub",
    "viewDeveloperHubViaUserId",
    "viewDeveloperHubServicesByEnv",
    "viewDeveloperHubExternalUserPreferencesViaUserId",
    "editDeveloperHubExternalUserPreferencesViaUserId",
    "developer"
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = customRole == "DeveloperHubUser"

}
