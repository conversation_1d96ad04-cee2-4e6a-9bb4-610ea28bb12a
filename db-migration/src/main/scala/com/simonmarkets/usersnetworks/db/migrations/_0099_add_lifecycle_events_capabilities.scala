package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.LifecycleEventsCapabilities._
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0099_add_lifecycle_events_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {

  implicit val traceId: TraceId = TraceId("_0099_add_lifecycle_events_capabilities")

  def capabilities: Set[Capability] = Set(ViewLifecycleEventViaPayOffCapability.name, EditLifecycleEventViaPayOffCapability.name)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = true
}
