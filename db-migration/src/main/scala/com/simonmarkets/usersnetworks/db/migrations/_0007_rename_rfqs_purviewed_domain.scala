package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.migrations._0007_rename_rfqs_purviewed_domain._
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.model.Updates.{combine, set, unset}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class _0007_rename_rfqs_purviewed_domain(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_7_rename_rfqs_purviewed_domain")

  log.info("Starting migration")

  val networkCollection: MongoCollection[Network] = db.getCollection[Network]("networks_new").withCodecRegistry(networkCodecRegistry)
  val purviewedDomainsField: String = "purviewNetworks.purviewedDomains"
  val oldRfqsPurviewedDomain: String = "Rfqs"
  val newRfqsPurviewedDomain: String = "EditRfqs"

  private def getUpdatedPurviewNetworks(purviewNetworks: Option[Set[IssuerPurview]]): Option[Set[IssuerPurview]] = {
    purviewNetworks.map(issuerPurviewSet => issuerPurviewSet.map(issuerPurview => getUpdatedIssuerPurview(issuerPurview)))
  }

  private def getUpdatedIssuerPurview(issuerPurview: IssuerPurview): IssuerPurview = {
    val newPurviewedDomains = issuerPurview.purviewedDomains.map(purviewedDomains =>
      purviewedDomains.map(x => if (x == oldRfqsPurviewedDomain) newRfqsPurviewedDomain else x)
    )
    issuerPurview.copy(purviewedDomains = newPurviewedDomains)
  }

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      networks <- networkCollection.find(equal(purviewedDomainsField, oldRfqsPurviewedDomain)).toFuture
      _ = log.info(s"Updating purviewed domain Rfqs to EditRfqs")

      updates <- Future.traverse(networks) { network => {
        val purviewNetworks = getUpdatedPurviewNetworks(network.purviewNetworks)
        log.info(s"Updating network ${network.id}")

        networkCollection.updateOne(
          equal("id", network.id),
          combine(
            purviewNetworks match {
              case Some(value) => set("purviewNetworks", value)
              case None => unset("purviewNetworks")
            }
          )
        ).toFuture.transformWith {
          case Success(result) => Future.successful(result.getModifiedCount)
          case Failure(ex) =>
            log.error(s"Error occurred while updating network ${network.id}", ex)
            Future.successful(0L)
        }
      }}
    } yield updates.sum
  }
}

object _0007_rename_rfqs_purviewed_domain {
  case class Network(
      id: String,
      purviewNetworks: Option[Set[IssuerPurview]] = None
  )

  case class IssuerPurview(
      network: String,
      issuers: Set[String],
      wholesaler: Option[String] = None,
      purviewedDomains: Option[Set[String]] = None
  )

  val networkCodecRegistry: CodecRegistry = fromRegistries(
    fromProviders(
      createCodecProviderIgnoreNone[Network],
      classOf[IssuerPurview]
    ),
    DEFAULT_CODEC_REGISTRY
  )
}
