package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Indexes.{ascending, compoundIndex}

import scala.concurrent.{ExecutionContext, Future}

class _0074_add_task_indexes(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0074_add_task_indexes")

  //collections
  val tasks = mongoDB.getCollection("users.tasks")
  val inputs = mongoDB.getCollection("users.tasks.inputs")
  val outputs = mongoDB.getCollection("users.tasks.outputs")

  //fields
  val Id = "id"
  val TaskId = "taskId"
  val Entitlements = "entitlements"
  val Row = "row"

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      _ <- tasks.createIndex(key = compoundIndex(ascending(Id), ascending(Entitlements))).toFuture
      _ <- inputs.createIndex(key = compoundIndex(ascending(TaskId), ascending(Row))).toFuture
      _ <- outputs.createIndex(key = compoundIndex(ascending(TaskId), ascending(Row))).toFuture
    } yield ()
  }

}
