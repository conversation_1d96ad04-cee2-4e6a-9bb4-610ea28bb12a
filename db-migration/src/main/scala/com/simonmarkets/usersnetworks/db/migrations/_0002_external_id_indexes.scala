package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Filters.{and, exists}
import org.mongodb.scala.model.Indexes.{ascending, compoundIndex}

import scala.concurrent.{ExecutionContext, Future}

class _0002_external_id_indexes(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_2_external_id_indexes")
  log.info("Starting migration")

  //collections
  val externalIdTypeColl = mongoDB.getCollection("users.externalIdTypes")
  val usersColl = mongoDB.getCollection("users")

  //fields
  val ExternalId_id = "externalIds.id"
  val ExternalId_subject = "externalIds.subject"
  val Name = "name"
  val DistributorId = "distributorId"
  val OmsId = "omsId"
  val NetworkId = "networkId"

  //index options
  private val unique = new IndexOptions().unique(true)
  private val partialExternal = new IndexOptions()
    .unique(true)
    .partialFilterExpression(
      and(
        exists(ExternalId_subject),
        exists(ExternalId_id)
      )
    )
  private val partialDistributor = new IndexOptions()
    .unique(true)
    .partialFilterExpression(
      exists(DistributorId)
    )
  private val partialOms = new IndexOptions()
    .unique(true)
    .partialFilterExpression(
      exists(OmsId)
    )


  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      _ <- externalIdTypeColl.createIndex(key = ascending(Name), options = unique).toFuture()
      _ = log.info("External id type index created")
      _ <- usersColl.createIndex(compoundIndex(ascending(ExternalId_subject), ascending(ExternalId_id)), partialExternal).toFuture()
      _ = log.info("External ids index created")
      _ <- usersColl.createIndex(compoundIndex(ascending(DistributorId), ascending(NetworkId)), partialDistributor).toFuture()
      _ = log.info("DistributorId index created")
      _ <- usersColl.createIndex(compoundIndex(ascending(OmsId), ascending(NetworkId)), partialOms).toFuture()
      _ = log.info("OmsId index created")
    } yield ()
  }

}