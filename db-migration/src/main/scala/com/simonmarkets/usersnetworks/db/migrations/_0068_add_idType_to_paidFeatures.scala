package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoDatabase, _}

import scala.concurrent.{ExecutionContext, Future}


class _0068_add_idType_to_paidFeatures(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0068_add_idType_to_paidFeatures")
  log.info(s"Starting migration _0068_add_idType_to_paidFeatures")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val collection = mongoDB.getCollection("users.paidFeatures")
    // Update all documents in the collection to add the idType field
    for {
      updateResult <- collection.updateMany(
        exists("idType", false),
        set("idType", "UserId")
      ).toFuture()
      _ = log.info(s"Updated ${updateResult.getModifiedCount} documents in users.paidFeatures collection")
    } yield ()
  }
}
