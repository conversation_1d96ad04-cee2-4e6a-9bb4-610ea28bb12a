package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.{ChangeSet, CustomConfig}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.repository.encoders.UserFormat
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Filters.{equal, exists}
import org.mongodb.scala.model.Updates.push
import org.mongodb.scala.model.{Filters, UpdateOneModel}
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import pureconfig.generic.auto._

import scala.concurrent.{ExecutionContext, Future}

class _0014_migrate_fidelity_ids(db: MongoDatabase) extends ChangeSet(db) with TraceLogging with CustomConfig {

  case class FidelityNetworks(fidelityNetworkIds: Seq[String])

  private val fidelityNetworks = loadConfig[FidelityNetworks]

  implicit val traceId: TraceId = TraceId("_0014_migrate_fidelity_ids")
  log.info("Starting migration")

  //field names
  private val Id = "id"
  private val NetworkId = "networkId"
  private val ExternalIds = "externalIds"
  private val Subject = "subject"
  private val DistributorId = "distributorId"

  //constants
  private val Wealthscape = "wealthscape"

  val usersColl: MongoCollection[Document] = db.getCollection("users")

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      existingUsers <- usersColl.find(
        Filters.and(
          Filters.in(NetworkId, fidelityNetworks.fidelityNetworkIds: _*),
          exists(DistributorId, exists = true)
        )
      ).map(UserFormat.read).toFuture
      _ = log.info(s"Updating externalIds for user ids ${existingUsers.map(_.id).mkString("\n")}")
      updates = existingUsers.map { u =>
        val distIdOpt = u.distributorId
        distIdOpt.map { distId =>
          UpdateOneModel(
            equal(Id, u.id),
            push(ExternalIds, Document(Subject -> Wealthscape, Id -> distId))
          )
        }
      }
      _ <- usersColl.bulkWrite(updates.flatten).toFuture
    } yield ()
  }
}
