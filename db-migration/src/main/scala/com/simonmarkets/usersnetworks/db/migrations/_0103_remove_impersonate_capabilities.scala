package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0103_remove_impersonate_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0103_remove_impersonate_capabilities")

  def capabilities: Set[Capability] = Set(
    "impersonateUserViaFANumber",
    "impersonateUserViaLocation",
    "impersonateUserViaNetwork",
    "impersonateUserViaPurview"
  )

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network, customRole: Role): Boolean = {
    // remove the capabilities from every network except iCapital Admin & Raymond James
    network.name != "iCapital Admin" && network.name != "<PERSON>"
  }
}
