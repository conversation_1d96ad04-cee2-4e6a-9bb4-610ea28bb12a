package com.simonmarkets.usersnetworks.db.migrations

import akka.actor.ActorSystem
import com.goldmansachs.marquee.pipg.{<PERSON><PERSON><PERSON><PERSON>, CustodianFaNumber}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.{MongoClient, MongoDatabase}
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.model.Filters
import org.mongodb.scala.model.Updates.set

import scala.concurrent.{ExecutionContext, Future}

/** To avoid annoyances arising from interactions with a typing of "custodianFaNumbers: Option[Set[CustodianFaNumber]]"
 * vs. simply Set[CustodianFaNumber], we are setting a default for the field "custodianFaNumbers" throughout the database.
 * Specifically, any user entry without this new field will simply have it set to "Set.empty"
 * Otherwise, leave the field alone, if already populated.
 */
class _0108_default_custodianFaNumbers(db: MongoDatabase)
    extends ChangeSet(db)
    with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0108_default_custodianFaNumbers")
  implicit val ac: ActorSystem = ActorSystem("migration")

  val CUSTODIAN_FA_NUMBERS_FIELD = "custodianFaNumbers"

  // collections
  val users = mongoDB
    .getCollection("users")
    .withCodecRegistry(
      fromRegistries(
        MongoClient.DEFAULT_CODEC_REGISTRY,
        fromProviders(
          Macros.createCodecProvider[Custodian],
          Macros.createCodecProvider[CustodianFaNumber]
        )
      )
    )

  override def apply(implicit
      executionContext: ExecutionContext
  ): Future[Unit] = {
    for {
      _ <- users
        .updateMany(
          Filters.exists(CUSTODIAN_FA_NUMBERS_FIELD, exists = false),
          set(CUSTODIAN_FA_NUMBERS_FIELD, Set.empty[CustodianFaNumber])
        )
        .toFuture()
    } yield ()
  }
}
