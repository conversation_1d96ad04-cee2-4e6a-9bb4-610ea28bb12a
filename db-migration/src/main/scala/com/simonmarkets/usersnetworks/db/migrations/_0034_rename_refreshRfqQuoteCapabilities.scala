package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.migrations._0034_rename_refreshRfqQuoteCapabilities._
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import org.mongodb.scala.model.Filters.{equal, in}
import org.mongodb.scala.model.Updates.{combine, set}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class _0034_rename_refreshRfqQuoteCapabilities(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0034_rename_refreshRfqQuoteCapabilities")

  log.info("Starting migration")

  val networkCollection: MongoCollection[Network] = db.getCollection[Network]("networks_new").withCodecRegistry(networkCodecRegistry)
  val oldRefreshRfqIdeaQuoteViaOwner: String = "refreshRfqIdeaQuoteViaOwner"
  val oldRefreshRfqIdeaQuoteViaNetwork: String = "refreshRfqIdeaQuoteViaNetwork"
  val oldRefreshRfqQuoteViaNetworkIssuerPurview: String = "refreshRfqIdeaQuoteViaNetworkIssuerPurview"
  val oldCapabilities = Seq(oldRefreshRfqIdeaQuoteViaOwner, oldRefreshRfqIdeaQuoteViaNetwork, oldRefreshRfqQuoteViaNetworkIssuerPurview)
  val capabilityField: String = "customRolesConfig.capabilities"

  private def getUpdatedCustomRolesConfig(customRolesConfig: Set[CustomRoleDefinition]): Set[CustomRoleDefinition] = {
    customRolesConfig.map(roleCapabilities => {
      val updatedCapabilities = roleCapabilities.capabilities.map(capability =>
        if (capability == oldRefreshRfqIdeaQuoteViaOwner) "refreshRfqQuoteViaOwner"
        else if (capability == oldRefreshRfqIdeaQuoteViaNetwork) "refreshRfqQuoteViaNetwork"
        else if (capability == oldRefreshRfqQuoteViaNetworkIssuerPurview) "refreshRfqQuoteViaNetworkIssuerPurview"
        else capability
      )
      roleCapabilities.copy(capabilities = updatedCapabilities)
    })
  }

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      networks <- networkCollection.find(in(capabilityField, oldCapabilities: _*)).toFuture

      _ = log.info(s"Updating refreshRfqIdeaQuoteCapabilities to refreshRfqQuoteCapabilities")

      updates <- Future.traverse(networks) { network => {
        val updatedCustomRolesConfig = getUpdatedCustomRolesConfig(network.customRolesConfig)
        log.info(s"Updating network ${network.id}")

        networkCollection.updateOne(
          equal("id", network.id),
          combine(
            set("customRolesConfig", updatedCustomRolesConfig)
          )
        ).toFuture.transformWith {
          case Success(result) => Future.successful(result.getModifiedCount)
          case Failure(ex) =>
            log.error(s"Error occurred while updating network ${network.id}", ex)
            Future.successful(0L)
        }
      }}
    } yield updates.sum
  }
}

object _0034_rename_refreshRfqQuoteCapabilities {
  case class Network(
      id: String,
      customRolesConfig: Set[CustomRoleDefinition] = Set.empty,
  )

  case class CustomRoleDefinition(
      role: String,
      capabilities: Set[String],
  )

  val networkCodecRegistry: CodecRegistry = fromRegistries(
    fromProviders(
      createCodecProviderIgnoreNone[Network],
      classOf[CustomRoleDefinition]
    ),
    DEFAULT_CODEC_REGISTRY
  )
}
