package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.okta.domain.Factor
import com.simonmarkets.users.repository.encoders.UserFormat.Fields
import org.mongodb.scala.bson.Document
import org.mongodb.scala.model.{Filters, Updates}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0098_backfill_users_with_mfa(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0098_update_mfa_capability_users")
  val coll: MongoCollection[Document] = db.getCollection[Document]("users")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    log.info("Updating mfa capability users")
    for {
      //backfill mfas
      backfilledUsers <- coll
        .updateMany(Filters.exists(Fields.Mfas, exists = false), Updates.set(Fields.Mfas, Map.empty[String, Factor]))
        .toFuture()

      _ = log.info(s"Updated ${backfilledUsers.getModifiedCount} with mfas field")
    } yield ()
  }
}
