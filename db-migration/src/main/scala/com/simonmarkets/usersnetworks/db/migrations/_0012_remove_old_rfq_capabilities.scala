package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0012_remove_old_rfq_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_12_remove_old_rfq_capabilities")

  def capabilities: Set[Capability] = Set("submitRfqToIssuerOnBehalfOfViaNetworkIssuerPurview", "submitRfqToIssuerViaOwner", "submitRfqToIssuerOnBehalfOfViaNetwork")

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network, customRole: Role): Boolean = true

}
