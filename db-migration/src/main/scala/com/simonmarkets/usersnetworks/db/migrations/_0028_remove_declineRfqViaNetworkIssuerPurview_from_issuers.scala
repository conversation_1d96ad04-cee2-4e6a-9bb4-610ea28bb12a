package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.{RfqCapabilities, SimonUICapabilities}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0028_remove_declineRfqViaNetworkIssuerPurview_from_issuers(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0028_remove_declineRfqViaNetworkIssuerPurview_from_issuers")

  def capabilities: Set[Capability] = Set(RfqCapabilities.DeclineRfqViaNetworkIssuerPurview)

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    rolesConfig.capabilities.contains(SimonUICapabilities.ViewUIRfqIssuerCapability.name))

}
