package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.RfqCapabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0011_add_AwardRfqOnBehalfOfViaNetworkIssuerPurview_capability(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_11_add_AwardOnBehalfOfViaNetworkIssuerPurview_capability")

  def capabilities: Set[Capability] = Set(RfqCapabilities.AwardRfqOnBehalfOfViaNetworkIssuerPurview)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    rolesConfig.capabilities.contains("submitRfqToIssuerOnBehalfOfViaNetworkIssuerPurview"))

}
