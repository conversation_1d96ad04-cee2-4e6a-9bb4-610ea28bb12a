package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.{ClientProfileCapabilities, SimonUICapabilities}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0106_add_view_and_edit_clientProfile_capability(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0106_add_view_and_edit_clientProfile_capability")

  def capabilities: Set[Capability] = Set(
    ClientProfileCapabilities.ViewClientProfileViaOwnerCapability.name,
    ClientProfileCapabilities.EditClientProfileViaOwnerCapability.name
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    rolesConfig.capabilities.contains(SimonUICapabilities.ViewUIArchitectCapability.name))
}
