package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.migrations._0036_remove_productNomenclature_short_JPMPB.ProductNomenclature
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters.{and, in, lt}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import java.time.{Instant, LocalDate}

import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}

class _0036_remove_productNomenclature_short_JPMPB(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0036_remove_productNomenclature_short_JPMPB")
  val qaTestNetworks = Seq("********-8ebe-46d2-bf05-d13d44b296d7")
  val productNomenclatureColl: MongoCollection[ProductNomenclature] = db.getCollection[ProductNomenclature]("productNomenclature")
    .withCodecRegistry(fromRegistries(
      fromProviders(
        classOf[ProductNomenclature]
      ),
      DEFAULT_CODEC_REGISTRY
    ))
  val jpmpbProdNetworks: Seq[String] = Seq("JP Morgan Private Bank", "df18ee2d-b0e9-4455-b085-1868f52bada2", "0682e53f-1044-4463-bd8f-ab2608868e45", "67b16121-1d5c-46e8-ab9b-d74be091e472")
  val oct282022: LocalDate = LocalDate.of(2022, 10, 28)

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    deleteOldLongProductNomenclature(getConfig)
  }

  private def getConfig: Seq[String] = {
    val env_key = "ENV_KEY"
    val osLookup = System.getenv().asScala.toMap
    val env = osLookup.getOrElse(env_key, "prod")
    log.info(s"Environment is '$env'")
    env.toLowerCase match {
      case "qa" => qaTestNetworks
      case "prod" => jpmpbProdNetworks
      case _ => Seq("SIMON Admin")
    }
  }

  def deleteOldLongProductNomenclature(networkList: Seq[String])(implicit executionContext: ExecutionContext): Future[Unit] = {
    val query = and(
      in("networkId", networkList :_*),
      lt("timeCreated", oct282022)
    )

    log.info(s"Deleting productNomenclature entries before 10/28/2022 for $networkList")
    for {
      res <- productNomenclatureColl.deleteMany(query).toFuture()
      _ = log.info(s"Deleted ${res.getDeletedCount} productNomenclature entries")
    } yield ()
  }
}

object _0036_remove_productNomenclature_short_JPMPB {

  case class ProductNomenclature(
      id: String,
      networkId: String,
      userCreated: String,
      timeCreated: Instant
  )
}
