package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.model.Updates
import org.mongodb.scala.{Document, MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0057_add_NetworkReadOnlyAdminCapability(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId = TraceId("add_NetworkReadOnlyAdminCapability")
  val networksCollection: MongoCollection[Document] = db.getCollection("networks_new")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    log.info("Starting migration _0059_add_NetworkReadOnlyAdminCapability")

    val updateEntitlements = Updates.addToSet("entitlements", Capabilities.ReadOnlyAdmin)

    for {
      _ <- networksCollection.updateMany(Document.empty, updateEntitlements).toFuture()
    } yield ()
  }
}
