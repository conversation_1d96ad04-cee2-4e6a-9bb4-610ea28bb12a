package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0005_network_index_cleanup(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_5_network_index_cleanup")
  log.info("Starting migration")

  val networksCol: MongoCollection[Document] = db.getCollection("networks_new")

  val indexName = "networkCode_1"

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    networksCol.dropIndex(indexName).toFuture.map(_ => ())

  }

}