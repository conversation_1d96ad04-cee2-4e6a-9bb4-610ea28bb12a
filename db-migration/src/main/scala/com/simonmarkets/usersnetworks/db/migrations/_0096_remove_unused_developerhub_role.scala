package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.{Filters, Updates}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0096_remove_unused_developerhub_role(
    val db: MongoDatabase) extends ChangeSet(db) {
  implicit val traceId: TraceId = TraceId("_0096_remove_unused_developerhub_role")

  lazy val usersColl: MongoCollection[Document] = db.getCollection("users")
  lazy val networksColl: MongoCollection[Document] = db.getCollection("networks_new")

  def apply(implicit
      executionContext: ExecutionContext): Future[Unit] = {
    (for {
      _ <- usersColl.updateMany(Filters.empty(), Updates.pull("customRoles", "DeveloperHubAdmin"))
      _ <- networksColl.updateMany(Filters.empty(), Updates.pullByFilter(Filters.eq("customRolesConfig", Filters.equal("role", "DeveloperHubAdmin"))))
    } yield ()).toFuture().map(_ => ())
  }
}
