package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.{IssuerPurview, NetworkCategory, Purview, PurviewEntity}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.encoders.NetworkFormat
import org.mongodb.scala.bson.Document
import org.mongodb.scala.model.{Filters, UpdateOneModel, Updates}
import org.mongodb.scala.{BulkWriteResult, MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0107_add_purviews_to_network(val db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0107_add_purviews_to_network")

  lazy val networks: MongoCollection[Document] = db.getCollection[Document]("networks_new")

  def mapIssuerPurviewToPurview(issuerPurview: IssuerPurview): Purview = {
    val purviewEntities = issuerPurview.issuers.map { issuer =>
      PurviewEntity(
        key = issuer,
        purviewType = Some(NetworkCategory.Issuer),
        domains = issuerPurview.purviewedDomainsUpdated
      )
    } ++ issuerPurview.wholesaler.map { wholesaler =>
      PurviewEntity(
        key = wholesaler.toString,
        purviewType = Some(NetworkCategory.Wholesaler),
        domains = issuerPurview.purviewedDomainsUpdated
      )
    }

    Purview(
      networkId = issuerPurview.network.toString,
      purviewEntities = purviewEntities
    )
  }

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      networkDocs <- networks.find().toFuture
      updates = networkDocs.map { dbo =>
        val network = NetworkFormat.read(dbo)
        val purviews = network.purviewNetworks.getOrElse(Set.empty[IssuerPurview]).map(mapIssuerPurviewToPurview)

        val setQuery = Updates.set("purviews", purviews.map(p => Document(
          "networkId" -> p.networkId,
          "purviewEntities" -> p.purviewEntities.map(e => Document(
            "key" -> e.key,
            "purviewType" -> e.purviewType.map(_.toString),
            "domains" -> e.domains.map(_.map(_.productPrefix).toList)
          )).toList
        )).toList)

        UpdateOneModel(
          Filters.eq("_id", dbo.getObjectId("_id")),
          setQuery
        )
      }
      result <- if (updates.nonEmpty) networks.bulkWrite(updates.toList).toFuture else Future.unit
      _ = result match {
        case res: BulkWriteResult => log.info(s"Total network updates made: ${res.getModifiedCount}")
        case _ => log.info("No network updates were made")
      }
    } yield ()
  }
}
