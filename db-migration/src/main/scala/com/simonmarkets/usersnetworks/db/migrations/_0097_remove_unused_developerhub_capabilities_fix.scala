package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0097_remove_unused_developerhub_capabilities_fix(
    val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0097_remove_unused_developerhub_capabilities_fix")

  def capabilities: Set[Capability] = Set(
    "developer",
    "viewDeveloperHubViaUserId",
    "viewDeveloperHubServicesAllEnv",
    "viewDeveloperHubServicesByEnv",
    "viewDeveloperHubExternalUserPreferencesViaUserId",
    "viewDeveloperHubInternalUserPreferencesViaUserId",

    "editDeveloperHubInternalUserPreferencesViaUserId",
    "editDeveloperHubExternalUserPreferencesViaUserId"
  )

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network, customRole: Role): Boolean = true
}
