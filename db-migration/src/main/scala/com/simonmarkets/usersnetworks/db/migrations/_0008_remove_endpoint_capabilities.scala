package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0008_remove_endpoint_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_8_remove_endpoint_capabilities")

  def capabilities: Set[Capability] = Set(
    "EqPIPGGSAdmin",
    "EqPIPGMarqueeManager",
    "EqPIPGFAManager",
    "EqPIPGFA",
    "EqPIPGDeveloper",
    "EqPIPGPB",
    "DataUpdateUser",
    "EqPIPGDataAdmin",
    "EqPIPGTwdSystemUser",
    "EqPIPGHealthcheck",
    "EqPIPGAuctionAdmin",
    "EqPIPGSIMONSystemUser",
    "Issuer",
    "Wholesaler",
    "EAppProvider"
  )

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network, customRole: Role): Boolean = true

}
