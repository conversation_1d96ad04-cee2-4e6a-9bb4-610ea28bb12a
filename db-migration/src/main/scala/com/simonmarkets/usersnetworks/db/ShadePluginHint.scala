package com.simonmarkets.usersnetworks.db

import com.simonmarkets.db.migration.MigrationController

object ShadePluginHint {

  /**
   * This object is for the Maven Shade plugin. We keep a reference to the MigrationController here, so that
   * it will keep this class and all its references in the uber jar. There is no need to specify everything in
   * the pom.xml one by one.
   */
  object ShadePluginHint {
    def controller = new MigrationController
  }

}
