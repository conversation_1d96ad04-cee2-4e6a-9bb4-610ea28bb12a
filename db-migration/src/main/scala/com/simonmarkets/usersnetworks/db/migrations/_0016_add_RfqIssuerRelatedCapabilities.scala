package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.{RfqCapabilities, SimonUICapabilities}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0016_add_RfqIssuerRelatedCapabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0016_add_RfqIssuerRelatedCapabilities")

  def capabilities: Set[Capability] = Set(RfqCapabilities.ReceiveRfqViaNetworkIssuerPurview, RfqCapabilities.InProgressRfqViaNetworkIssuerPurview,
    RfqCapabilities.QuoteRfqViaNetworkIssuerPurview, RfqCapabilities.PendingIssuerApprovalRfqViaNetworkIssuerPurview,
    RfqCapabilities.IssuerApproveRfqViaNetworkIssuerPurview, RfqCapabilities.IssuerAcknowledgeRfqViaNetworkIssuerPurview,
    RfqCapabilities.IssueRfqViaNetworkIssuerPurview, RfqCapabilities.DeclineRfqViaNetworkIssuerPurview, SimonUICapabilities.ViewUIRfqQuoteDetailsCapability.name,
    "viewUIRfqBacktestInvestment", "viewUIRfqCustomizeInBuilder", SimonUICapabilities.ViewUIRfqAuditTrailCapability.name,
    SimonUICapabilities.ViewUIRfqTemplateRequestToTradeCapability.name)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    rolesConfig.capabilities.contains("editRfqViaNetworkIssuerPurview"))

}
