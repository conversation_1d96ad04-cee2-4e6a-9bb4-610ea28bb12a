package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.NetworkType
import com.simonmarkets.capabilities.OfferingsV1Capabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0047_remove_tradeOfferingViaTradePayoffEntitlement_from_issuers(
    val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0047_remove_tradeOfferingViaTradePayoffEntitlement_from_issuers")

  def capabilities: Set[Capability] = Set(OfferingsV1Capabilities.TradeOfferingViaTradePayoffEntitlement)

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network,
      customRole: Role): Boolean = network.networkTypes.exists(_.contains(NetworkType.Issuer))
}
