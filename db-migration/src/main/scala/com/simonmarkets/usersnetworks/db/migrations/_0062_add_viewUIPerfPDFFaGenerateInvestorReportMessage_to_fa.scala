package com.simonmarkets.usersnetworks.db.migrations

import com.goldmansachs.marquee.pipg.UserRole
import com.simonmarkets.capabilities.SimonUICapabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0062_add_viewUIPerfPDFFaGenerateInvestorReportMessage_to_fa(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0062_add_viewUIPerfPDFFaGenerateInvestorReportMessage_to_fa")

  def capabilities: Set[Capability] = Set(
    SimonUICapabilities.ViewUIPerfPDFFaGenerateInvestorReportMessageCapability.name
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    rolesConfig.role == UserRole.EqPIPGFA.productPrefix)
}
