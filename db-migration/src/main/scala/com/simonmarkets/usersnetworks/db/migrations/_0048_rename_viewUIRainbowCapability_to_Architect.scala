package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.migrations._0048_rename_viewUIRainbowCapability_to_Architect._
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.model.Updates.{combine, set}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class _0048_rename_viewUIRainbowCapability_to_Architect(val db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0048_rename_viewUIRainbowCapability_to_Architect")

  log.info("Starting migration")

  val networkCollection: MongoCollection[Network] = db.getCollection[Network]("networks_new").withCodecRegistry(networkCodecRegistry)
  val oldViewUIRainbowCapability: String = "viewUIRainbow"
  val oldCapabilities = Seq(oldViewUIRainbowCapability)
  val capabilityField: String = "customRolesConfig.capabilities"

  private def getUpdatedCustomRolesConfig(customRolesConfig: Set[CustomRoleDefinition]): Set[CustomRoleDefinition] = {
    customRolesConfig.map { roleCapabilities =>
      val updatedCapabilities = roleCapabilities.capabilities.map { capability =>
        if (capability == oldViewUIRainbowCapability) "viewUIArchitect"
        else capability
      }
      roleCapabilities.copy(capabilities = updatedCapabilities)
    }
  }

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      networks <- networkCollection.find(equal(capabilityField, oldViewUIRainbowCapability)).toFuture

      _ = log.info(s"Updating viewUIRainbow to viewUIArchitect")

      updates <- Future.traverse(networks) { network => {
        val updatedCustomRolesConfig = getUpdatedCustomRolesConfig(network.customRolesConfig)
        log.info(s"Updating network ${network.id}")

        networkCollection.updateOne(
          equal("id", network.id),
          combine(
            set("customRolesConfig", updatedCustomRolesConfig)
          )
        ).toFuture.transformWith {
          case Success(result) => Future.successful(result.getModifiedCount)
          case Failure(ex) =>
            log.error(s"Error occurred while updating network ${network.id}", ex)
            Future.successful(0L)
        }
      }
      }
    } yield updates.sum
  }
}


object _0048_rename_viewUIRainbowCapability_to_Architect {
  case class Network(
      id: String,
      customRolesConfig: Set[CustomRoleDefinition] = Set.empty,
  )

  case class CustomRoleDefinition(
      role: String,
      capabilities: Set[String],
  )

  val networkCodecRegistry: CodecRegistry = fromRegistries(
    fromProviders(
      createCodecProviderIgnoreNone[Network],
      classOf[CustomRoleDefinition]
    ),
    DEFAULT_CODEC_REGISTRY
  )
}
