package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0042_cleanup_and_add_unique_networkId_index_for_networkEntities(
    db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0042_cleanup_and_add_unique_networkId_index_for_networkEntities")
  log.info("Starting migration _0042_cleanup_and_add_unique_networkId_index_for_networkEntities")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val collection = mongoDB.getCollection("networkEntities")
    collection.dropIndex("networkId_1").toFuture.map(_ => ())
    log.info("index dropped, adding unique index")
    collection.createIndex(ascending("networkId"), new IndexOptions().unique(true)).toFuture.map(_ => ())
  }

}
