package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.repository.MongoUserImpersonationRepository.userImpersonationCodecRegistry
import org.mongodb.scala.bson.Document
import org.mongodb.scala.model.{Filters, Updates}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0100_add_impersonatedNetworkId_field_to_impersonation(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0100_add_impersonatedNetworkId_field_to_impersonation")

  // MongoDB Collection
  val coll: MongoCollection[Document] = mongoDB.getCollection("users.impersonation").withCodecRegistry(userImpersonationCodecRegistry)

  // Fields
  private val ImpersonatedNetworkId = "impersonatedNetworkId"

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    log.info("Start to add empty impersonatedNetworkId to pipg.users.impersonation.")
    for {
      userImpersonations <- coll
        .updateMany(
          Filters.exists(ImpersonatedNetworkId, exists = false),
          Updates.set(ImpersonatedNetworkId, ""))
        .toFuture()

      _ = log.info(s"Updated ${userImpersonations.getModifiedCount} with impersonatedNetworkId field")
    } yield ()
  }
}
