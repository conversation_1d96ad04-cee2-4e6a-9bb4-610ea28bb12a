package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.{ChangeSet, CustomConfig}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.repository.encoders.UserFormat
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Filters.{equal, exists, nin}
import org.mongodb.scala.model.{Filters, UpdateOneModel}
import org.mongodb.scala.model.Updates.{combine, push, set}
import pureconfig.generic.auto._

import scala.concurrent.{ExecutionContext, Future}

class _0050_migrate_citi_external_ids (db: MongoDatabase) extends ChangeSet(db) with TraceLogging with CustomConfig {
  case class CitiNetworks(networkIds: Set[String])

  private val citiNetworks = loadConfig[CitiNetworks]

  implicit val traceId: TraceId = TraceId("_0050_migrate_citi_external_ids")

  log.info("Starting migration")

  //field names
  private val Id = "id"
  private val NetworkId = "networkId"
  private val ExternalIds = "externalIds"
  private val Subject = "subject"
  private val DistributorId = "distributorId"

  //constants
  private val Citi = "citi"

  val usersColl: MongoCollection[Document] = db.getCollection("users")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      existingUsers <- usersColl.find(
        Filters.and(
          Filters.in(NetworkId, citiNetworks.networkIds.toSeq: _*),
          exists(DistributorId, exists = true),
          nin("externalIds.subject", Citi)
        )
      ).map(UserFormat.read).toFuture
      _ = log.info(s"Updating externalIds for user ids ${existingUsers.map(_.id).mkString("\n")}")
      updates = existingUsers.flatMap { u =>
        val distIdOpt = u.distributorId
        distIdOpt.map { distId =>
          UpdateOneModel(
            equal(Id, u.id),
            combine(
              set(DistributorId, distId.toUpperCase),
              push(ExternalIds, Document(Subject -> Citi, Id -> distId.toUpperCase))
            )
          )
        }
      }
      _ <-
        if (updates.nonEmpty)
          usersColl.bulkWrite(updates).toFuture
        else Future.unit
    } yield ()
  }

}
