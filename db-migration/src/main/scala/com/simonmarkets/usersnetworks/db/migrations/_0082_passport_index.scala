package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0082_passport_index(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0082_passport_index")

  //collections
  val col = mongoDB.getCollection("users.passport")

  //fields
  val Code = "code"

  //index options
  private val unique = new IndexOptions().unique(true)

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      _ <- col.createIndex(key = ascending(Code), options = unique).toFuture()
    } yield ()
  }

}