package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.model.Updates.set
import org.mongodb.scala.model.{Filters, UpdateOneModel}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0085_backfill_network_domiciles(val db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0085_backfill_network_domiciles")
  log.info("Starting migration")

  //field names
  private val Id = "id"
  private val Domiciles = "domiciles"

  //default value
  private val DefaultDomiciles: Seq[String] = Seq("US")

  val networksColl: MongoCollection[Document] = db.getCollection("networks_new")

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      existingNetworks <- networksColl.find(
        Filters.or(
          Filters.eq(Domiciles, Seq.empty),
          Filters.exists(Domiciles, exists = false)
        )
      ).toFuture
      _ = log.info(s"Found ${existingNetworks.size} networks to update.")
      _ = log.info(s"Updating domiciles for the following network ids: ${existingNetworks.map(_(Id).toString).mkString("\n")}")
      updates = existingNetworks.map { n =>
        UpdateOneModel(
          equal(Id, n(Id)),
          set(Domiciles, DefaultDomiciles)
        )
      }
      _ <- if (updates.nonEmpty)
        networksColl
          .bulkWrite(updates)
          .toFuture
          .map(r => log.info("Bulk update result", r))
      else Future.unit
    } yield ()
  }
}
