package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0060_feature_sets_index(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0060_feature_sets_index")
  log.info("Starting migration")

  private val col = mongoDB.getCollection("simonOnboarding.feature")

  private val uniqueOption = new IndexOptions().unique(true)

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      _ <- col.dropIndexes.toFuture
      _ <- col.createIndex(ascending("id"), uniqueOption).toFuture
      _ <- col.createIndex(ascending("title"), uniqueOption).toFuture
      _ = log.info(s"Feature set indexes created")
    } yield ()
  }
}
