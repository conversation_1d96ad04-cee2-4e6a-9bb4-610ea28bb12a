package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.usersnetworks.db.data.networkEntity.{ProprietaryIndexIdentifiers, ProprietaryIndexNewSchema, ProprietaryIndicesInfo}
import com.simonmarkets.usersnetworks.db.migrations._0053_change_proprietaryIndices_structure_in_networkEntities.{NetworkEntities, NetworkEntitiesNewSchema, networkEntityCodecs}
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.model.Updates.set
import org.mongodb.scala.model.{UpdateOneModel, UpdateOptions}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}

class _0053_change_proprietaryIndices_structure_in_networkEntities(
    db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("_0053_change_proprietaryIndices_structure_in_networkEntities")
  val collectionExisting: MongoCollection[NetworkEntities] = db.getCollection[NetworkEntities]("networkEntities").withCodecRegistry(networkEntityCodecs)
  val collectionUpdated: MongoCollection[NetworkEntitiesNewSchema] = db.getCollection[NetworkEntitiesNewSchema]("networkEntities").withCodecRegistry(networkEntityCodecs)

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val networkIdToPropIndicesMap = getEnvironment.toLowerCase match {
      case "alpha" => ProprietaryIndicesInfo.networkIdToProprietaryIndicesMapAlphaQA
      case "qa" => ProprietaryIndicesInfo.networkIdToProprietaryIndicesMapAlphaQA
      case "prod" => ProprietaryIndicesInfo.networkIdToProprietaryIndicesMapProd
    }
    for {
      documents <- collectionExisting.find().toFuture()
      _ = log.info(s"Number of records to update ${documents.size}")
      databaseWrites = documents.map { document =>
        val propIndices = networkIdToPropIndicesMap.getOrElse(
          document.networkId,
          List(ProprietaryIndexNewSchema("ShortName-DBmig", ProprietaryIndexIdentifiers("MQID-DBmig", "BBID-DBmig")))
        )
        UpdateOneModel(
          equal("networkId", document.networkId),
          set("proprietaryIndices", propIndices),
          UpdateOptions().upsert(false)
        )
      }
      successfulDatabaseWrites <- collectionUpdated.bulkWrite(databaseWrites).toFuture()
      _ = log.info(s"Successfully updated proprietary indices from ${successfulDatabaseWrites.getModifiedCount} records")
    } yield ()
  }

  private def getEnvironment: String = {
    val env_key = "ENV_KEY"
    val osLookup = System.getenv().asScala.toMap
    val env = osLookup.getOrElse(env_key, "prod")
    log.info(s"Environment is '$env'")
    env
  }
}

object _0053_change_proprietaryIndices_structure_in_networkEntities {
  val networkEntityCodecs: CodecRegistry =
    fromRegistries(
      fromProviders(
        classOf[NetworkEntities],
        classOf[NetworkEntitiesNewSchema],
        classOf[ProprietaryIndexNewSchema],
        classOf[ProprietaryIndexIdentifiers],
        classOf[ProprietaryIndex]
      ),
      DEFAULT_CODEC_REGISTRY
    )

  case class NetworkEntities(
      networkId: String,
      proprietaryIndices: Option[List[ProprietaryIndex]]
  )

  case class NetworkEntitiesNewSchema(
      networkId: String,
      proprietaryIndices: Option[List[ProprietaryIndexNewSchema]]
  )

  case class ProprietaryIndex(shortName: String, mqId: String)
}
