package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.RatesSPOfferingsCapabilities.ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability
import com.simonmarkets.capabilities.SPOfferingsCapabilities.ViewNonRegSSPPastOfferingsViaViewPayoffEntitlementsCapability
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0086_backfill_offerings_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0086_backfill_offerings_capabilities")

  def capabilities: Set[Capability] = Set(
    ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability.name, //new rates equivalent capability
    ViewNonRegSSPPastOfferingsViaViewPayoffEntitlementsCapability.name //existing equities capability that replaces viewNonRegSSPPastOffering
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network,
      customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    (rolesConfig.capabilities.contains(ViewNonRegSSPPastOfferingsViaViewPayoffEntitlementsCapability.name) ||
      rolesConfig.capabilities.contains("viewNonRegSSPPastOffering"))) //this capability will be retired
}
