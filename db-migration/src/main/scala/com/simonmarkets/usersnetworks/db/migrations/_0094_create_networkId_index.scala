package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.repository.MongoUserImpersonationApproversRepository.userImpersonationApproversCodecRegistry
import org.mongodb.scala.{Document, MongoCollection, MongoDatabase}
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0094_create_networkId_index(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0094_create_networkId_index")

  //collections
  val col: MongoCollection[Document] = mongoDB.getCollection("users.impersonation.approvers").withCodecRegistry(userImpersonationApproversCodecRegistry)

  //fields
  val NetworkId = "networkId"

  //index options
  private val unique = new IndexOptions().unique(true)

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    for {
      _ <- col.createIndex(key = ascending(NetworkId), options = unique).toFuture()
      _ = log.info("NetworkId index created")
    } yield ()
  }

}