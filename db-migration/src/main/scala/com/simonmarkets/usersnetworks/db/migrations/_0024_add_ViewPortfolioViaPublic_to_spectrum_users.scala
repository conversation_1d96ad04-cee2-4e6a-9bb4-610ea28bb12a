package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.{PortfolioCapabilities, SpectrumLearningCenterCapabilities}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0024_add_ViewPortfolioViaPublic_to_spectrum_users (val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0024_add_ViewPortfolioViaPublic_to_spectrum_users")

  def capabilities: Set[Capability] = Set(PortfolioCapabilities.ViewPortfolioViaPublic, PortfolioCapabilities.EditPortfolioViaOwner, PortfolioCapabilities.ViewPortfolioViaOwner)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    rolesConfig.capabilities.contains(SpectrumLearningCenterCapabilities.ViewSpectrumLearningCenter))

}
