package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.MongoDatabase
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0003_external_id_network_index(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_3_external_id_network_index")
  log.info("Starting migration")

  val externalIdTypeColl = mongoDB.getCollection("users.externalIdTypes")

  val MatchableNetworks = "matchableNetworks"

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      _ <- externalIdTypeColl.createIndex(key = ascending(MatchableNetworks)).toFuture()
      _ = log.info(s"External id type index created on $MatchableNetworks")
    } yield ()
  }
}
