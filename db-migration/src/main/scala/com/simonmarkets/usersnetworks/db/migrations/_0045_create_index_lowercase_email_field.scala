package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.{ChangeSet}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.repository.encoders.UserFormat.Fields
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0045_create_index_lowercase_email_field(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId: TraceId = TraceId("create-index-lowercase-email")
  val usersCollection: MongoCollection[Document] = db.getCollection("users")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    log.info("Starting migration")
    usersCollection.createIndex(ascending(Fields.EmailLowerCase), new IndexOptions().unique(false)).toFuture.map(_ => ())
  }
}
