package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.migrations._0027_add_viewUIBacktestingComparisonCapability_capability.annuityNetworks
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

// 2022-10-25 turning ON this capability means they can NOT see the feature
// want to turn it ON for all networks except admin/simon demo networks so that they can't see it
class _0027_add_viewUIBacktestingComparisonCapability_capability(val db: MongoDatabase)
    extends ChangeSet(db)
    with UpdateNetworkCapabilities {

  implicit val traceId: TraceId = TraceId("_0027_add_viewUIBacktestingComparisonCapability_capability")

  def capabilities: Set[Capability] = Set("viewUIBacktestingComparisonCapability")

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = annuityNetworks.contains(network.name)

}

object _0027_add_viewUIBacktestingComparisonCapability_capability {

  // all network names of distributors with approved annuity products in production
  // not including admin/simon demo networks
  val annuityNetworks =
    Set(
      "Allianz Life",
      "Allstate",
      "Alphastar",
      "Ameritas",
      "Arvest",
      "Atria- Annuity Demo",
      "Avantax Investment Services",
      "Bank of America Merrill Lynch",
      "Bank of Oklahoma",
      "Barnabas",
      "BBVA-Compass",
      "BD Demo Network",
      "Black Diamond Wealth Platform",
      "Brighthouse Financial",
      "Cambridge Investment Research",
      "Centaurus Financial",
      "Cetera Advisor Networks LLC",
      "Cetera Advisors LLC",
      "Cetera Financial Group",
      "Cetera Financial Specialists LLC",
      "Cetera Investment Services LLC",
      "Citizens Securities Inc.",
      "City National",
      "Concourse Financial Group Securities, Inc",
      "Credit Suisse Annuities Demo",
      "CWM",
      "Demo Only: Cross-Product Network",
      "Deprecated Barnabas",
      "Edward Jones",
      "Envestnet",
      "EqualWeb",
      "Equitable Advisors",
      "Equity Services",
      "Financial Independence Group",
      "Financial Independence Group Sub-Network",
      "Financial Independence Group Sub-Network 2",
      "First Allied",
      "First Citizens Investor Services",
      "First Horizon Advisors",
      "First Republic",
      "Global Atlantic Distribution",
      "Goldman Sachs",
      "GSAS Home Office",
      "Hantz",
      "Hilltop Securities Inc",
      "Huntington",
      "IGNORE - DO NOT USE -LPL Annuities",
      "Invesco",
      "J.P. Morgan",
      "Janney",
      "JP Morgan Advisors Demo Only",
      "JP Morgan Annuity",
      "JPMS",
      "Kalos Capital",
      "Kovack Securities Inc.",
      "Lincoln Financial Group",
      "Lion Street Financial",
      "LPL Financial LLC",
      "MassMutual",
      "MIT",
      "Nationwide Mutual Insurance Company",
      "Oak Ridge Financial",
      "Pacific Life",
      "PNC Invs LLC",
      "Principal",
      "Producers Choice",
      "PRUCO Securities",
      "Prudential Financial",
      "Prudential RIA",
      "Raymond James",
      "RBC Capital",
      "Sanctuary Securities",
      "Santander",
      "Schwab",
      //"SIMON 2.0 Demo",
      //"SIMON Admin",
      //"SIMON Annuities Demo",
      //"SIMON Annuities Only Demo",
      //"SIMON Annuity Only Test",
      "Stifel Nicolaus",
      "Summit Brokerage Services Inc",
      "Symetra Securities",
      "Synovus",
      "TD Bank",
      //"Test Network 1",
      //"Test Network 2",
      "Truist Investment Services",
      "U.S. Bancorp Investments",
      "UBS Distributors",
      "United Brokerage Services",
      "Vention Partners",
      "WB Secondary Demo Network",
      "Wells Fargo Advisors",
      "Wells Fargo Annuity",
      "World Equity Group"
    )

}
