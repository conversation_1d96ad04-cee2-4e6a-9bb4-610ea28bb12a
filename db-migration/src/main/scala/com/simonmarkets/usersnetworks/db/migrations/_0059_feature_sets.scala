package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.{Document, MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0059_feature_sets(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0059_feature_sets")
  log.info("Starting migration")

  val col: MongoCollection[Document] = db.getCollection("simonOnboarding.feature")

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    col.drop().toFuture.map(_ => ())
  }

}