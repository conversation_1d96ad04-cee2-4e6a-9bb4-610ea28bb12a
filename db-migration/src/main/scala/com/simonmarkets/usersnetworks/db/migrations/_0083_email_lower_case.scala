package com.simonmarkets.usersnetworks.db.migrations

import akka.actor.ActorSystem
import akka.stream.scaladsl.{Sink, Source}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.bson.codecs.configuration.CodecRegistries
import org.mongodb.scala.bson.codecs.Macros
import org.mongodb.scala.model.{Filters, UpdateOneModel, Updates}
import org.mongodb.scala.{MongoClient, MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0083_email_lower_case(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0083_email_lower_case")
  implicit val ac: ActorSystem = ActorSystem("migration")

  //collections
  val userCol: MongoCollection[MiniUser] = mongoDB
    .getCollection[MiniUser]("users")
    .withCodecRegistry(
      CodecRegistries.fromRegistries(
        MongoClient.DEFAULT_CODEC_REGISTRY,
        CodecRegistries.fromProviders(
          Macros.createCodecProviderIgnoreNone[MiniUser]
        )
      )
    )

  //fields
  val Email = "email"
  val Id = "id"

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      _ <- Source
        .fromPublisher(userCol.find)
        .grouped(1000)
        .mapAsync(1) { users =>
          val updatedUsers = users.collect {
            case user if user.email != user.email.toLowerCase =>
              UpdateOneModel(
                Filters.equal(Id, user.id),
                Updates.set(Email, user.email.toLowerCase)
              )
          }
          log.info(s"Updating ${updatedUsers.size} users")
          if (updatedUsers.nonEmpty) userCol.bulkWrite(updatedUsers).toFuture.map(_ => ())
          else Future.successful(())
        }
        .runWith(Sink.seq)
      _ <- userCol.dropIndex("emailLowerCase_1").toFuture
    } yield ()
  }

}

case class MiniUser(
    id: String,
    email: String
)