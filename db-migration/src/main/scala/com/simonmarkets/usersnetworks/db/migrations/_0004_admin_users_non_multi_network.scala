package com.simonmarkets.usersnetworks.db.migrations


import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.repository.encoders.UserFormat
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.model.Updates.set
import org.mongodb.scala.model.{Filters, UpdateOneModel}
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0004_admin_users_non_multi_network(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_4_admin_users_non_multi_network")
  log.info("Starting migration")

  //field names
  private val Id = "id"
  private val IdpLoginId = "idpLoginId"
  private val NetworkId = "networkId"

  //constant
  private val InternalNetworkIds: Seq[String] = Seq(
    "SIMON Admin",
    "a819e947-3b66-4b6d-bceb-25e3561e89d7", //Index Training Demo Network
    "1f742928-2c27-41da-9ab6-ba0979c64394", //Internal Training Network 1
    "feb10d98-0840-4143-812d-c02052973d6b", //Internal Training Network 2
    "34a36871-3a10-4185-ab5b-01dfe67cb8ef", //SIMON Training - Internal
    "76af6c1c-4346-4dff-8e61-fcc580161ee0", //SIMON Training Network - Internal 2
    "0d220496-38df-4290-bded-2502e5fe67fb", //Training Network
  )

  val usersColl: MongoCollection[Document] = db.getCollection("users")

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      existingUsers <- usersColl.find(Filters.in(NetworkId, InternalNetworkIds: _*)).map(UserFormat.read).toFuture
      _ = log.info(s"Updating idpLoginId for user ids ${existingUsers.map(_.id).mkString("\n")}")
      updates = existingUsers.map { u =>
        UpdateOneModel(equal(Id, u.id), set(IdpLoginId, u.email))
      }
      _ <- usersColl.bulkWrite(updates).toFuture
    } yield ()
  }
}
