package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0052_remove_lifecycleNotificationSettings_capabilities(val db: MongoDatabase)
  extends ChangeSet(db)
    with UpdateNetworkCapabilities {

  implicit val traceId: TraceId = TraceId("_0052_remove_lifecycleNotificationSettings_capabilities")

  def capabilities: Set[Capability] = Set("viewLifecycleNotificationSettingsViaOwner", "editLifecycleNotificationSettingsViaNetwork",
    "editLifecycleNotificationSettingsViaOwner", "viewLifecycleNotificationSettingsViaNetwork")

  def shouldAdd: Boolean = false

  def shouldUpdate(network: Network, customRole: Role): Boolean = true

}
