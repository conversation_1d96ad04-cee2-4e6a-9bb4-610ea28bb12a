package com.simonmarkets.usersnetworks.db.migrations

import com.mongodb.client.model.IndexOptions
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import org.mongodb.scala.model.Indexes.ascending

import scala.concurrent.{ExecutionContext, Future}

class _0038_network_visuals_index(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0038_network_visuals_index")
  log.info("Starting migration")

  val networksSnapshotColl: MongoCollection[Document] = mongoDB.getCollection("network.visuals")

  val id = "networkId"

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    val unique = new IndexOptions().unique(true)
    for {
    _ <- networksSnapshotColl.createIndex(key = ascending(id), options = unique).toFuture()
    _ = log.info(s"Network visuals collection index created on $id")
    } yield ()
  }
}