package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.RfqConfigsCapabilities.ViewRfqConfigsViaNetworkCapability
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0055_add_ViewRfqConfigsViaNetworkCapability_to_all_networks(
    val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0055_add_ViewRfqConfigsViaNetworkCapability_to_all_networks")

  def capabilities: Set[Capability] = Set(ViewRfqConfigsViaNetworkCapability.name)

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = true
}
