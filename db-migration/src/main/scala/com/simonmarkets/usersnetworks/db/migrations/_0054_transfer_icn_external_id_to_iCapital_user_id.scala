package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.repository.encoders.UserFormat
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.UpdateOneModel
import org.mongodb.scala.model.Updates._
import org.mongodb.scala.{MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0054_transfer_icn_external_id_to_iCapital_user_id(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_0054_transfer_icn_external_id_to_iCapital_user_id")
  log.info("Starting migration")

  //fields
  private val Id = "id"
  private val ExternalIds = "externalIds"
  private val Subject = "subject"
  private val ICapitalUserId = "iCapitalUserId"

  val usersColl: MongoCollection[Document] = db
    .getCollection("users")
    .withCodecRegistry(UserFormat.codecRegistry)

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      users <- usersColl.find(elemMatch(ExternalIds, equal(Subject, "icn"))).map(UserFormat.read).toFuture
      _ = log.info("Updating users", "userIds" -> users.map(_.id))
      updates = users.map { user =>
        val externalIdsWithoutIcnId = user.externalIds.filter(_.subject != "icn")
        UpdateOneModel(
          filter = equal(Id, user.id),
          update = combine(
            set(ICapitalUserId, user.externalIds.find(_.subject == "icn").map(_.id).getOrElse("")),
            set(ExternalIds, externalIdsWithoutIcnId)
          )
        )
      }
      _ <- if (updates.nonEmpty)
        usersColl
          .bulkWrite(updates)
          .toFuture
          .map(r => log.info("Bulk update result", r))
      else Future.unit
    } yield ()
  }

}

