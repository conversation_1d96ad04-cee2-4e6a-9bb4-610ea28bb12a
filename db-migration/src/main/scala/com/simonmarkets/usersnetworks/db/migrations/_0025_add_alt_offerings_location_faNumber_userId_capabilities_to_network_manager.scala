package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.AltOfferingCapabilities.{ViewApprovedAltOfferingViaFaNumberCapability, ViewApprovedAltOfferingViaLocationCapability, ViewApprovedAltOfferingViaUserCapability, ViewClosedAltOfferingViaFaNumberCapability, ViewClosedAltOfferingViaLocationCapability, ViewClosedAltOfferingViaUserCapability, ViewPendingAltOfferingViaFaNumberCapability, ViewPendingAltOfferingViaLocationCapability, ViewPendingAltOfferingViaUserCapability}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.migrations._0025_add_alt_offerings_location_faNumber_userId_capabilities_to_network_manager.ALTS_FA_MANAGER_ROLE
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0025_add_alt_offerings_location_faNumber_userId_capabilities_to_network_manager(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0025_add_alt_offerings_location_faNumber_userId_capabilities_to_network_manager")

  def capabilities: Set[Capability] = Set(
    ViewApprovedAltOfferingViaUserCapability.name,
    ViewPendingAltOfferingViaUserCapability.name,
    ViewClosedAltOfferingViaUserCapability.name,
    ViewApprovedAltOfferingViaLocationCapability.name,
    ViewPendingAltOfferingViaLocationCapability.name,
    ViewClosedAltOfferingViaLocationCapability.name,
    ViewApprovedAltOfferingViaFaNumberCapability.name,
    ViewPendingAltOfferingViaFaNumberCapability.name,
    ViewClosedAltOfferingViaFaNumberCapability.name
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig => customRole == rolesConfig.role &&
    customRole == ALTS_FA_MANAGER_ROLE)
}

object _0025_add_alt_offerings_location_faNumber_userId_capabilities_to_network_manager {
  val ALTS_FA_MANAGER_ROLE = "AltsFAManager"
}
