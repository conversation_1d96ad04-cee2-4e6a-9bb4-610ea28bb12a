package com.simonmarkets.usersnetworks.db.migrations


import com.simonmarkets.capabilities.{AccountsCapabilities, Capability, OrderAccountsCapabilities}
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.networks.common.encoders.NetworkFormat
import org.mongodb.scala.{BulkWriteResult, Document, MongoDatabase}
import com.simonmarkets.networks.common.encoders.NetworkFormat.CustomRoleDefinitionFormat
import com.goldmansachs.marquee.pipg.CustomRoleDefinition
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.model.Filters.{equal, in}
import org.mongodb.scala.model.UpdateOneModel
import org.mongodb.scala.model.Updates.set
import simon.Id.NetworkId

import scala.concurrent.Future

class _0080_add_OrderAccountsCapabilities(val db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val tid = TraceId("add_OrderAccountsCapabilities")
  val capabilityField: String = "customRolesConfig.capabilities"

  val networksCollection = db.getCollection[Document]("networks_new")

  def toAddCapabilities: Map[Capability, Capability] = Map(
    AccountsCapabilities.ViewAccountViaNetworkCapability -> OrderAccountsCapabilities.ViewOrderAccountViaNetworkCapability,
    AccountsCapabilities.ViewAccountViaLocationCapability -> OrderAccountsCapabilities.ViewOrderAccountViaLocationCapability,
    AccountsCapabilities.ViewAccountViaFaNumberCapability -> OrderAccountsCapabilities.ViewOrderAccountViaFaNumberCapability,
    AccountsCapabilities.ViewAccountViaPurviewedDomainCapability -> OrderAccountsCapabilities.ViewOrderAccountViaPurviewedDomainCapability,
    AccountsCapabilities.ViewAccountViaLocationHierarchyCapability -> OrderAccountsCapabilities.ViewOrderAccountViaLocationHierarchyCapability,
    AccountsCapabilities.ViewAccountViaDistributorIdCapability -> OrderAccountsCapabilities.ViewOrderAccountViaDistributorIdCapability,
    AccountsCapabilities.ViewAccountViaOwnerCapability -> OrderAccountsCapabilities.ViewOrderAccountViaOwnerCapability,
    AccountsCapabilities.EditAccountViaNetworkCapability -> OrderAccountsCapabilities.EditOrderAccountViaNetworkCapability,
    AccountsCapabilities.UploadAccountsSnapshotViaNetworkCapability -> OrderAccountsCapabilities.UploadOrderAccountsSnapshotViaNetworkCapability
  )

  def apply(implicit executionContext: scala.concurrent.ExecutionContext): Future[Unit] = {
    for {
      networks <- networksCollection.find(in(capabilityField, toAddCapabilities.keys.map(_.name).toSeq: _*)).toFuture
      updates = networks.map { dbo =>
        val network = NetworkFormat.read(dbo)
        val networkUpdate = network.customRolesConfig.foldLeft(Set.empty[CustomRoleDefinition]) {
            case (networkUpdate: Set[CustomRoleDefinition], customRoleDefinition: CustomRoleDefinition) =>
              val accountCapabilities = toAddCapabilities.keys.filter(accountCapability => customRoleDefinition.capabilities.contains(accountCapability.name))
              val capabilitiesToAdd = accountCapabilities.flatMap(toAddCapabilities.get(_)).toSet
              networkUpdate ++ Set(customRoleDefinition.copy(capabilities = customRoleDefinition.capabilities ++ capabilitiesToAdd.map(_.name)))
          }
          networkUpdate.foreach(customRoleDefinition =>
            log.info(s"Updating : networkId=${network.id} networkName=${network.name} " +
              s"role=${customRoleDefinition.role} new capabilities='${customRoleDefinition.capabilities}'"))

          val setQuery = set("customRolesConfig", networkUpdate.map(definition => CustomRoleDefinitionFormat.write(definition)).toList)
          UpdateOneModel(equal("id", NetworkId.unwrap(network.id)), setQuery)
      }
      result <- if (updates.nonEmpty) networksCollection.bulkWrite(updates).toFuture else Future.unit
      _ = result match {
        case res: BulkWriteResult => log.info(s"Total network updates made: ${res.getModifiedCount}")
        case _ => log.info("No network updates were made")
      }
    } yield ()
  }
}
