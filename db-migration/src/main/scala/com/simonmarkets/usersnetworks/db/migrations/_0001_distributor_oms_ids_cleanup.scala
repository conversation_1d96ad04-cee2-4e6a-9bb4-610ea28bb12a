package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.users.common.User
import com.simonmarkets.users.repository.encoders.UserFormat
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.model.Updates.unset
import org.mongodb.scala.model.{Accumulators, Aggregates, Filters, UpdateOneModel}
import org.mongodb.scala.{MongoCollection, MongoDatabase}
import simon.Id.NetworkId

import java.time.LocalDateTime

import scala.concurrent.{ExecutionContext, Future}

class _0001_distributor_oms_ids_cleanup(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {

  implicit val traceId: TraceId = TraceId("_1_distributor_oms_ids_cleanup")
  log.info("Starting migration")

  //user fields
  private val DistributorId = "distributorId"
  private val OmsId = "omsId"

  //shared fields
  private val Id = "id"

  implicit val dateTimeOrdering: Ordering[Option[LocalDateTime]] = new Ordering[Option[LocalDateTime]] {
    override def compare(x: Option[LocalDateTime], y: Option[LocalDateTime]): Int = (x, y) match {
      case (Some(x), Some(y)) => x.compareTo(y)
      case (None, None) => 0
      case (Some(_), None) => 1
      case (None, Some(_)) => -1
    }
  }

  val usersColl: MongoCollection[Document] = db.getCollection("users")

  def apply(implicit executionContext: ExecutionContext): Future[Unit] = {

    for {
      dupeDistIds <- usersColl.aggregate(Seq(
        Aggregates.filter(Filters.exists(DistributorId)),
        Aggregates.group("$distributorId", Accumulators.sum("count", 1)),
        Aggregates.filter(Filters.gt("count", 1))
      )).map(_.getString("_id")).toFuture
      dupeOmsIds <- usersColl.aggregate(Seq(
        Aggregates.filter(Filters.exists(OmsId)),
        Aggregates.group("$omsId", Accumulators.sum("count", 1)),
        Aggregates.filter(Filters.gt("count", 1))
      )).map(_.getString("_id")).toFuture
      existingUsers <- usersColl.find(Filters.or(Filters.in(DistributorId, dupeDistIds:_*), Filters.in(OmsId, dupeOmsIds:_*))).map(UserFormat.read).toFuture
      distGroup = existingUsers
        .groupBy(u => (u.networkId, u.distributorId))
        .mapValues(_.sortBy(_.lastVisitedAt).reverse)
      omsGroup = existingUsers
        .groupBy(u => (u.networkId, u.omsId))
        .mapValues(_.sortBy(_.lastVisitedAt).reverse)
      dupeDistUsers = getTails(distGroup)
      _ = log.info(s"Users with dupe distributor ids ${dupeDistUsers.map(_.id).mkString(",")}")
      dupeOmsUsers = getTails(omsGroup)
      _ = log.info(s"Users with dupe oms ids ${dupeOmsUsers.map(_.id).mkString(",")}")

      updates = dupeDistUsers.toSeq.map { u =>
        UpdateOneModel(equal(Id, u.id), unset(DistributorId))
      } ++
        dupeOmsUsers.toSeq.map { u =>
          UpdateOneModel(equal(Id, u.id), unset(OmsId))
        }
      _ <- usersColl.bulkWrite(updates).toFuture

    } yield ()

  }

  private def getTails(group: Map[(NetworkId, Option[String]), Seq[User]]): Set[User] = {
    group.collect { case ((_, Some(_)), list) => list.tail }.flatten.toSet
  }

}