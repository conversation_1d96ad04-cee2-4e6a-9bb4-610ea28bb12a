package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.LearnV2ContentCustomizationsCapabilities._
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0105_add_viewContentCustomizationViaNetworkAndLocation_capability(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0105_add_viewContentCustomizationViaNetworkAndLocation_capability")

  def capabilities: Set[Capability] = Set(
    ViewContentCustomizationViaNetworkAndLocationCapability.name
  )

  private val oldContentCustomizationCapabilities: Set[Capability] = Set(
    ViewContentCustomizationViaNetworkCapability.name
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig =>
    customRole == rolesConfig.role &&
    rolesConfig.capabilities.intersect(oldContentCustomizationCapabilities).nonEmpty)
}
