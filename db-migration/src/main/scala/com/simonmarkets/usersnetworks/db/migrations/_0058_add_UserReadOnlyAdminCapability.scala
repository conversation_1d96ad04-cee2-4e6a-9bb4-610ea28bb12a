package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.{TraceId, TraceLogging}
import org.mongodb.scala.model.Updates
import org.mongodb.scala.{Document, MongoCollection, MongoDatabase}

import scala.concurrent.{ExecutionContext, Future}

class _0058_add_UserReadOnlyCapabilities_capability(db: MongoDatabase) extends ChangeSet(db) with TraceLogging {
  implicit val traceId = TraceId("add_UserReadOnlyAdminCapability")
  val usersCollection: MongoCollection[Document] = db.getCollection("users")

  override def apply(implicit executionContext: ExecutionContext): Future[Unit] = {
    log.info("Starting migration _0060_add_UserReadOnlyCapabilities_capability")

    val updateEntitlements = Updates.addToSet("entitlements", Capabilities.ReadOnlyAdmin)

    for {
      _ <- usersCollection.updateMany(Document.empty, updateEntitlements).toFuture()
    } yield ()
  }
}