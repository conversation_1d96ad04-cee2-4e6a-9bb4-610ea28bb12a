package com.simonmarkets.usersnetworks.db.migrations

import com.simonmarkets.capabilities.LearnV2TrackCustomizationsCapabilities._
import com.simonmarkets.capabilities.LearnV2TracksCapabilities
import com.simonmarkets.db.migration.model.ChangeSet
import com.simonmarkets.logging.TraceId
import com.simonmarkets.networks.common.Network
import com.simonmarkets.usersnetworks.db.utils.UpdateNetworkCapabilities
import org.mongodb.scala.MongoDatabase

class _0063_add_viewApprovedTrackCustomization_capabilities(val db: MongoDatabase) extends ChangeSet(db) with UpdateNetworkCapabilities {
  implicit val traceId: TraceId = TraceId("_0063_add_viewApprovedTrackCustomization_capabilities")

  def capabilities: Set[Capability] = Set(
    ViewApprovedTrackCustomizationViaNetworkCapability.name,
    ViewApprovedTrackCustomizationViaNetworkAndLocationCapability.name,
    ViewApprovedTrackCustomizationViaNetworkAndFaNumberCapability.name,
    ViewApprovedTrackCustomizationViaUserIdCapability.name
  )

  private val oldTrackCapabilities: Set[Capability] = Set(
    LearnV2TracksCapabilities.ViewActiveTracksViaTrackIdCapability.name,
    LearnV2TracksCapabilities.ViewAllTracksViaTrackIdCapability.name
  )

  def shouldAdd: Boolean = true

  def shouldUpdate(network: Network, customRole: Role): Boolean = network.customRolesConfig.exists(rolesConfig =>
    customRole == rolesConfig.role &&
    rolesConfig.capabilities.intersect(oldTrackCapabilities).nonEmpty)
}
