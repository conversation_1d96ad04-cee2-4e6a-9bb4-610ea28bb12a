<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.simonmarkets</groupId>
        <artifactId>users-networks</artifactId>
        <version>sandbox-SNAPSHOT</version>
    </parent>

    <artifactId>om-networks</artifactId>
    <version>sandbox-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>io.circe</groupId>
            <artifactId>circe-core_${scala.version.short}</artifactId>
        </dependency>
        <dependency>
            <groupId>io.circe</groupId>
            <artifactId>circe-generic_${scala.version.short}</artifactId>
        </dependency>
        <dependency>
            <groupId>io.circe</groupId>
            <artifactId>circe-parser_${scala.version.short}</artifactId>
        </dependency>
        <dependency>
            <groupId>io.simon</groupId>
            <artifactId>lib-openapi-generator</artifactId>
        </dependency>

        <!--test-->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
