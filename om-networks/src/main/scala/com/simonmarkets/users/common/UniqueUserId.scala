package com.simonmarkets.users.common

import com.simonmarkets.networks.ExternalId
import simon.Id.NetworkId

/**
 * Each of these uniquely identifies a SIMON user
 */
sealed trait UniqueUserId

object UniqueUserId {

  case class UniqueUserIdFailure(message: String)

  def unsafeApply(idType: IdType, stringId: Option[String], externalId: Option[ExternalId],
      networkId: Option[NetworkId]): Either[UniqueUserIdFailure, UniqueUserId] = {

    def userNetworkTup: Either[UniqueUserIdFailure, (String, NetworkId)] = for {
      user <- userLeftIfEmpty(stringId)
      network <- networkLeftIfEmpty(networkId)
    } yield (user, network)

    def userLeftIfEmpty(value: Option[String]) =
      leftIfEmpty(s"UserId required $idType", value)

    def networkLeftIfEmpty(value: Option[NetworkId]) =
      leftIfEmpty(s"NetworkId required for $idType", value)

    def leftIfEmpty[T](message: String, value: Option[T]): Either[UniqueUserIdFailure, T] = {
      value.toRight(UniqueUserIdFailure(message))
    }

    idType match {
      case IdType.Oms => userLeftIfEmpty(stringId).map(Oms)
      case IdType.Distributor => userNetworkTup.map(t => Distributor(t._1, t._2))
      case IdType.UserId => userLeftIfEmpty(stringId).map(Guid)
      case IdType.NPN => userNetworkTup.map(t => NPN(t._1, t._2))
      case IdType.Email => userNetworkTup.map(t => Email(t._1, t._2))
      case IdType.IdpId => userLeftIfEmpty(stringId).map(Idp)
      case IdType.ExternalId => leftIfEmpty(s"ExternalId required for $idType", externalId).map(External)
      case IdType.SSN => Left(UniqueUserIdFailure("SSN based query not supported"))
    }

  }

  case class Oms(id: String) extends UniqueUserId

  case class Distributor(id: String, networkId: NetworkId) extends UniqueUserId

  case class Guid(id: String) extends UniqueUserId

  case class NPN(id: String, networkId: NetworkId) extends UniqueUserId

  case class Email(id: String, networkId: NetworkId) extends UniqueUserId

  case class Idp(id: String) extends UniqueUserId

  case class External(id: ExternalId) extends UniqueUserId

}
