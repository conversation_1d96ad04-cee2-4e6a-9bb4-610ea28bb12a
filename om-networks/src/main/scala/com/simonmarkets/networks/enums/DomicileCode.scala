package com.simonmarkets.networks.enums

import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues("AU", "CA", "US")
sealed trait DomicileCode extends EnumEntry

object DomicileCode extends SafeEnums[DomicileCode] {

  case object AU extends DomicileCode

  case object CA extends DomicileCode

  case object US extends DomicileCode

  case object EnumNotFound extends DomicileCode

  val Values: Seq[DomicileCode] = Seq(
    AU,
    CA,
    US,
  )
}
