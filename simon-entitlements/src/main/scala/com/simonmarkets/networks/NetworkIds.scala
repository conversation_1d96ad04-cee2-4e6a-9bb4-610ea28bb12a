package com.simonmarkets.networks

import simon.Id.NetworkId


object NetworkIds {
  /*
    This is here so that 1. it's not a magic string, and 2. it's accessible from everywhere. Preference would be to
    put it in networks-common, but that can't be used from simon-entitlements because adding networks-common as a
    dependency to simon-entitlements leads to a circular dependency as such:
    lib-okta -> simon-entitlements -> networks-common -> lib-okta.
   */
  val AdminNetworkId: NetworkId.Type = NetworkId("SIMON Admin")
}
