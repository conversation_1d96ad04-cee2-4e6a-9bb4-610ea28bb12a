package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability, ReadOnlyAdmin, ReadOnlyAdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildGuidKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object ClientProfileCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {

  override val DomainName: String = "ClientProfile"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewClientProfileViaOwnerCapability: Capability = Capability("viewClientProfileViaOwner", "", assetClasses)
  val EditClientProfileViaOwnerCapability: Capability = Capability("editClientProfileViaOwner", "", assetClasses)

  val ViewClientProfileViaOwner: String = ViewClientProfileViaOwnerCapability.name
  val EditClientProfileViaOwner: String = EditClientProfileViaOwnerCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ReadOnlyAdminCapability, ViewClientProfileViaOwnerCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditClientProfileViaOwnerCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ReadOnlyAdmin -> AvailableKeyBuilder(buildAdminKeys),
    ViewClientProfileViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    EditClientProfileViaOwner -> AvailableKeyBuilder(buildGuidKeys),
  )
}
