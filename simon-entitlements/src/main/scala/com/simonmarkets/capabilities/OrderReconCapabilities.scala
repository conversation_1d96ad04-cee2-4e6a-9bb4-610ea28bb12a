package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkTypeKeys, buildGuidKeys, buildNetworkKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities}

object OrderReconCapabilities extends Capabilities with HasDetailedViewCapabilities
  with HasDetailedEditCapabilities with AvailableAccessKeysGenerator {
  override val DomainName: String = "OrderRecons"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewOrderReconViaNetworkTypeCapability: Capability = Capability("viewOrderReconViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditOrderReconViaNetworkTypeCapability: Capability = Capability("editOrderReconViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val ViewOrderReconViaNetworkCapability: Capability = Capability("viewOrderReconViaNetwork", "view Order Recon Via Network", assetClasses)
  val EditOrderReconViaNetworkCapability: Capability = Capability("editOrderReconViaNetwork", "edit Order Recon Via Network", assetClasses)
  val ViewOrderReconViaOwnerCapability: Capability = Capability("viewOrderReconViaOwner", "view Order Recon Via Owner", assetClasses)
  val EditOrderReconViaOwnerCapability: Capability = Capability("editOrderReconViaOwner", "edit Order Recon Via Owner", assetClasses)

  val ViewOrderReconViaNetwork: String = ViewOrderReconViaNetworkCapability.name
  val EditOrderReconViaNetwork: String = EditOrderReconViaNetworkCapability.name
  val ViewOrderReconViaOwner: String = ViewOrderReconViaOwnerCapability.name
  val EditOrderReconViaOwner: String = EditOrderReconViaOwnerCapability.name
  val ViewOrderReconViaNetworkType: String = ViewOrderReconViaNetworkTypeCapability.name
  val EditOrderReconViaNetworkType: String = EditOrderReconViaNetworkTypeCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewOrderReconViaNetworkTypeCapability, ViewOrderReconViaNetworkCapability, ViewOrderReconViaOwnerCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditOrderReconViaNetworkTypeCapability, EditOrderReconViaNetworkCapability, EditOrderReconViaOwnerCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewOrderReconViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
    EditOrderReconViaNetworkType -> AvailableKeyBuilder(buildNetworkTypeKeys),
    ViewOrderReconViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    EditOrderReconViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ViewOrderReconViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    EditOrderReconViaOwner -> AvailableKeyBuilder(buildGuidKeys),
  )
}
