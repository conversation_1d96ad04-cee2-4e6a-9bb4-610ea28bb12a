package com.simonmarkets.capabilities

trait Capabilities extends {
  val DomainName: String

  def toSet: Set[String] = toDetailedCapabilitySet.map(_.name)

  def toDetailedCapabilitySet: Set[Capability]
}

object Capabilities {
  val Admin = "admin"
  val AdminCapability: Capability = Capability(Admin, "Gives admin-level capability")
  val ReadOnlyAdmin = "readOnlyAdmin"
  val ReadOnlyAdminCapability: Capability = Capability(ReadOnlyAdmin, "Gives read-only admin-level capability")
  val DefaultCapability = "defaultCapability"
  val DetailedDefaultCapability: Capability = Capability(DefaultCapability, "Default capability")
}

object AssetClasses {
  val StructuredInvestmentsAssetClass: String = "StructuredInvestments"
  val AnnuitiesAssetClass: String = "Annuities"
  val InsuranceAssetClass: String = "Insurance"
  val AlternativesAssetClass: String = "Alternatives"
  val SMAsAssetClass: String = "SMAs"
  val DefinedOutcomeETFsAssetClass: String = "DefinedOutcomeETFs"
  val DigitalAssetClass: String = "Digital"
  val Platform: String = "Platform"
  val Spectrum: String = "Spectrum"
  val Architect: String = "Architect"
  val AIChat: String = "AIChat"
  val ReconEngine: String = "ReconEngine"
}
