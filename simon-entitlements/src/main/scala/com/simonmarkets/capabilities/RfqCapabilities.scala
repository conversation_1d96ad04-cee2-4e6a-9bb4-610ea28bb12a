package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.{NetworkType, PurviewedDomain, UserACL}
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements._

object RfqCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "Rfq"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val DeclineRfqViaOwner = "declineRfqViaOwner"
  val DeclineRfqViaNetwork = "declineRfqViaNetwork"
  val DeclineRfqViaNetworkIssuerPurview = "declineRfqViaNetworkIssuerPurview"

  val RequestRfqIdeaViaOwner = "requestRfqIdeaViaOwner"
  val RequestRfqIdeaViaNetwork = "requestRfqIdeaViaNetwork"
  val RequestRfqIdeaViaNetworkIssuerPurview = "requestRfqIdeaViaNetworkIssuerPurview"
  val SubmitRfqIdeaViaOwner = "submitRfqIdeaViaOwner"
  val SubmitRfqIdeaViaNetwork = "submitRfqIdeaViaNetwork"
  val SubmitRfqIdeaViaNetworkIssuerPurview = "submitRfqIdeaViaNetworkIssuerPurview"
  val ApproveRfqIdeaViaNetwork = "approveRfqIdeaViaNetwork"
  val ApproveRfqIdeaViaNetworkIssuerPurview = "approveRfqIdeaViaNetworkIssuerPurview"

  val RequestRfqViaOwner = "requestRfqViaOwner"
  val RequestRfqViaNetwork = "requestRfqViaNetwork"
  val RequestRfqViaNetworkIssuerPurview = "requestRfqViaNetworkIssuerPurview"

  val ReceiveRfqViaNetworkIssuerPurview = "receiveRfqViaNetworkIssuerPurview"
  val InProgressRfqViaNetworkIssuerPurview = "inProgressRfqViaNetworkIssuerPurview"
  val QuoteRfqViaNetworkIssuerPurview = "quoteRfqViaNetworkIssuerPurview"

  val AwardRfqViaOwner = "awardRfqViaOwner"
  val AwardRfqOnBehalfOfViaNetwork = "awardRfqOnBehalfOfViaNetwork"
  val AwardRfqOnBehalfOfViaNetworkIssuerPurview = "awardRfqOnBehalfOfViaNetworkIssuerPurview"

  val HomeOfficeApproveRfqViaNetwork = "homeOfficeApproveRfqViaNetwork"
  val HomeOfficeApproveRfqViaNetworkIssuerPurview = "homeOfficeApproveRfqViaNetworkIssuerPurview"

  val PendingIssuerApprovalRfqViaNetworkIssuerPurview = "pendingIssuerApprovalRfqViaNetworkIssuerPurview"
  val IssuerApproveRfqViaNetworkIssuerPurview = "issuerApproveRfqViaNetworkIssuerPurview"
  val IssuerAcknowledgeRfqViaNetworkIssuerPurview = "issuerAcknowledgeRfqViaNetworkIssuerPurview"
  val IssueRfqViaNetworkIssuerPurview = "issueRfqViaNetworkIssuerPurview"

  val UploadRftDocumentViaNetworkIssuerPurview = "uploadRftDocumentViaNetworkIssuerPurview"
  val ApproveRftDocumentViaNetwork = "approveRftDocumentViaNetwork"
  val ApproveRftDocumentViaNetworkIssuerPurview = "approveRftDocumentViaNetworkIssuerPurview"

  val ViewRfqViaOwnerCapability: Capability = Capability("viewRfqViaOwner", "View rfqs via owner", assetClasses)
  val ViewRfqViaUserCreatedCapability: Capability = Capability("viewRfqViaUserCreated", "View rfqs via user created", assetClasses)
  val ViewRfqViaNetworkCapability: Capability = Capability("viewRfqViaNetwork", "View rfqs via network", assetClasses)
  val ViewRfqViaNetworkIssuerPurviewCapability: Capability = Capability("viewRfqViaNetworkIssuerPurview", "View rfqs via network purview capabilities", assetClasses)

  val DeclineRfqViaOwnerCapability: Capability = Capability(DeclineRfqViaOwner, "Allow the rfq owner to decline their rfqs", assetClasses)
  val DeclineRfqViaUserCreatedCapability: Capability = Capability("declineRfqViaUserCreated", "Allow the rfq User Created to decline their rfqs", assetClasses)
  val DeclineRfqViaNetworkCapability: Capability = Capability(DeclineRfqViaNetwork, "Allow the home office to decline rfqs", assetClasses)
  val DeclineRfqViaNetworkIssuerPurviewCapability: Capability = Capability(DeclineRfqViaNetworkIssuerPurview, "Allow the wholesaler or parent home office to decline rfqs", assetClasses)
  val DeclineRfqViaNetworkTypeIssuerPurviewCapability: Capability = Capability("declineRfqViaNetworkTypeIssuerPurview", "Allow the issuer to decline rfqs", assetClasses)

  val RequestRfqIdeaViaOwnerCapability: Capability = Capability(RequestRfqIdeaViaOwner, "Allow the rfq owner to transition idea rfq to Requested", assetClasses)
  val RequestRfqIdeaViaNetworkCapability: Capability = Capability(RequestRfqIdeaViaNetwork, "Allow the rfq network to transition idea rfq to Requested", assetClasses)
  val RequestRfqIdeaViaNetworkIssuerPurviewCapability: Capability = Capability(RequestRfqIdeaViaNetworkIssuerPurview, "Allow the rfq issuer purview to transition idea rfq to Requested", assetClasses)
  val SubmitRfqIdeaViaOwnerCapability: Capability = Capability(SubmitRfqIdeaViaOwner, "Allow the rfq owner to submit idea rfq for home office approval", assetClasses)
  val SubmitRfqIdeaViaUserCreatedCapability: Capability = Capability("submitRfqIdeaViaUserCreated", "Allow the rfq User Created to submit idea rfq for home office approval", assetClasses)
  val SubmitRfqIdeaViaNetworkCapability: Capability = Capability(SubmitRfqIdeaViaNetwork, "Allow the rfq network to submit idea rfq for home office approval", assetClasses)
  val SubmitRfqIdeaViaNetworkIssuerPurviewCapability: Capability = Capability(SubmitRfqIdeaViaNetworkIssuerPurview, "Allow the rfq network issuer purview to submit idea rfq for home office approval", assetClasses)
  val ApproveRfqIdeaViaNetworkCapability: Capability = Capability(ApproveRfqIdeaViaNetwork, "Allow the rfq network to approve idea rfq for true rfq", assetClasses)
  val ApproveRfqIdeaViaNetworkIssuerPurviewCapability: Capability = Capability(ApproveRfqIdeaViaNetworkIssuerPurview, "Allow the rfq network to approve idea rfq for true rfq", assetClasses)

  val RequestRfqViaOwnerCapability: Capability = Capability(RequestRfqViaOwner, "Allow the rfq owner to transition rfq to Requested", assetClasses)
  val RequestRfqViaUserCreatedCapability: Capability = Capability("requestRfqViaUserCreated", "Allow the rfq user created to transition rfq to Requested", assetClasses)
  val RequestRfqViaNetworkCapability: Capability = Capability(RequestRfqViaNetwork, "Allow the home office to transition rfq to Requested", assetClasses)
  val RequestRfqViaNetworkIssuerPurviewCapability: Capability = Capability(RequestRfqViaNetworkIssuerPurview, "Allow the issuer to transition rfq to Requested", assetClasses)

  val ReceiveRfqViaNetworkIssuerPurviewCapability: Capability = Capability(ReceiveRfqViaNetworkIssuerPurview, "Allow the issuer to transition the rfq to Received", assetClasses)
  val InProgressRfqViaNetworkIssuerPurviewCapability: Capability = Capability(InProgressRfqViaNetworkIssuerPurview, "Allow the issuer to transition the rfq to InProgressAuto or InProgressManual", assetClasses)
  val QuoteRfqViaNetworkIssuerPurviewCapability: Capability = Capability(QuoteRfqViaNetworkIssuerPurview, "Allow the issuer to transition the rfq to Quoted", assetClasses)

  val HomeOfficeApproveRfqViaNetworkCapability: Capability = Capability(HomeOfficeApproveRfqViaNetwork, "Allow the home office to transition the rfq to HomeOfficeApproved", assetClasses)
  val HomeOfficeApproveRfqViaNetworkIssuerPurviewCapability: Capability = Capability(HomeOfficeApproveRfqViaNetworkIssuerPurview, "Allow the issuer to transition the rfq to HomeOfficeApproved", assetClasses)

  val PendingIssuerApprovalRfqViaNetworkIssuerPurviewCapability: Capability = Capability(PendingIssuerApprovalRfqViaNetworkIssuerPurview, "Allow the issuer to transition the rfq to PendingIssuerApproval", assetClasses)
  val IssuerApproveRfqViaNetworkIssuerPurviewCapability: Capability = Capability(IssuerApproveRfqViaNetworkIssuerPurview, "Allow the issuer to transition the rfq to IssuerApproved", assetClasses)
  val IssuerAcknowledgeRfqViaNetworkIssuerPurviewCapability: Capability = Capability(IssuerAcknowledgeRfqViaNetworkIssuerPurview, "Allow the issuer to transition the rfq to IssuerAcknowledged", assetClasses)
  val IssueRfqViaNetworkIssuerPurviewCapability: Capability = Capability(IssueRfqViaNetworkIssuerPurview, "Allow the issuer to transition the rfq to Issued", assetClasses)

  val IssueUploadedContractAsRftViaNetworkCapability: Capability = Capability("issueUploadedContractAsRftViaNetwork", "Allow home office to transition uploaded contract as RFT to Issued", assetClasses)

  val AwardRfqViaOwnerCapability: Capability = Capability(AwardRfqViaOwner, "Allow the owner to award rfq", assetClasses)
  val AwardRfqViaUserCreatedCapability: Capability = Capability("awardRfqViaUserCreated", "Allow the User Created to award rfq", assetClasses)
  val AwardRfqOnBehalfOfViaNetworkCapability: Capability = Capability(AwardRfqOnBehalfOfViaNetwork, "Allow the home office to submit rfq on behalf of the FA with state transition to Awarded", assetClasses)
  val AwardRfqOnBehalfOfViaNetworkIssuerPurviewCapability: Capability = Capability(AwardRfqOnBehalfOfViaNetworkIssuerPurview, "Allow the wholesaler and issuer to submit rfq on behalf of the FA with state transition to Awarded", assetClasses)

  val ViewRfqAuditNotesFieldViaNetworkCapability: Capability = Capability("viewRfqAuditNotesViaNetwork", "Allow home office to view Notes field under audit", assetClasses)
  val EditRfqAuditNotesFieldViaNetworkCapability: Capability = Capability("editRfqAuditNotesViaNetwork", "Allow home office to edit Notes field under audit", assetClasses)

  val UseRFQIssuerSelectionRestrictionsCapability: Capability = Capability("useRFQIssuerSelectionRestrictions", "", assetClasses)
  val CreateRfqTemplateForNetworkCapability: Capability = Capability("createRfqTemplateForNetwork", "Allow the user to create rfq template for the network", assetClasses)

  val UploadRftDocumentViaNetworkIssuerPurviewCapability: Capability = Capability(UploadRftDocumentViaNetworkIssuerPurview, "Allow issuer to upload RFT documents", assetClasses)
  val ApproveRftDocumentViaNetworkCapability: Capability = Capability(ApproveRftDocumentViaNetwork, "Allow home office in the submittedOnBehalfOf network to review RFT documents", assetClasses)
  val ApproveRftDocumentViaNetworkIssuerPurviewCapability: Capability = Capability(ApproveRftDocumentViaNetworkIssuerPurview, "Allow home office in the purview network to review RFT documents", assetClasses)

  val ViewTagsInternalApprovalCapability: Capability = Capability("viewTagsInternalApproval", "View internal approval tags assigned to RFQ capability", assetClasses)
  val EditTagsInternalApprovalCapability: Capability = Capability("editTagsInternalApproval", "Edit internal approval tags assigned to RFQ capability", assetClasses)

  val ViewRfqViaOwner: String = ViewRfqViaOwnerCapability.name
  val ViewRfqViaNetwork: String = ViewRfqViaNetworkCapability.name
  val ViewRfqViaNetworkIssuerPurview: String = ViewRfqViaNetworkIssuerPurviewCapability.name
  val ViewRfqAuditNotesFieldViaNetwork: String = ViewRfqAuditNotesFieldViaNetworkCapability.name
  val EditRfqAuditNotesFieldViaNetwork: String = EditRfqAuditNotesFieldViaNetworkCapability.name
  val UseRFQIssuerSelectionRestrictions: String = UseRFQIssuerSelectionRestrictionsCapability.name
  val CreateRfqTemplateForNetwork: String = CreateRfqTemplateForNetworkCapability.name

  val UseIssuerSelectionRestrictionsCapabilities: Set[String] = Set(UseRFQIssuerSelectionRestrictions)

  val CreateRfqTemplateForNetworkCapabilities: Set[String] = Set(CreateRfqTemplateForNetwork)

  override val DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewRfqViaNetworkCapability,
    ViewRfqViaOwnerCapability,
    ViewRfqViaUserCreatedCapability,
    ViewRfqViaNetworkIssuerPurviewCapability,
  )

  override val DetailedEditCapabilities: Set[Capability] = Set(
    AdminCapability
  )

  val DetailedSubmitCapabilities: Set[Capability] = Set(
    AdminCapability,
    AwardRfqViaOwnerCapability,
    AwardRfqViaUserCreatedCapability,
    AwardRfqOnBehalfOfViaNetworkCapability,
    AwardRfqOnBehalfOfViaNetworkIssuerPurviewCapability,
  )

  val DetailedRequestCapabilities: Set[Capability] = Set(
    AdminCapability,
    RequestRfqViaOwnerCapability,
    RequestRfqViaUserCreatedCapability,
    RequestRfqViaNetworkCapability,
    RequestRfqViaNetworkIssuerPurviewCapability
  )

  val DetailedHomeOfficeApproveCapabilities: Set[Capability] = Set(
    AdminCapability,
    HomeOfficeApproveRfqViaNetworkCapability,
    HomeOfficeApproveRfqViaNetworkIssuerPurviewCapability
  )

  val DetailedDeclineCapabilities: Set[Capability] = Set(
    AdminCapability,
    DeclineRfqViaOwnerCapability,
    DeclineRfqViaUserCreatedCapability,
    DeclineRfqViaNetworkCapability,
    DeclineRfqViaNetworkIssuerPurviewCapability,
    DeclineRfqViaNetworkTypeIssuerPurviewCapability
  )

  val DetailedRequestRfqIdeaCapabilities: Set[Capability] = Set(
    AdminCapability,
    RequestRfqIdeaViaOwnerCapability,
    RequestRfqIdeaViaNetworkCapability,
    RequestRfqIdeaViaNetworkIssuerPurviewCapability
  )

  val DetailedSubmitRfqIdeaCapabilities: Set[Capability] = Set(
    AdminCapability,
    SubmitRfqIdeaViaOwnerCapability,
    SubmitRfqIdeaViaUserCreatedCapability,
    SubmitRfqIdeaViaNetworkCapability,
    SubmitRfqIdeaViaNetworkIssuerPurviewCapability
  )

  val DetailedApproveRfqIdeaCapabilities: Set[Capability] = Set(
    AdminCapability,
    ApproveRfqIdeaViaNetworkCapability,
    ApproveRfqIdeaViaNetworkIssuerPurviewCapability
  )

  val DetailedUseIssuerSelectionRestrictionsCapabilities: Set[Capability] = Set(UseRFQIssuerSelectionRestrictionsCapability)
  val DetailedCreateRfqTemplateForNetworkCapabilities: Set[Capability] = Set(CreateRfqTemplateForNetworkCapability)

  val DetailedApproveRftDocumentCapabilities: Set[Capability] = Set(
    ApproveRftDocumentViaNetworkCapability,
    ApproveRftDocumentViaNetworkIssuerPurviewCapability
  )

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++
    DetailedEditCapabilities ++
    DetailedSubmitCapabilities ++
    DetailedUseIssuerSelectionRestrictionsCapabilities ++
    DetailedCreateRfqTemplateForNetworkCapabilities ++
    DetailedRequestCapabilities ++
    DetailedDeclineCapabilities ++
    DetailedRequestRfqIdeaCapabilities ++
    DetailedSubmitRfqIdeaCapabilities ++
    DetailedApproveRfqIdeaCapabilities ++
    DetailedHomeOfficeApproveCapabilities ++
    DetailedApproveRftDocumentCapabilities ++
    Set(ReceiveRfqViaNetworkIssuerPurviewCapability,
      InProgressRfqViaNetworkIssuerPurviewCapability,
      QuoteRfqViaNetworkIssuerPurviewCapability,
      PendingIssuerApprovalRfqViaNetworkIssuerPurviewCapability,
      IssuerAcknowledgeRfqViaNetworkIssuerPurviewCapability,
      IssuerApproveRfqViaNetworkIssuerPurviewCapability,
      IssueRfqViaNetworkIssuerPurviewCapability,
      IssueUploadedContractAsRftViaNetworkCapability,
      ViewRfqAuditNotesFieldViaNetworkCapability,
      EditRfqAuditNotesFieldViaNetworkCapability,
      UploadRftDocumentViaNetworkIssuerPurviewCapability,
      ViewTagsInternalApprovalCapability,
      EditTagsInternalApprovalCapability,
    )

  private def buildViewRfqViaNetworkIssuerPurviewKeys(capability: String, userACL: UserACL): Set[String] =
    buildNetworkIssuerPurviewedDomainKeys(capability, userACL, PurviewedDomain.ViewRfqs)

  private def buildEditRfqViaNetworkIssuerPurviewKeys(capability: String, userACL: UserACL): Set[String] =
    buildNetworkIssuerPurviewedDomainKeys(capability, userACL, PurviewedDomain.EditRfqs)

  private def buildDeclineRfqViaNetworkTypeIssuerPurviewKeys(capability: String, userACL: UserACL): Set[String] =
    if (userACL.networkTypes.exists(types => types.contains(NetworkType.Issuer))) {
      for {
        issuerPurview <- userACL.issuerPurviewIds.filter(issuerPurview =>
          issuerPurview.purviewedDomainsUpdated.exists(purviewedDomainsSet => purviewedDomainsSet.contains(PurviewedDomain.EditRfqs))
        )
        issuerSymbol <- issuerPurview.issuers
      } yield mkKey(capability, issuerPurview.network.toString, NetworkType.Issuer.productPrefix, issuerSymbol)
    } else {
      Set.empty[String]
    }

  private def buildAwardRfqViaNetworkIssuerPurviewKeys(capability: String, userACL: UserACL): Set[String] =
    buildNetworkIssuerPurviewedDomainKeys(capability, userACL, PurviewedDomain.AwardRfqs)

  private def buildSubmitIdeaRfqViaNetworkIssuerPurviewKeys(capability: String, userACL: UserACL): Set[String] =
    buildNetworkIssuerPurviewedDomainKeys(capability, userACL, PurviewedDomain.SubmitIdeaRfqs)

  private def buildApproveIdeaRfqViaNetworkIssuerPurviewKeys(capability: String, userACL: UserACL): Set[String] =
    buildNetworkIssuerPurviewedDomainKeys(capability, userACL, PurviewedDomain.ApproveIdeaRfqs)


  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewRfqViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ViewRfqViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    ViewRfqViaUserCreatedCapability.name -> AvailableKeyBuilder(buildGuidKeys),
    ViewRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildViewRfqViaNetworkIssuerPurviewKeys),

    AwardRfqViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    AwardRfqViaUserCreatedCapability.name -> AvailableKeyBuilder(buildGuidKeys),
    AwardRfqOnBehalfOfViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    AwardRfqOnBehalfOfViaNetworkIssuerPurview-> AvailableKeyBuilder(buildAwardRfqViaNetworkIssuerPurviewKeys),

    RequestRfqViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    RequestRfqViaUserCreatedCapability.name -> AvailableKeyBuilder(buildGuidKeys),
    RequestRfqViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    RequestRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    DeclineRfqViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    DeclineRfqViaUserCreatedCapability.name -> AvailableKeyBuilder(buildGuidKeys),
    DeclineRfqViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    DeclineRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    DeclineRfqViaNetworkTypeIssuerPurviewCapability.name -> AvailableKeyBuilder(buildDeclineRfqViaNetworkTypeIssuerPurviewKeys),

    RequestRfqIdeaViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    RequestRfqIdeaViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    RequestRfqIdeaViaNetworkIssuerPurview -> AvailableKeyBuilder(buildSubmitIdeaRfqViaNetworkIssuerPurviewKeys),
    SubmitRfqIdeaViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    SubmitRfqIdeaViaUserCreatedCapability.name -> AvailableKeyBuilder(buildGuidKeys),
    SubmitRfqIdeaViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    SubmitRfqIdeaViaNetworkIssuerPurview -> AvailableKeyBuilder(buildSubmitIdeaRfqViaNetworkIssuerPurviewKeys),
    ApproveRfqIdeaViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ApproveRfqIdeaViaNetworkIssuerPurview -> AvailableKeyBuilder(buildApproveIdeaRfqViaNetworkIssuerPurviewKeys),

    ReceiveRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    InProgressRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    QuoteRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    HomeOfficeApproveRfqViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    HomeOfficeApproveRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    PendingIssuerApprovalRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    IssuerAcknowledgeRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    IssuerApproveRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    IssueRfqViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),

    IssueUploadedContractAsRftViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),

    ViewRfqAuditNotesFieldViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    EditRfqAuditNotesFieldViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),

    UseRFQIssuerSelectionRestrictions -> AvailableKeyBuilder(undecoratedKeyBuilder),
    CreateRfqTemplateForNetwork -> AvailableKeyBuilder(undecoratedKeyBuilder),

    UploadRftDocumentViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),
    ApproveRftDocumentViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ApproveRftDocumentViaNetworkIssuerPurview -> AvailableKeyBuilder(buildEditRfqViaNetworkIssuerPurviewKeys),

    ViewTagsInternalApprovalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
    EditTagsInternalApprovalCapability.name -> AvailableKeyBuilder(undecoratedKeyBuilder),
  )
}
