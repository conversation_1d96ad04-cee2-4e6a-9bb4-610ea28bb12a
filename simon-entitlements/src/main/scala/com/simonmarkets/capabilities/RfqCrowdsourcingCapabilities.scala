package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildGuidKeys, buildNetworkKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities}

object RfqCrowdsourcingCapabilities extends Capabilities with AvailableAccessK<PERSON>sGenerator with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "RfqCrowdsourcing"

  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val ViewRfqCrowdsourcingViaOwner: String = "viewRfqCrowdsourcingViaOwner"
  val ViewRfqCrowdsourcingViaNetwork: String = "viewRfqCrowdsourcingViaNetwork"
  val EditRfqCrowdsourcingViaOwner: String = "editRfqCrowdsourcingViaOwner"
  val EditRfqCrowdsourcingViaNetwork: String = "editRfqCrowdsourcingViaNetwork"

  val ViewRfqCrowdsourcingViaOwnerCapability: Capability = Capability(ViewRfqCrowdsourcingViaOwner, "View rfq crowdsourcing via owner", assetClasses)
  val ViewRfqCrowdsourcingViaNetworkCapability: Capability = Capability(ViewRfqCrowdsourcingViaNetwork, "View rfq crowdsourcing via network", assetClasses)
  val EditRfqCrowdsourcingViaOwnerCapability: Capability = Capability(EditRfqCrowdsourcingViaOwner, "View rfq crowdsourcing via owner", assetClasses)
  val EditRfqCrowdsourcingViaNetworkCapability: Capability = Capability(EditRfqCrowdsourcingViaNetwork, "View rfq crowdsourcing via network", assetClasses)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewRfqCrowdsourcingViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    ViewRfqCrowdsourcingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    EditRfqCrowdsourcingViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    EditRfqCrowdsourcingViaNetwork -> AvailableKeyBuilder(buildNetworkKeys)
  )

  override def DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewRfqCrowdsourcingViaOwnerCapability,
    ViewRfqCrowdsourcingViaNetworkCapability
  )

  override def DetailedEditCapabilities: Set[Capability] = Set(
    AdminCapability,
    EditRfqCrowdsourcingViaOwnerCapability,
    EditRfqCrowdsourcingViaNetworkCapability
  )
}
