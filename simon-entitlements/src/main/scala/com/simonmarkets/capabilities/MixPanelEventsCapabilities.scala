package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.AssetClasses.Platform
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator._
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}

object MixPanelEventsCapabilities extends Capabilities {

  override val DomainName: String = "MixPanelEvents"

  val ViewMixPanelUserEventsViaUserCapability: Capability = Capability("viewMixpanelUserEventsViaUser", "Gives a user access to their mixpanel user event data", Some(Seq(Platform)))
  val ViewMixPanelUserEventsViaNetworkCapability: Capability = Capability("viewMixpanelUserEventsViaNetwork", "Gives a network access to its users' mixpanel user event data", Some(Seq(Platform)))
  val ViewMixPanelUserEventsViaPurviewCapability: Capability = Capability("viewMixpanelUserEventsViaPurview", "Gives a network access to its purview networks' mixpanel user event data", Some(Seq(Platform)))

  val ViewMixPanelUserEventsViaUser: String = ViewMixPanelUserEventsViaUserCapability.name
  val ViewMixPanelUserEventsViaNetwork: String = ViewMixPanelUserEventsViaNetworkCapability.name
  val ViewMixPanelUserEventsViaPurview: String = ViewMixPanelUserEventsViaPurviewCapability.name

  val ViewMixPanelUserEventsCapabilities =  Set(
    Admin,
    ViewMixPanelUserEventsViaUser,
    ViewMixPanelUserEventsViaNetwork,
    ViewMixPanelUserEventsViaPurview,
  )


  val availableAccessKeyGen: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewMixPanelUserEventsViaUser -> AvailableKeyBuilder(buildGuidKeys),
      ViewMixPanelUserEventsViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
      ViewMixPanelUserEventsViaPurview -> AvailableKeyBuilder(buildUserPurviewKeys)
    )
  }

  override def toDetailedCapabilitySet: Set[Capability] = Set(
    AdminCapability,
    ViewMixPanelUserEventsViaUserCapability,
    ViewMixPanelUserEventsViaNetworkCapability,
    ViewMixPanelUserEventsViaPurviewCapability
  )




}
