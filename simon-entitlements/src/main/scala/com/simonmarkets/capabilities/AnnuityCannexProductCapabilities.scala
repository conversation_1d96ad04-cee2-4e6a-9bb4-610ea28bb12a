package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.capabilities.utils.CapabilityDefinition
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, payoffEntitlementKeysForEdit, payoffEntitlementKeysForView}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities}

@CapabilityDefinition
object AnnuityCannexProductCapabilities extends Capabilities
  with HasDetailedViewCapabilities
  with HasDetailedEditCapabilities
  with AvailableAccessKeysGenerator {

  override val DomainName: String = "AnnuityCannexProduct"

  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.AnnuitiesAssetClass))

  val ViewCannexProductViaPayoffEntitlementCapability: Capability = Capability("viewCannexProductViaPayoffEntitlement", "", assetClasses)
  val EditCannexProductViaPayoffEntitlementCapability: Capability = Capability("editCannexProductViaPayoffEntitlement", "", assetClasses)

  val ViewCannexProductViaPayoffEntitlement = ViewCannexProductViaPayoffEntitlementCapability.name
  val EditCannexProductViaPayoffEntitlement = EditCannexProductViaPayoffEntitlementCapability.name

  override def DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewCannexProductViaPayoffEntitlementCapability)

  override def DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditCannexProductViaPayoffEntitlementCapability)

  override def toDetailedCapabilitySet: Set[Capability] =
    DetailedViewCapabilities ++
      DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewCannexProductViaPayoffEntitlement -> AvailableKeyBuilder(payoffEntitlementKeysForView),
    EditCannexProductViaPayoffEntitlement -> AvailableKeyBuilder(payoffEntitlementKeysForEdit),
  )
}
