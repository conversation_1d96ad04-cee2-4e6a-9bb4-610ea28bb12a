package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.AdminCapability
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkTypeKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder}

object LearnV2QuizCapabilities extends Capabilities with AvailableAccessKeysGenerator  {
  override val DomainName: String = "LearnQuizCapabilities"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewQuizCapabilitiesViaNetworkTypeCapability: Capability = Capability("viewQuizCapabilitiesViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditQuizCapabilitiesViaNetworkTypeCapability: Capability = Capability("editQuizCapabilitiesViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  val ViewQuizCapabilities: Set[String] = Set(Capabilities.Admin, ViewQuizCapabilitiesViaNetworkTypeCapability.name)
  val EditQuizCapabilities: Set[String] = Set(Capabilities.Admin, EditQuizCapabilitiesViaNetworkTypeCapability.name)

  val DetailedViewQuizCapabilities: Set[Capability] = Set(AdminCapability, ViewQuizCapabilitiesViaNetworkTypeCapability)
  val DetailedEditQuizCapabilities: Set[Capability] = Set(AdminCapability, EditQuizCapabilitiesViaNetworkTypeCapability)

  override val capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Capabilities.Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewQuizCapabilitiesViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
    EditQuizCapabilitiesViaNetworkTypeCapability.name -> AvailableKeyBuilder(buildNetworkTypeKeys),
  )

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewQuizCapabilities ++ DetailedEditQuizCapabilities
}
