package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability, ReadOnlyAdmin, ReadOnlyAdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildFANumberKeys, buildGuidKeys, buildLocationKeys, buildNetworkKeys, undecoratedKeyBuilder}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object PortfolioCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {

  override val DomainName: String = "Portfolio"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewPortfolioViaOwnerCapability: Capability = Capability("viewPortfolioViaOwner", "", assetClasses)
  val EditPortfolioViaOwnerCapability: Capability = Capability("editPortfolioViaOwner", "", assetClasses)
  val ViewSharedPortfolioViaFANumberCapability: Capability = Capability("viewSharedPortfolioViaFANumber", "View shared portfolio via fa number, typically generated for systematic integration", assetClasses)
  val EditSharedPortfolioViaFANumberCapability: Capability = Capability("editSharedPortfolioViaFANumber", "Edit shared portfolio via fa number, typically generated for systematic integration", assetClasses)
  val ViewSharedPortfolioViaNetworkCapability: Capability = Capability("viewSharedPortfolioViaNetwork", "View shared portfolio via network", assetClasses)
  val EditSharedPortfolioViaNetworkCapability: Capability = Capability("editSharedPortfolioViaNetwork", "Edit shared portfolio via network", assetClasses)
  val ViewSharedPortfolioViaLocationCapability: Capability = Capability("viewSharedPortfolioViaLocation", "View shared portfolio via location", assetClasses)
  val EditSharedPortfolioViaLocationCapability: Capability = Capability("editSharedPortfolioViaLocation", "Edit shared portfolio via location", assetClasses)
  val SharePortfolioViaOwnerCapability: Capability = Capability("sharePortfolioViaOwner", "Share portfolios you own", assetClasses)
  val ViewPortfolioViaPublicCapability: Capability = Capability("viewPortfolioViaPublic", "View the model portfolios available on the spectrum platform", assetClasses)
  val ViewAssetViaPublicCapability: Capability = Capability("viewAssetViaPublic", "View all Architect Assets", assetClasses)
  val ViewAltOfferingViaUserIdCapability: Capability = Capability("viewAltOfferingViaUserId", "View an altfund offering that is associated with your user id", assetClasses)
  val EditSharedPortfolioViaUserIdCapability: Capability = Capability("editSharedPortfolioViaUserId", "Edit a shared porfolio that is associated with your user id", assetClasses)
  val ViewSharedPortfolioViaUserIdCapability: Capability = Capability("viewSharedPortfolioViaUserId", "View a shared porfolio that is associated with your user id", assetClasses)
  val ViewCustomAssetViaNetworkCapability: Capability = Capability("viewCustomAssetViaNetwork", "View a custom asset that is associated with a user's network id", assetClasses)
  val ViewCustomAssetViaUserIdCapability: Capability = Capability("viewCustomAssetViaUserId", "View a custom asset that is associated with a user id", assetClasses)
  val EditCustomAssetViaUserIdCapability: Capability = Capability("editCustomAssetViaUserId", "Edit a custom asset that is associated with a user id", assetClasses)
  val EditCustomAssetViaNetworkCapability: Capability = Capability("editCustomAssetViaNetwork", "Edit a custom asset that is associated with a user's network id", assetClasses)
  val ViewSharedPortfolioInviteViaUserIdCapability: Capability = Capability("viewSharedPortfolioInviteViaUserId", "View restricted information about a shared portfolio before the invite is accepted", assetClasses)
  val ViewSharedCustomAssetInviteViaUserIdCapability: Capability = Capability("viewSharedCustomAssetInviteViaUserId", "View restricted information about a shared custom asset before the invite is accepted", assetClasses)
  val ViewPortfolioGroupViaOwnerCapability: Capability = Capability("viewPortfolioGroupViaOwner", "View Portfolio Groups you own", assetClasses)
  val EditPortfolioGroupViaOwnerCapability: Capability = Capability("editPortfolioGroupViaOwner", "Edit Portfolio Groups you own", assetClasses)
  val ViewSharedPortfolioInviteViaOwnerCapability: Capability = Capability("viewSharedPortfolioInviteViaOwner", "View a shared portfolio invite you own", assetClasses)
  val EditSharedPortfolioInviteViaOwnerCapability: Capability = Capability("editSharedPortfolioInviteViaOwner", "Edit a shared portfolio invite you own", assetClasses)
  val EditSharedPortfolioInviteViaUserIdCapability: Capability = Capability("editSharedPortfolioInviteViaUserId", "Edit a shared portfolio invite that is associated with a user id", assetClasses)
  val EditSharedCustomAssetInviteViaUserIdCapability: Capability = Capability("editSharedCustomAssetInviteViaUserId", "Edit a shared custom asset invite that is associated with a user id", assetClasses)


  val ViewPortfolioViaOwner: String = ViewPortfolioViaOwnerCapability.name
  val EditPortfolioViaOwner: String = EditPortfolioViaOwnerCapability.name
  val ViewSharedPortfolioViaFANumber: String = ViewSharedPortfolioViaFANumberCapability.name
  val EditSharedPortfolioViaFANumber: String = EditSharedPortfolioViaFANumberCapability.name
  val ViewSharedPortfolioViaNetwork: String = ViewSharedPortfolioViaNetworkCapability.name
  val EditSharedPortfolioViaNetwork: String = EditSharedPortfolioViaNetworkCapability.name
  val ViewSharedPortfolioViaLocation: String = ViewSharedPortfolioViaLocationCapability.name
  val EditSharedPortfolioViaLocation: String = EditSharedPortfolioViaLocationCapability.name
  val SharePortfolioViaOwner: String = SharePortfolioViaOwnerCapability.name
  val ViewPortfolioViaPublic: String = ViewPortfolioViaPublicCapability.name
  val ViewAssetViaPublic: String = ViewAssetViaPublicCapability.name
  val ViewAltOfferingViaUserId: String = ViewAltOfferingViaUserIdCapability.name
  val EditSharedPortfolioViaUserId: String = EditSharedPortfolioViaUserIdCapability.name
  val ViewSharedPortfolioViaUserId: String = ViewSharedPortfolioViaUserIdCapability.name
  val ViewCustomAssetViaUserId: String = ViewCustomAssetViaUserIdCapability.name
  val ViewCustomAssetViaNetwork: String = ViewCustomAssetViaNetworkCapability.name
  val EditCustomAssetViaNetwork: String = EditCustomAssetViaNetworkCapability.name
  val EditCustomAssetViaUserId: String = EditCustomAssetViaUserIdCapability.name
  val ViewSharedPortfolioInviteViaUserId: String = ViewSharedPortfolioInviteViaUserIdCapability.name
  val ViewSharedCustomAssetInviteViaUserId: String = ViewSharedCustomAssetInviteViaUserIdCapability.name
  val ViewPortfolioGroupViaOwner: String = ViewPortfolioGroupViaOwnerCapability.name
  val EditPortfolioGroupViaOwner: String = EditPortfolioGroupViaOwnerCapability.name
  val ViewSharedPortfolioInviteViaOwner: String = ViewSharedPortfolioInviteViaOwnerCapability.name
  val EditSharedPortfolioInviteViaOwner: String = EditSharedPortfolioInviteViaOwnerCapability.name
  val EditSharedPortfolioInviteViaUserId: String = EditSharedPortfolioInviteViaUserIdCapability.name
  val EditSharedCustomAssetInviteViaUserId: String = EditSharedCustomAssetInviteViaUserIdCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ReadOnlyAdminCapability,
    ViewPortfolioViaOwnerCapability, ViewPortfolioViaPublicCapability, ViewAltOfferingViaUserIdCapability,
    ViewSharedPortfolioViaUserIdCapability, ViewAssetViaPublicCapability, ViewCustomAssetViaUserIdCapability,
    ViewSharedPortfolioInviteViaUserIdCapability, ViewSharedCustomAssetInviteViaUserIdCapability,
    ViewPortfolioGroupViaOwnerCapability, ViewCustomAssetViaNetworkCapability, ViewSharedPortfolioViaFANumberCapability,
    ViewSharedPortfolioViaNetworkCapability, ViewSharedPortfolioViaLocationCapability, ViewSharedPortfolioInviteViaOwnerCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditPortfolioViaOwnerCapability,
    EditSharedPortfolioViaUserIdCapability, SharePortfolioViaOwnerCapability, EditCustomAssetViaUserIdCapability,
    EditPortfolioGroupViaOwnerCapability, EditCustomAssetViaNetworkCapability, EditSharedPortfolioViaFANumberCapability,
    EditSharedPortfolioViaNetworkCapability, EditSharedPortfolioViaLocationCapability, EditSharedPortfolioInviteViaOwnerCapability,
    EditSharedPortfolioInviteViaUserIdCapability, EditSharedCustomAssetInviteViaUserIdCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ReadOnlyAdmin -> AvailableKeyBuilder(buildAdminKeys),
    ViewPortfolioViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    EditPortfolioViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    ViewSharedPortfolioViaFANumber -> AvailableKeyBuilder(buildFANumberKeys),
    EditSharedPortfolioViaFANumber -> AvailableKeyBuilder(buildFANumberKeys),
    ViewSharedPortfolioViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    EditSharedPortfolioViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ViewSharedPortfolioViaLocation -> AvailableKeyBuilder(buildLocationKeys),
    EditSharedPortfolioViaLocation -> AvailableKeyBuilder(buildLocationKeys),
    SharePortfolioViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    ViewPortfolioViaPublic -> AvailableKeyBuilder(undecoratedKeyBuilder),
    ViewAssetViaPublic -> AvailableKeyBuilder(undecoratedKeyBuilder),
    ViewAltOfferingViaUserId -> AvailableKeyBuilder(buildGuidKeys),
    EditSharedPortfolioViaUserId -> AvailableKeyBuilder(buildGuidKeys),
    ViewSharedPortfolioViaUserId -> AvailableKeyBuilder(buildGuidKeys),
    ViewCustomAssetViaUserId -> AvailableKeyBuilder(buildGuidKeys),
    ViewCustomAssetViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    EditCustomAssetViaUserId -> AvailableKeyBuilder(buildGuidKeys),
    EditCustomAssetViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ViewSharedPortfolioInviteViaUserId -> AvailableKeyBuilder(buildGuidKeys),
    ViewSharedCustomAssetInviteViaUserId -> AvailableKeyBuilder(buildGuidKeys),
    ViewPortfolioGroupViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    EditPortfolioGroupViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    ViewSharedPortfolioInviteViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    EditSharedPortfolioInviteViaOwner -> AvailableKeyBuilder(buildGuidKeys),
    EditSharedPortfolioInviteViaUserId -> AvailableKeyBuilder(buildGuidKeys),
    EditSharedCustomAssetInviteViaUserId -> AvailableKeyBuilder(buildGuidKeys)
  )
}
