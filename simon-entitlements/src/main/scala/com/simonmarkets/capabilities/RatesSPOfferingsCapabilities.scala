package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.AdminCapability
import com.simonmarkets.capabilities.utils.CapabilityDefinition
import com.simonmarkets.entitlements.{HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

@CapabilityDefinition
object RatesSPOfferingsCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities{
  override val DomainName = "RatesSPOfferings"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  // viaNetworkTypeAndIssuerSymbol
  val ViewRatesSPOfferingViaNetworkTypeAndIssuerSymbolCapability: Capability = Capability("viewRatesSPOfferingViaNetworkTypeAndIssuerSymbol", "Allow issuer to view rates offerings from api responses.", assetClasses)
  val EditRatesSPOfferingViaNetworkTypeAndIssuerSymbolCapability: Capability = Capability("editRatesSPOfferingViaNetworkTypeAndIssuerSymbol", "Allow issuer to edit rates offerings.", assetClasses)
  val TradeRatesSPOfferingViaNetworkTypeAndIssuerSymbolCapability: Capability = Capability("tradeRatesSPOfferingViaNetworkTypeAndIssuerSymbol", "Allow issuer to see rates offerings in marketplace.", assetClasses)

  // viaNetwork
  val ViewRatesSPOfferingViaNetworkCapability: Capability = Capability("viewRatesSPOfferingViaNetwork", "Allow user to view rates offerings from api responses.", assetClasses)
  val ViewApprovedRatesSPOfferingViaNetworkCapability: Capability = Capability("viewApprovedRatesSPOfferingViaNetwork", "Allow user to view approved rates offerings from api responses.", assetClasses)
  val TradeRatesSPOfferingViaNetworkCapability: Capability = Capability("tradeRatesSPOfferingViaNetwork", "Allow user to see rates offerings in marketplace.", assetClasses)
  val ApproveRatesSPOfferingViaNetworkCapability: Capability = Capability("approveRatesSPOfferingViaNetwork", "Allow user to see and approve rates offerings in marketplace.", assetClasses)
  val RejectRatesSPOfferingViaNetworkCapability: Capability = Capability("rejectRatesSPOfferingViaNetwork", "Allow user to reject rates offerings.", assetClasses)

  val SubmitOrderRatesSPOfferingCapability: Capability = Capability("submitOrderRatesSPOffering", "Part of buy button check for Rates. Other parts are trade capabilities and payoff entitlements. Equivalent to tradeOfferingViaTradePayoffEntitlement for equity sp.", assetClasses)

  val ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability: Capability = Capability("viewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlements", "Allow non-issuer user to view non Reg S past offering", assetClasses)

  // viaNetworkType
  val ViewRatesSPOfferingViaNetworkTypeCapability: Capability = Capability("viewRatesSPOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val EditRatesSPOfferingViaNetworkTypeCapability: Capability = Capability("editRatesSPOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val TradeRatesSPOfferingViaNetworkTypeCapability: Capability = Capability("tradeRatesSPOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val ApproveRatesSPOfferingViaNetworkTypeCapability: Capability = Capability("approveRatesSPOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)
  val RejectRatesSPOfferingViaNetworkTypeCapability: Capability = Capability("rejectRatesSPOfferingViaNetworkType", "Gives access based on User’s NetworkType, should generally only be Admin NetworkType", assetClasses)

  // viaNetworkIssuerPurview
  val ViewRatesSPOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("viewRatesSPOfferingViaNetworkIssuerPurview", "Allow user to view rates offerings via purview", assetClasses)
  val TradeRatesSPOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("tradeRatesSPOfferingViaNetworkIssuerPurview", "Allow user to trade rates offerings via purview", assetClasses)
  val ApproveRatesSPOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("approveRatesSPOfferingViaNetworkIssuerPurview", "Allow user to approve rates offerings via purview", assetClasses)
  val RejectRatesSPOfferingViaNetworkIssuerPurviewCapability: Capability = Capability("rejectRatesSPOfferingViaNetworkIssuerPurview", "Allow user to reject rates offerings via purview", assetClasses)

  // viaUserGuid
  val ViewRatesSPOfferingViaUserGuidCapability: Capability = Capability("viewRatesSPOfferingViaUserGuid", "Allow user to view rates offerings via offeredTo userId", assetClasses)
  val TradeRatesSPOfferingViaUserGuidCapability: Capability = Capability("tradeRatesSPOfferingViaUserGuid", "Allow user to trade rates offerings via offeredTo userId", assetClasses)

  // viaFaNumber
  val ViewRatesSPOfferingViaFaNumberCapability: Capability = Capability("viewRatesSPOfferingViaFaNumber", "Allow user to view rates offerings via offeredTo fa number", assetClasses)
  val TradeRatesSPOfferingViaFaNumberCapability: Capability = Capability("tradeRatesSPOfferingViaFaNumber", "Allow user to trade rates offerings via offeredTo fa number", assetClasses)

  // viaLocation
  val ViewRatesSPOfferingViaLocationCapability: Capability = Capability("viewRatesSPOfferingViaLocation", "Allow user to view rates offerings via offeredTo location", assetClasses)
  val TradeRatesSPOfferingViaLocationCapability: Capability = Capability("tradeRatesSPOfferingViaLocation", "Allow user to trade rates offerings via offeredTo location", assetClasses)

  // open/close rates offerings
  val OpenCloseRatesSPOfferingViaNetworkCapability: Capability = Capability("openCloseRatesSPOfferingViaNetwork", "Allow user to open/close rates offerings via network", assetClasses)
  val OpenCloseRatesSPOfferingViaPurviewCapability: Capability = Capability("openCloseRatesSPOfferingViaPurview", "Allow user to open/close rates offerings via purview", assetClasses)

  override val DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewRatesSPOfferingViaNetworkTypeCapability,
    ViewRatesSPOfferingViaNetworkTypeAndIssuerSymbolCapability,
    ViewRatesSPOfferingViaNetworkCapability,
    ViewRatesSPOfferingViaNetworkIssuerPurviewCapability,
    ViewRatesSPOfferingViaUserGuidCapability,
    ViewRatesSPOfferingViaFaNumberCapability,
    ViewRatesSPOfferingViaLocationCapability,
    ViewApprovedRatesSPOfferingViaNetworkCapability,
    ViewNonRegSRatesSPPastOfferingsViaViewPayoffEntitlementsCapability,
  )
  override val DetailedEditCapabilities: Set[Capability] = Set(
    AdminCapability,
    EditRatesSPOfferingViaNetworkTypeCapability,
    EditRatesSPOfferingViaNetworkTypeAndIssuerSymbolCapability
  )
  val DetailedApproveCapabilities: Set[Capability] = Set(
    AdminCapability,
    ApproveRatesSPOfferingViaNetworkCapability,
    ApproveRatesSPOfferingViaNetworkIssuerPurviewCapability,
    ApproveRatesSPOfferingViaNetworkTypeCapability
  )
  val DetailedTradeCapabilities: Set[Capability] = Set(
    AdminCapability,
    TradeRatesSPOfferingViaNetworkCapability,
    TradeRatesSPOfferingViaNetworkIssuerPurviewCapability,
    TradeRatesSPOfferingViaNetworkTypeAndIssuerSymbolCapability,
    TradeRatesSPOfferingViaUserGuidCapability,
    TradeRatesSPOfferingViaFaNumberCapability,
    TradeRatesSPOfferingViaLocationCapability,
    TradeRatesSPOfferingViaNetworkTypeCapability
  )

  val DetailedRejectCapabilities: Set[Capability] = Set(
    AdminCapability,
    RejectRatesSPOfferingViaNetworkCapability,
    RejectRatesSPOfferingViaNetworkIssuerPurviewCapability,
    RejectRatesSPOfferingViaNetworkTypeCapability
  )

  val ApproveCapabilities: Set[String] = DetailedApproveCapabilities.map(_.name)
  val TradeCapabilities: Set[String] = DetailedTradeCapabilities.map(_.name)
  val RejectCapabilities: Set[String] = DetailedRejectCapabilities.map(_.name)

  override def toDetailedCapabilitySet: Set[Capability] =
    DetailedViewCapabilities ++
    DetailedEditCapabilities ++
    DetailedApproveCapabilities ++
    DetailedTradeCapabilities ++
    DetailedRejectCapabilities ++
    Set(SubmitOrderRatesSPOfferingCapability, OpenCloseRatesSPOfferingViaNetworkCapability, OpenCloseRatesSPOfferingViaPurviewCapability)
}
