package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.AssetClasses._
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.buildAdminKeys
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

import scala.collection.mutable

object CrmIntegrationCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "CrmIntegrations"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Platform))

  val ViewCrmIntegrationViaOwnerCapability: Capability = Capability("viewCrmIntegrationViaOwner", "", assetClasses)
  val EditCrmIntegrationViaOwnerCapability: Capability = Capability("editCrmIntegrationViaOwner", "", assetClasses)

  val ViewCrmIntegrationViaOwner: String = ViewCrmIntegrationViaOwnerCapability.name
  val EditCrmIntegrationViaOwner: String = EditCrmIntegrationViaOwnerCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(ViewCrmIntegrationViaOwnerCapability, AdminCapability)
  override val DetailedEditCapabilities: Set[Capability] = Set(EditCrmIntegrationViaOwnerCapability, AdminCapability)

  val availableAccessKeysGenerator: AvailableAccessKeysGenerator = new AvailableAccessKeysGenerator {
    override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
      Admin -> AvailableKeyBuilder(buildAdminKeys),
      ViewCrmIntegrationViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner),
      EditCrmIntegrationViaOwner -> AvailableKeyBuilder(getAvailableKeysViaOwner)
    )
  }

  private def getAvailableKeysViaOwner(capability: String, userACL: UserACL): Set[String] = Set(s"$capability:${userACL.userId}")
  
  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities
}
