package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.AdminCapability

// 1. Add your capabilities here in the format below for them to get picked up by the Onboarding Tool.
// 2. If you remove or rename any capabilities make sure the old names are removed from the network config in mongo. Use
// _285_remove_unused_capabilities.scala as a template.

object CustomRoleCapabilities extends Capabilities {
  override val DomainName = "CustomRole"

  val capabilitiesByDomain: Map[String, Capabilities] = Map(
    HoldingsCapabilities.DomainName -> HoldingsCapabilities,
    AccountsCapabilities.DomainName -> AccountsCapabilities,
    OrderAccountsCapabilities.DomainName -> OrderAccountsCapabilities,
    HouseholdsCapabilities.DomainName -> HouseholdsCapabilities,
    OrdersCapabilities.DomainName -> OrdersCapabilities,
    OrdersBulkUploadCapabilities.DomainName -> OrdersBulkUploadCapabilities,
    ClientOrderCapabilities.DomainName -> ClientOrderCapabilities,
    OrderReconDefaultMappingsCapabilities.DomainName -> OrderReconDefaultMappingsCapabilities,
    OrderReconRunCapabilities.DomainName -> OrderReconRunCapabilities,
    OrderReconCapabilities.DomainName -> OrderReconCapabilities,
    UsersCapabilities.DomainName -> UsersCapabilities,
    SimonUICapabilities.DomainName -> SimonUICapabilities,
    NetworksCapabilities.DomainName -> NetworksCapabilities,
    OfferingsV1Capabilities.DomainName -> OfferingsV1Capabilities,
    DocumentsCapabilities.DomainName -> DocumentsCapabilities,
    SalesFeeCapabilities.DomainName -> SalesFeeCapabilities,
    RfqCapabilities.DomainName -> RfqCapabilities,
    RfqConfigsCapabilities.DomainName -> RfqConfigsCapabilities,
    RfqTagsCapabilities.DomainName -> RfqTagsCapabilities,
    RfqCrowdsourcingCapabilities.DomainName -> RfqCrowdsourcingCapabilities,
    RfqTemplatesCapabilities.DomainName -> RfqTemplatesCapabilities,
    BuilderCapabilities.DomainName -> BuilderCapabilities,
    ProductClassificationCapabilities.DomainName -> ProductClassificationCapabilities,
    SpectrumLearningCenterCapabilities.DomainName -> SpectrumLearningCenterCapabilities,
    UserViewsCapabilities.DomainName -> UserViewsCapabilities,
    SharedViewsCapabilities.DomainName -> SharedViewsCapabilities,
    SPOfferingsCapabilities.DomainName -> SPOfferingsCapabilities,
    RatesSPOfferingsCapabilities.DomainName -> RatesSPOfferingsCapabilities,
    NonRegisteredAnnuityOfferingsCapabilities.DomainName -> NonRegisteredAnnuityOfferingsCapabilities,
    StructuredETFOfferingsCapabilities.DomainName -> StructuredETFOfferingsCapabilities,
    RegisteredAnnuityOfferingsCapabilities.DomainName -> RegisteredAnnuityOfferingsCapabilities,
    LearnContentActivityCapabilities.DomainName -> LearnContentActivityCapabilities,
    LearnContentCapabilities.DomainName -> LearnContentCapabilities,
    LearnV2AttestationCapabilities.DomainName -> LearnV2AttestationCapabilities,
    LearnV2QuestionCapabilities.DomainName -> LearnV2QuestionCapabilities,
    LearnV2QuizCapabilities.DomainName -> LearnV2QuizCapabilities,
    LearnV2TrackActivityCapabilities.DomainName -> LearnV2TrackActivityCapabilities,
    LearnV2TracksCapabilities.DomainName -> LearnV2TracksCapabilities,
    LearnV2TrackCustomizationsCapabilities.DomainName -> LearnV2TrackCustomizationsCapabilities,
    LearnV2ContentCustomizationsCapabilities.DomainName -> LearnV2ContentCustomizationsCapabilities,
    UserIdSSNHashMapCapability.DomainName -> UserIdSSNHashMapCapability,
    NotificationsCapabilities.DomainName -> NotificationsCapabilities,
    SmaCapabilities.DomainName -> SmaCapabilities,
    ContractsCapabilities.DomainName -> ContractsCapabilities,
    StructuredETFCapabilities.DomainName -> StructuredETFCapabilities,
    SPOrdersCloseoutCapabilities.DomainName -> SPOrdersCloseoutCapabilities,
    ProductNomenclatureCapabilities.DomainName -> ProductNomenclatureCapabilities,
    AnnuityHoldingsCapabilities.DomainName -> AnnuityHoldingsCapabilities,
    AnnuityIllustrationHistoryCapabilities.DomainName -> AnnuityIllustrationHistoryCapabilities,
    AnnuityMarketplaceCapabilities.DomainName -> AnnuityMarketplaceCapabilities,
    EndpointScopes.DomainName -> EndpointScopes,
    BatchStagingCapabilities.DomainName -> BatchStagingCapabilities,
    LevelsCapabilities.DomainName -> LevelsCapabilities,
    SkillsCapabilities.DomainName -> SkillsCapabilities,
    UserLevelsCapabilities.DomainName -> UserLevelsCapabilities,
    UserSkillActivitiesCapabilities.DomainName -> UserSkillActivitiesCapabilities,
    PortfolioCapabilities.DomainName -> PortfolioCapabilities,
    ClientProfileCapabilities.DomainName -> ClientProfileCapabilities,
    CrmContactCapabilities.DomainName -> CrmContactCapabilities,
    CrmIntegrationCapabilities.DomainName -> CrmIntegrationCapabilities,
    CrmAuthorizationCapabilities.DomainName -> CrmAuthorizationCapabilities,
    AltOfferingCapabilities.DomainName -> AltOfferingCapabilities,
    AltsProductCapabilities.DomainName -> AltsProductCapabilities,
    SmaSamplePortfolioCapabilities.DomainName -> SmaSamplePortfolioCapabilities,
    SmaCompositeDataCapabilities.DomainName -> SmaCompositeDataCapabilities,
    SmaStrategyCapabilities.DomainName -> SmaStrategyCapabilities,
    SmaAccountsCapabilities.DomainName -> SmaAccountsCapabilities,
    SmaAccountMaintenanceTasksCapabilities.DomainName -> SmaAccountMaintenanceTasksCapabilities,
    MixPanelEventsCapabilities.DomainName -> MixPanelEventsCapabilities,
    ForYouCardsCapabilities.DomainName -> ForYouCardsCapabilities,
    SuitabilityScoreCapabilities.DomainName -> SuitabilityScoreCapabilities,
    CryptoAccountsCapabilities.DomainName -> CryptoAccountsCapabilities,
    CryptoOrdersCapabilities.DomainName -> CryptoOrdersCapabilities,
    CryptoOfferingsCapabilities.DomainName -> CryptoOfferingsCapabilities,
    CryptoPersonsCapabilities.DomainName -> CryptoPersonsCapabilities,
    AltHoldingsCapabilities.DomainName -> AltHoldingsCapabilities,
    AltHouseholdsCapabilities.DomainName -> AltHouseholdsCapabilities,
    AltAccountsCapabilities.DomainName -> AltAccountsCapabilities,
    AnnuityActivityCapabilities.DomainName -> AnnuityActivityCapabilities,
    UIAssetsCapabilities.DomainName -> UIAssetsCapabilities,
    EodValuationsBulkUploadCapabilities.DomainName -> EodValuationsBulkUploadCapabilities,
    DeveloperHubCapabilities.DomainName -> DeveloperHubCapabilities,
    MultiFactorAuthenticationCapabilities.DomainName -> MultiFactorAuthenticationCapabilities,
    FeatureCapabilities.DomainName -> FeatureCapabilities,
    TVShowCapabilities.DomainName -> TVShowCapabilities,
    ArchitectCapabilities.DomainName -> ArchitectCapabilities,
    ArchitectDisclosureCapabilities.DomainName -> ArchitectDisclosureCapabilities,
    WlpFirmConfigurationCapabilities.DomainName -> WlpFirmConfigurationCapabilities,
    IOIConfigsCapabilities.DomainName -> IOIConfigsCapabilities,
    PaidFeaturesCapabilities.DomainName -> PaidFeaturesCapabilities,
    OptimizationAssetClassViaNetworkCapabilities.DomainName -> OptimizationAssetClassViaNetworkCapabilities,
    InvestmentObjectiveCapabilities.DomainName -> InvestmentObjectiveCapabilities,
    NetworkCMAProviderViaNetworkCapabilities.DomainName -> NetworkCMAProviderViaNetworkCapabilities,
    UafClientProfileCapabilities.DomainName -> UafClientProfileCapabilities,
    SleeveCapabilities.DomainName -> SleeveCapabilities,
    AnnuityNoteCapabilities.DomainName -> AnnuityNoteCapabilities,
    ReconEngineCapabilities.DomainName -> ReconEngineCapabilities,
    AIChatCapabilities.DomainName -> AIChatCapabilities,
    LifecycleEventsCapabilities.DomainName -> LifecycleEventsCapabilities,
    UserImpersonationCapabilities.DomainName -> UserImpersonationCapabilities,
    WholesalerExchangeContractsCapabilities.DomainName -> WholesalerExchangeContractsCapabilities,
    WholesalerExchangeOrdersCapabilities.DomainName -> WholesalerExchangeOrdersCapabilities,
    RegBiHistoricalAnalysisCapabilities.DomainName -> RegBiHistoricalAnalysisCapabilities,
    UnifiedHoldingsCapabilities.DomainName -> UnifiedHoldingsCapabilities,
    UnifiedOwnersCapabilities.DomainName -> UnifiedOwnersCapabilities,
    SuitabilityRuleTreeCapabilitites.DomainName -> SuitabilityRuleTreeCapabilitites,
    PlatformDocsCapabilities.DomainName -> PlatformDocsCapabilities,
  )

  override def toDetailedCapabilitySet: Set[Capability] = Set(AdminCapability) ++ capabilitiesByDomain.values.flatMap(_.toDetailedCapabilitySet.toSeq).toSet
}