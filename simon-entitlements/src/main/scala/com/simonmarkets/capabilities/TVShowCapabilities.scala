package com.simonmarkets.capabilities

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities}

object TVShowCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasDetailedViewCapabilities with HasDetailedEditCapabilities {
  override val DomainName: String = "TVShow"
  val assetClass = Some(Seq(AssetClasses.Platform))

  val ViewTvShowViaNetworkCapability: Capability = Capability("viewTVShowViaNetwork", "Allow one to view a tv show watched if one's Network ID Matches the tv show watched's Network ID", assetClass)
  val EditTvShowViaNetworkCapability: Capability = Capability("editTvShowViaNetwork", "Allow one to edit a tv show watched if one's Network ID Matches the tv show watched's Network ID", assetClass)
  val EditTvShowViaEmailCapability: Capability = Capability("editTvShowViaEmail", "Allow one to edit a tv show watched if one's email matches", assetClass)


  override def DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ViewTvShowViaNetworkCapability)
  override def DetailedEditCapabilities: Set[Capability] = Set(AdminCapability, EditTvShowViaNetworkCapability, EditTvShowViaEmailCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewTvShowViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
    EditTvShowViaNetworkCapability.name -> AvailableKeyBuilder(buildNetworkKeys),
    EditTvShowViaEmailCapability.name -> AvailableKeyBuilder(buildEmailKey)
  )

  def buildEmailKey(capability: String, user: UserACL): Set[String] = {
    Set(s"$capability:${user.email}")
  }

}
