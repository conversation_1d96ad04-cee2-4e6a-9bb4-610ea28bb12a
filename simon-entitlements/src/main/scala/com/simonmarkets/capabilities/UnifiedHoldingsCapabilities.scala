package com.simonmarkets.capabilities

import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedViewCapabilities, HasViewCapabilities, HasEditCapabilities}

object UnifiedHoldingsCapabilities extends Capabilities with AvailableAccess<PERSON>eysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities {
  override val DomainName: String = "UnifiedPortfolioHoldings"

  val ViewUnifiedHoldingViaNetworkCapability: Capability = Capability(s"viewUnifiedHoldingViaNetwork", "Allows one to view a Holding if one's Network Id matches the Holding's Network Id")

  val ViewUnifiedHoldingViaNetwork: String = ViewUnifiedHoldingViaNetworkCapability.name

  override val ViewCapabilities: Set[String] = Set(ViewUnifiedHoldingViaNetwork)
  override val DetailedViewCapabilities: Set[Capability] = Set(ViewUnifiedHoldingViaNetworkCapability)
  override val EditCapabilities: Set[String] = Set.empty

  override def toSet: Set[String] = ViewCapabilities

  def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    ViewUnifiedHoldingViaNetwork -> availableKeyBuilder.add(_.networkId),
  )
}
