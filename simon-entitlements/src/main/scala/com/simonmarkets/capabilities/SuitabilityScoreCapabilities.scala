package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.capabilities.SmaCapabilities.{EditSmaViaNetwork, ViewSmaViaNetwork}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities, HasEditCapabilities, HasViewCapabilities}

object SuitabilityScoreCapabilities extends Capabilities with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities
  with AvailableAccess<PERSON>eysGenerator {

  override val DomainName: String = "SuitabilityScore"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.StructuredInvestmentsAssetClass))

  val EditOverrideSuitabilityScoreViaNetworkCapability = Capability("EditOverrideSuitabilityScoreViaNetwork", "Allows one to edit override score if netowrkId matches", assetClasses)
  val ViewSuitabilityScoreViaNetworkCapability = Capability("ViewSuitabilityScoreViaNetwork", "Allows one to view suitability score if netowrkId matches", assetClasses)

  val ViewSuitabilityScoreViaNetwork = ViewSuitabilityScoreViaNetworkCapability.name
  val EditOverrideSuitabilityScoreViaNetwork = EditOverrideSuitabilityScoreViaNetworkCapability.name

  override val ViewCapabilities: Set[String] = Set(
    Admin,
    ViewSuitabilityScoreViaNetwork
  )
  override val EditCapabilities: Set[String] = Set(
    Admin,
    EditOverrideSuitabilityScoreViaNetwork
  )

  val DeleteCapabilities: Set[String] = Set(Admin)

  override val DetailedViewCapabilities: Set[Capability] = Set(
    AdminCapability,
    ViewSuitabilityScoreViaNetworkCapability
  )
  override val DetailedEditCapabilities: Set[Capability] = Set(
    AdminCapability,
    EditOverrideSuitabilityScoreViaNetworkCapability
  )

  val DetailedDeleteCapabilities: Set[Capability] = Set(AdminCapability)

  override def toSet: Set[String] = Set(Admin) ++ ViewCapabilities ++ EditCapabilities ++ DeleteCapabilities
  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities ++ DetailedDeleteCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewSuitabilityScoreViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    EditOverrideSuitabilityScoreViaNetwork -> AvailableKeyBuilder(buildNetworkKeys)

  )
}
