package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability, ReadOnlyAdmin, ReadOnlyAdminCapability}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedViewCapabilities, HasViewCapabilities}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildGuidKeys, buildNetworkKeys, buildLocationKeys}

object SleeveCapabilities extends Capabilities with AvailableAccess<PERSON>eysGenerator with HasViewCapabilities with HasDetailedViewCapabilities {
  override val DomainName: String = "Sleeve"

  val ViewSleeveViaNetworkCapability: Capability = Capability("viewSleeveViaNetwork", "Grants access to an Asset Sleeve at the Network Level")
  val ViewSleeveViaLocationCapability: Capability = Capability("viewSleeveViaLocation", "Grants access to an Asset Sleeve at the Network+Location Level")
  val ViewSleeveViaUserCapability: Capability = Capability("viewSleeveViaUser", "Grants access to an Asset Sleeve at the User Level")

  val ViewSleeveViaNetwork: String = ViewSleeveViaNetworkCapability.name
  val ViewSleeveViaLocation: String = ViewSleeveViaLocationCapability.name
  val ViewSleeveViaUser: String = ViewSleeveViaUserCapability.name

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities

  override val DetailedViewCapabilities: Set[Capability] = Set(AdminCapability, ReadOnlyAdminCapability,
    ViewSleeveViaNetworkCapability, ViewSleeveViaLocationCapability, ViewSleeveViaUserCapability)

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ReadOnlyAdmin -> AvailableKeyBuilder(buildAdminKeys),
    ViewSleeveViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ViewSleeveViaLocation -> AvailableKeyBuilder(buildLocationKeys),
    ViewSleeveViaUser -> AvailableKeyBuilder(buildGuidKeys)
  )
}
