package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.utils.CapabilityDefinition
import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.capabilities.utils.CapabilityDefinitionImpl.macroCapability
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkKeys}
import com.simonmarkets.entitlements.{AvailableAccessKeysGenerator, AvailableKeyBuilder, HasDetailedEditCapabilities, HasDetailedViewCapabilities}

@CapabilityDefinition
object SuitabilityRuleTreeCapabilitites extends
  Capabilities
  with AvailableAccessKeysGenerator
  with HasDetailedViewCapabilities
  with HasDetailedEditCapabilities
{

  override val DomainName: String = "SuitabilityRuleTree"

  val ViewSuitabilityRuleTreeViaNetwork = macroCapability
  val ViewSuitabilityRuleTreeViaNetworkCapability = Capability(
    ViewSuitabilityRuleTreeViaNetwork,
    "Allows the user to view a suitability rule tree if their network matches the network of that rule tree"
  )

  val EditSuitabilityRuleTreeViaNetwork = macroCapability
  val EditSuitabilityRuleTreeViaNetworkCapability = Capability(
    EditSuitabilityRuleTreeViaNetwork,
    "Allows the user to edit a suitability rule tree if their network matches the network of that rule tree"
  )

  override def DetailedEditCapabilities: Set[Capability] = Set[Capability](
    AdminCapability,
    ViewSuitabilityRuleTreeViaNetworkCapability
  )

  override def DetailedViewCapabilities: Set[Capability] = Set[Capability](
    AdminCapability,
    EditSuitabilityRuleTreeViaNetworkCapability
  )

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewSuitabilityRuleTreeViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    EditSuitabilityRuleTreeViaNetwork -> AvailableKeyBuilder(buildNetworkKeys)
  )

}
