package com.simonmarkets.capabilities

import com.simonmarkets.capabilities.Capabilities.{Admin, AdminCapability}
import com.simonmarkets.entitlements.AvailableAccessKeysGenerator.{buildAdminKeys, buildNetworkKeys}
import com.simonmarkets.entitlements._

object InvestmentObjectiveCapabilities extends Capabilities with AvailableAccessKeysGenerator with HasViewCapabilities with HasDetailedViewCapabilities with HasEditCapabilities with HasDetailedEditCapabilities {

  override val DomainName: String = "InvestmentObjective"
  val assetClasses: Option[Seq[String]] = Option(Seq(AssetClasses.Architect))

  val ViewInvestmentObjectiveViaNetworkCapability: Capability = Capability("viewInvestmentObjectiveViaNetwork", "Allows a network to view investment objectives", assetClasses)
  val ViewUAFInvestmentObjectiveViaNetworkCapability: Capability = Capability("viewUAFInvestmentObjectiveViaNetwork", "Allows a network to view the UAF investment objectives", assetClasses)

  val ViewInvestmentObjectiveViaNetwork: String = ViewInvestmentObjectiveViaNetworkCapability.name
  val ViewUAFInvestmentObjectiveViaNetwork: String = ViewUAFInvestmentObjectiveViaNetworkCapability.name

  override val DetailedViewCapabilities: Set[Capability] = Set(
    ViewInvestmentObjectiveViaNetworkCapability,
    ViewUAFInvestmentObjectiveViaNetworkCapability,
  )
  override val DetailedEditCapabilities = Set(AdminCapability)

  override def toDetailedCapabilitySet: Set[Capability] = DetailedViewCapabilities ++ DetailedEditCapabilities

  override def capabilityToAvailableKeyBuilders: Map[String, AvailableKeyBuilder] = Map(
    Admin -> AvailableKeyBuilder(buildAdminKeys),
    ViewInvestmentObjectiveViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
    ViewUAFInvestmentObjectiveViaNetwork -> AvailableKeyBuilder(buildNetworkKeys),
  )
}
