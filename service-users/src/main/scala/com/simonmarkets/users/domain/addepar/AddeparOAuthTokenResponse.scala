package com.simonmarkets.users.domain.addepar

import com.simonmarkets.users.domain.{OAuthTokenResponse, TokenIssuer}

case class AddeparOAuthTokenResponse(
    accessToken: Option[String],
    refreshToken: Option[String],
    tokenType: Option[String],
    expiresIn: Int,
    tokenIssuer: Option[TokenIssuer],
    addeparFirm: Option[String],
    addeparSubdomain: Option[String]
) extends OAuthTokenResponse
