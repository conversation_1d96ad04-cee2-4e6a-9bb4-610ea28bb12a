package com.simonmarkets.users

import akka.http.scaladsl.server.Directives
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import com.simonmarkets.users.api.routes._
import com.simonmarkets.users.config.AppConfiguration
import com.simonmarkets.users.config.AppConfiguration._
import com.simonmarkets.users.di.ServiceLocator
import io.simon.openapi.generator.OpenApiGenerator
import pureconfig.generic.auto._

object UsersAggregateApp extends RestEasyModule[AppConfiguration] with App {
  /** Returns the service name, for example, "FlawlessGems". Used for logging purposes.
   * The "service-" prefix is appended automatically. */
  override def serviceName = "UsersAggregateApp"

  /** Returns the root path to all of the service endpoints.
   * For example, "/simon/api/v1/lorem" */
  override def servicePath = "simon/api/v2/users"

  override def init(environment: Environment): Unit = {
    val serviceLocator = new ServiceLocator(config)

    val userRoutes = UserServiceRoutes(
      serviceLocator.userService,
      serviceLocator.authDirective,
      config.enhanceAccessTokenConfig.inlineHookAuth,
      config.kafkaConfig,
      serviceLocator.userEventPublisherEnv,
      serviceLocator.userNetworkMappingClient,
    ).routes

    val masterUserRoutes = MasterUserRoutes(
      serviceLocator.masterUserService,
      serviceLocator.authDirective
    ).routes

    val accessTokenRoutes = AccessTokenRoutes(
      serviceLocator.accessTokenService,
      serviceLocator.authDirective,
      config.enhanceAccessTokenConfig.inlineHookAuth
    ).routes

    val userImpersonationRoutes = UserImpersonationRoutes(
      serviceLocator.userImpersonationService,
      serviceLocator.userImpersonationApproversService,
      serviceLocator.authDirective
    ).routes

    val passportRoutes = PassportRoutes(
      serviceLocator.passportService,
      serviceLocator.authDirective
    ).routes

    environment.addRoutes(
      Directives.handleExceptions(RouteUtils.customExceptionHandler)(
        Directives.concat(
          passportRoutes,
          userImpersonationRoutes,
          userRoutes,
          masterUserRoutes,
          accessTokenRoutes,
        )
      )
    )
  }

  OpenApiGenerator.generateOpenApiDocumentation()
  RestEasy.start()
}