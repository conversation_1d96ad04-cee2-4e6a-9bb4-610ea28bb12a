package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.logging.TraceId
import com.simonmarkets.users.domain.{OAuthTokenRequest, OAuthTokenResponse}

import scala.concurrent.Future

/**
 * Manages OAuth token lifecycle for third-party integrations.
 * Handles the exchange of authorization codes for access tokens and
 * provides retrieval of stored tokens for authorized users.
 *
 * Supports standard OAuth 2.0 authorization code flow for secure
 * token management and persistence.
 */
trait OAuthTokenService {

  /**
   * Exchanges authorization code for access token and persists to storage.
   *
   * @param req OAuth token request containing authorization code
   * @return Future indicating success/failure of token exchange and storage
   */
  def exchangeAndStoreToken(req: OAuthTokenRequest)(implicit traceId: TraceId): Future[Boolean]

  /**
   * Retrieves stored OAuth access token for the current user.
   *
   * @param user Current user's access control information
   * @return Future containing the OAuth access token response or 404 if not found
   */
  def getStoredToken(implicit traceId: TraceId, user: UserACL): Future[OAuthTokenResponse]
}
