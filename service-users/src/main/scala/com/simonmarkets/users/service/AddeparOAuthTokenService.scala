package com.simonmarkets.users.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.logging.TraceId
import com.simonmarkets.users.domain.OAuthTokenRequest
import com.simonmarkets.users.domain.addepar.AddeparOAuthTokenResponse

import scala.concurrent.Future

case class AddeparOAuthTokenService()(implicit traceId: TraceId, user: UserACL) extends OAuthTokenService {
  /**
   * Exchanges authorization code for access token and persists to storage.
   *
   * @param req OAuth token request containing authorization code
   * @return Future indicating success/failure of token exchange and storage
   */
  override def exchangeAndStoreToken(req: OAuthTokenRequest)(implicit traceId: TraceId): Future[Boolean] = ???

  /**
   * Retrieves stored OAuth access token for the current user.
   *
   * @param user Current user's access control information
   * @return Future containing the OAuth access token response or 404 if not found
   */
  override def getStoredToken(implicit traceId: TraceId, user: UserACL): Future[AddeparOAuthTokenResponse] = ???
}
