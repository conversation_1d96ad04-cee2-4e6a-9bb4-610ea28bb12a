package com.simonmarkets.users.config

import com.simonmarkets.api.users.config.CacheConfig
import com.simonmarkets.dynamodb.config.DynamoDbConfiguration
import com.simonmarkets.eventbridge.config.EventBridgeConfig
import com.simonmarkets.http.HttpClientConfig
import com.simonmarkets.kafka.config.ConsumerConfig
import com.simonmarkets.mongodb.config.MongoClientConfig
import com.simonmarkets.okta.config.OktaConfiguration
import com.simonmarkets.resteasy.framework.RunMode
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration.SystemRoutesConfig
import com.simonmarkets.resteasy.framework.config.authn.AuthenticationConfiguration
import com.simonmarkets.resteasy.framework.info.InfoConfig
import com.simonmarkets.users.common.{LandingPage, LoginMode}
import io.simon.encryption.v3.service.IcnEncryptionService.IcnEncryptionConfig
import pureconfig.{ConfigFieldMapping, ConfigReader, NamingConvention}
import simon.Id.NetworkId
import pureconfig.generic.semiauto._

import scala.concurrent.duration.{Duration, FiniteDuration}
import scala.util.matching.Regex

case class AppConfiguration(
    authentication: Option[AuthenticationConfiguration],
    mongoDB: MongoConfiguration,
    runMode: RunMode,
    serviceConfig: UserServiceConfig,
    oktaConfig: OktaConfiguration,
    aclRepositoryConfig: DynamoDbConfiguration,
    systemRoutes: Option[SystemRoutesConfig],
    oktaSyncConfig: OktaSyncConfig,
    enhanceAccessTokenConfig: EnhanceAccessTokenConfig,
    oktaSyncEventBus: EventBridgeConfig,
    httpClientConfig: HttpClientConfig,
    simonBasePath: String,
    aclCacheConfig: CacheConfig,
    systemUserId: String,
    impersonationExclusions: Set[NetworkId],
    info: Option[InfoConfig],
    domainConfig: List[DomainConfig],
    kafkaConfig: KafkaConfig,
    mappingClientConfig: MappingClientConfig,
    userSyncSystemUserId: String,
    passport: PassportConfig,
    encryption: IcnEncryptionConfig,
    systemUserNetworkLimitationIgnoreList: Set[NetworkId]
  ) extends RestEasyAppConfiguration

case class DbConfig(
    collection: String,
    database: String
)

case class MongoConfiguration(
    users: DbConfig,
    usersSnapshots: DbConfig,
    usersImpersonation: DbConfig,
    usersImpersonationApprovers: DbConfig,
    networks: DbConfig,
    networksSnapshots: DbConfig,
    masterUser: DbConfig,
    tasks: DbConfig,
    inputs: DbConfig,
    outputs: DbConfig,
    externalIdTypes: DbConfig,
    passport: DbConfig,
    transientState: DbConfig,
    client: MongoClientConfig
)

case class UserServiceConfig(
    externalTargets: Set[String],
    paging: PagingConfig,
    parallelism: Int = 1,
    multiNetworksExcluded: Set[NetworkId],
    internalDomains: Set[String]
)

case class PagingConfig(
    defaultLimit: Int
)

case class InlineHookConfig(
    headerName: String,
    headerValue: String,
    timeout: Duration
)

case class EnhanceAccessTokenConfig(
    systemAdminId: String,
    pilotRoleKey: String,
    inlineHookAuth: InlineHookConfig,
    undefinedSubClaim: String,
    networkTokenLifetimes: List[TokenLifeTime],
    appExpiryConfig: Map[String, FiniteDuration]
)

case class TokenLifeTime(networkId: NetworkId, expirationTime: FiniteDuration)

case class DomainConfig(domain: String, landingPage: LandingPage, loginModes: List[LoginMode], defaultLoginMode: LoginMode) {
  private val matchDomainAndSubDomainPattern: String = "([a-z0-9]+[.])*"
  private val matchDomainAndSubDomainRegex: Regex    = s"$matchDomainAndSubDomainPattern$domain".r

  def isDomainMatch(host: String) = matchDomainAndSubDomainRegex.findFirstMatchIn(host).isDefined

}

case class KafkaConfig(
    producer: KafkaProducerConfig,
    consumer: ConsumerConfig,
    connectionUrl: String,
    auth: Option[KafkaAuthConfig],
    isUserSyncEnabled: Boolean,
)

case class KafkaProducerConfig(
    topic: String,
    producerGroupId: Option[String] = None,
    source: Option[String] = None,
)

case class KafkaAuthConfig(
    apiKey: String,
    secret: String,
    securityProtocol: String
)

case class MappingClientConfig(
    basePath: String,
    apiKey: String,
    httpClientConfig: HttpClientConfig,
    host: Option[String] = None,
    cacheSettings: Option[CacheSettings] = None,
)

case class CacheSettings(
    maxEntries: Option[Long],
    ttl: Option[Duration],
)

case class PassportConfig(
    validityWindow: FiniteDuration,
    codeLength: Int,
    cc: Option[String],
    from: String
)

object AppConfiguration {
  implicit val networkIdConfigReader: ConfigReader[NetworkId] = ConfigReader[String].map(NetworkId.apply)
  implicit val loginModeReader: ConfigReader[LoginMode]       = deriveEnumerationReader[LoginMode](ConfigFieldMapping(AsIsNamingConvention, AsIsNamingConvention))
  implicit val landingPageReader: ConfigReader[LandingPage]   = deriveEnumerationReader[LandingPage](ConfigFieldMapping(AsIsNamingConvention, AsIsNamingConvention))

  object AsIsNamingConvention extends NamingConvention {
    override def toTokens(s: String): Seq[String] = Seq(s)

    override def fromTokens(l: Seq[String]): String = l.mkString("")
  }

}
