include classpath("service-users-common.conf")

simon-base-path = "https://origin-dc1.api.qa.simonmarkets.com/simon/api"
system-user-id = "0oaevl54gplnFvyx70h7"

user-sync-system-user-id = "0oa1pqy4c2zavA5Sb0h8"

dynamo-db-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
      proxy-host = "internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

http-client-config {
  auth: {
    type = OAuth
    client-id = "0oaevl54gplnFvyx70h7"
    client-secret-file = ""
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type = {
      type = BearerToken
    }
    client-secret = "sm:applicationconfig-oktaclientsecret"
  }
  proxy {
    port: 3128
    address: "internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com"
  }
}

#TODO: add http client config for mapping service

acl-cache-config {
  enabled = true
  config {
    service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
    signin-region = "us-east-1"
  }
}

acl-repository-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

okta-config {
  client-config {
    okta-org-url = "https://auth.int.simonmarkets.com"
    okta-auth-token = "sm:applicationconfig-okta-api-token"
    cache-ttl = 30.seconds
    proxy {
      port: 3128
      host: "internal-qa-squid-elb-1135562419.us-east-1.elb.amazonaws.com"
    }
  }

  service-config {
    create {
      activate = true,
      setPassword = true,
      expirePassword = false
    }

    profile {
      network-name = "network"
      skip-secondary-emails = ["@simonmarkets.com"]
      secondary-email = "<EMAIL>"
    }
  }

  enabled = true
}

okta-sync-config {
  service-entitlements = ["admin"]
  cache-ttl = 15 minutes
  multi-factor-config {
    sms-group-id = "00g1kisw957yzLOEJ0h8"
    totp-group-id = "00g1kit12koGDognL0h8"
    email-group-id = "00g1kiszwnitLMIsG0h8"
    security-question-group-id = "00g1kiszgccQX8oY00h8"
    google-auth-group-id = "00g1kiszvdukp3hNr0h8"
    voice-call-group-id = "00g1kit0r35z23Kxt0h8"
    okta-verify-group-id = "00g1kisv2rhSljbkN0h8"
    sms-voice-call-group-id = "00g1lp0bcnr6JRf8Y0h8"
    enabled = true
  }
}

mongo-trigger-config {
  database-collections = [
    {
      database = "pipg"
      collection = "users"
    }
  ]
}

enhance-access-token-config {
  app-expiry-config {
    "0oa1ggsd0jae9ZSRT0h8" = 60.minutes
    "0oa1lmw4iq2gmYcIk0h8" = 60.minutes
    "0oa1n4xz7wwbdIJwA0h8" = 14.hours
    "0oa1era5g3hppXRCs0h8" = 14.hours
  }
  system-admin-id = "0oaevl54gplnFvyx70h7"
  pilot-role-key = "pilot"
  inline-hook-auth {
    header-name = "X-Simon-Qa-Token"
    header-value = "sm:gitlab_internal/kong-qa-enhance-access-token-inline-hook-secret"
    timeout = 15.seconds
  }
  undefined-sub-claim: "undefined"
  network-token-lifetimes: [
    {
      network-id = "31695b58-22c1-4573-925d-a67ecd1e4638"
      expiration-time = 50400.seconds
    },
    {
      network-id = "RBC"
      expiration-time = 12.hours
    },
    {
      network-id = "61c34a8d-dfef-4da4-8f97-349835240197"
      expiration-time = 12.hours
    }
  ]
}

encryption {
  endpoint = "https://origin-dc1.api.qa.simonmarkets.com/kms/api/v1",
  environment = "staging",
  simon-base-url = "https://origin-dc1.api.qa.simonmarkets.com",
  hashing-key-json = "sm:applicationkey-hashing"
}
