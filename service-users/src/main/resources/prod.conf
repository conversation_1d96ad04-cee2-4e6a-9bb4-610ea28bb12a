include classpath("service-users-common.conf")

simon-base-path = "https://origin-dc1.api.simonmarkets.com/simon/api"
system-user-id = "c388df581a28404a94bbc9885c1650fd"

user-sync-system-user-id = "0oan09s4u2z340ahp2p7"

dynamo-db-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader" # cookie / header / bearer
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
      proxy-host = "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }
  user-info {
    source {
      type = "header"
      name = "x-userinfo"
    }
  }
}

http-client-config {
  auth: {
    type = OAuth
    client-id = "0oa2h4re4ym0Fh3s82p7"
    client-secret-file = ""
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type = {
      type = BearerToken
    }
    client-secret = "sm:applicationconfig-oktaclientsecret"
    proxy {
      port: 3128
      address: "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
    }
  }
  proxy {
    port: 3128
    address: "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
  }
}

#TODO: add http client config for mapping service

acl-cache-config {
  enabled = true
  config {
    service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
    signin-region = "us-east-1"
  }
}

service-config {
  multi-networks-excluded = [
    "SIMON Admin"
    "a819e947-3b66-4b6d-bceb-25e3561e89d7", //Index Training Demo Network
    "1f742928-2c27-41da-9ab6-ba0979c64394", //Internal Training Network 1
    "feb10d98-0840-4143-812d-c02052973d6b", //Internal Training Network 2
    "34a36871-3a10-4185-ab5b-01dfe67cb8ef", //SIMON Training - Internal
    "76af6c1c-4346-4dff-8e61-fcc580161ee0", //SIMON Training Network - Internal 2
    "0d220496-38df-4290-bded-2502e5fe67fb", //Training Network
  ]
}

acl-repository-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

okta-config {
  client-config {
    okta-org-url = "https://auth.simonmarkets.com"
    okta-auth-token = "sm:applicationconfig-okta-api-token"
    cache-ttl = 30.seconds
    proxy {
      port: 3128
      host: "internal-prod-squid-elb-744301762.us-east-1.elb.amazonaws.com"
    }
  }

  service-config {
    create {
      activate = true,
      setPassword = true,
      expirePassword = false
    }

    profile {
      network-name = "network"
      skip-secondary-emails = ["@simonmarkets.com"]
      secondary-email = "<EMAIL>"
    }
  }

  enabled = true
}

okta-sync-config {
  service-entitlements = ["admin"]
  cache-ttl = 15 minutes
  multi-factor-config {
    sms-group-id = "00gmjfghjvMThSKoZ2p7"
    totp-group-id = "00gmjfdlwxORet77I2p7"
    email-group-id = "00gmjfdbqyikuhiFR2p7"
    security-question-group-id = "00gmjff6rnzcs8evG2p7"
    google-auth-group-id = "00gmjfh35pVBZZHV52p7"
    voice-call-group-id = "00gmjfbojk3CU4gSn2p7"
    okta-verify-group-id = "00gmjfe4xsG3mSP3L2p7"
    sms-voice-call-group-id = "00gmjfaqnxy7hK9ew2p7"
    enabled = true
  }
}

mongo-trigger-config {
  database-collections = [
    {
      database = "pipg"
      collection = "users"
    }
  ]
}

enhance-access-token-config {
  app-expiry-config {
    "0oaldg4lgoo3ArQAl2p7" = 60.minutes
    "0oanznr5gp7u9XZoq2p7" = 60.minutes
    "0oaopxl5a88DsiOQO2p7" = 14.hours
  }
  system-admin-id = "0oa2h4re4ym0Fh3s82p7"
  pilot-role-key = "pilot"
  inline-hook-auth {
    header-name = "X-Simon-Prod-Token"
    header-value = "sm:gitlab_internal/kong-prod-enhance-access-token-inline-hook-secret"
    timeout = 15.seconds
  }
  undefined-sub-claim: "undefined"
  network-token-lifetimes: [
    {
      network-id = "c9aa0114-db92-4616-87c0-a4c2fe2a9977"
      expiration-time = 50400.seconds
    },
    {
      network-id = "9bafb090-f453-4a04-a842-fd678a5051fd"
      expiration-time = 50400.seconds
    },
    {
      network-id = "03150e94-13ed-4738-a281-298014f660c7"
      expiration-time = 50400.seconds
    },
    {
      network-id = "15de6305-b527-4b3f-aba6-eb28c2561957"
      expiration-time = 50400.seconds
    },
    {
      network-id = "852edb37-2f7f-42fa-a159-d37f6081ca1c"
      expiration-time = 50400.seconds
    },
    {
      network-id = "Axio Financial"
      expiration-time = 50400.seconds
    },
    {
      network-id = "RBC Capital"
      expiration-time = 12.hours
    }
  ]
}

encryption {
  endpoint = "https://origin-dc1.api.simonmarkets.com/kms/api/v1",
  environment = "production",
  simon-base-url = "https://origin-dc1.api.simonmarkets.com",
  hashing-key-json = "sm:applicationkey-hashing"
}
