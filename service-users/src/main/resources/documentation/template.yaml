openapi: 3.0.0
info:
  title: User Service V2
  version: 1.0.0
x-kong-service-defaults:
  read_timeout: 300000
tags:
  - name: service-users
    description: User Service
paths:
  /simon/api/v2/users/uptime:
    get:
      x-disabled-plugins:
        - openId
        - request-validator
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - users
      summary: Retrieve service uptime
      responses:
        200:
          description: Success
  /simon/api/v2/users/info:
    get:
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - users
      summary: Retrieve service info
      responses:
        200:
          description: Success
  /simon/api/v2/users/healthcheck:
    get:
      x-disabled-plugins:
        - openId
        - request-validator
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - users
      summary: Retrieve service healthcheck
      responses:
        200:
          description: Success
  /simon/api/v2/users:
    x-kong-route-defaults:
      regex_priority: 999
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - users
      summary: Retrieve all or filter users
      parameters:
        - name: network
          in: query
          required: false
          description: "NetworkId (Optional)"
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
        - name: omsId
          in: query
          required: false
          description: "OMS id (Optional), e.g. TWD"
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.ArbitraryMessage}
        - name: distributorId
          in: query
          required: false
          description: "Distributor id (Optional), e.g. IdRaymond James"
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.ArbitraryMessage}
        - name: email
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.EmailAddress}
        - name: npn
          in: query
          required: false
          description: National Producer Number of user
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.AlphaNumeric}
        - name: maskedId
          in: query
          required: false
          description: "Masked id of a user. (Optional)"
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UUID}
        - name: target
          in: query
          required: false
          description: "Target which the maskedId belongs to (Optional). If the target parameter is set, the maskedId parameter has to be set too."
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.StringWithWhiteSpace128Length}
        - name: wlpId
          in: query
          required: false
          description: "whitelabel partner id"
          schema:
            type: string
        - name: firmId
          in: query
          required: false
          schema:
            type: string
        - name: location
          in: query
          required: false
          schema:
            type: array
            maxItems: 100
            items:
              type: string
        - name: id
          in: query
          required: false
          schema:
            type: array
            maxItems: 100
            items:
              type: string
        - name: limit
          in: query
          required: false
          schema:
            $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.Limit}
        - name: next
          in: query
          required: false
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.UserViewPageDefinition}
    post:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - developer
      tags:
        - users
      summary: Create new User
      parameters:
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.common.api.request.UpsertUserRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.UserView}
  /simon/api/v2/users/bulk-upsert:
    x-kong-route-defaults:
      regex_priority: 999
    post:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - users
      summary: Create or Update batch of new users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.BatchUserInsertRequest}
      responses:
        200:
          description: Successful or partially successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.BatchUserUpsertResponse}
  /simon/api/v2/users/locations:
    post:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - users
      summary: Assign Locations to the users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.UserLocationsBatch}
      responses:
        200:
          description: Report with errors per user
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.PartialUpdateBatchResponse}
  /simon/api/v2/users/fa-numbers:
    post:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - users
      summary: Assign FA numbers to the users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.UserFANumbersBatch}
      responses:
        200:
          description: Report with errors per user
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.PartialUpdateBatchResponse}
  /simon/api/v2/users/licenses:
    post:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - users
      summary: Assign licenses to the users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.LicenseUpdateRequestBatch}
      responses:
        200:
          description: Report with errors per user
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.PartialUpdateBatchResponse}
  /simon/api/v2/users/external-ids:
    post:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - users
      summary: Update users external ids field
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: ${com.simonmarkets.users.api.request.ExternalIdsUpdateRequest}
      responses:
        200:
          description: Report with errors per user
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.PartialUpdateBatchResponse}
  /simon/api/v2/users/email:
    put:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - fa
      tags:
        - users
      summary: Updates a user's email address
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.EmailUpdate}
      responses:
        200:
          description: Report with errors per user
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.PartialUpdateBatchResponse}
  /simon/api/v2/users/account:
    put:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - fa
      tags:
        - users
      summary: Updates a user's account-in-context
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.AccountUpdate}
      responses:
        200:
          description: Report with errors per user
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.PartialUpdateBatchResponse}
  /simon/api/v2/users/cusips:
    post:
      x-scopes:
        - admin
        - simon-system-user
      tags:
        - users
      summary: Updates a user's cusips
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: ${com.simonmarkets.users.api.request.CusipsUpdateRequest}
      responses:
        200:
          description: Report with errors per user
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.PartialUpdateBatchResponse}
  /simon/api/v2/users/{id}:
    x-kong-route-defaults:
      regex_priority: -10
    get:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - users
      summary: Retrieve specified User
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.UserView}
    head:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - users
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
    put:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
      tags:
        - users
      summary: Update User
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.common.api.request.UpsertUserRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.UserView}
        400:
          description: "The request contains bad syntax or cannot be fulfilled."
        403:
          description: "User does not have proper permissions."
  /simon/api/v2/users/{id}/internal:
    x-kong-route-defaults:
      regex_priority: -9
    get:
      x-scopes:
        - admin
      tags:
        - users
      summary: Retrieve internal view of specified User
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.User}
  /simon/api/v2/users/{id}/reset-password:
    x-scopes: [ admin ]
    post:
      tags:
        - users
      summary: Reset user's password and send welcome email
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.UserView}
  /simon/api/v2/users/{id}/reset-mfa:
    x-scopes: [ admin ]
    post:
      tags:
        - users
      summary: Reset user's mfa factors
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.ResetMfaRequest}
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.UserView}

  /simon/api/v2/users/{id}/secrets:
    get:
      x-scopes:
        - admin
        - internal
        - developer
      tags:
        - users
      summary: Retrieve secrets metadata for System Users
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.okta.client.api.application.GetOAuthSecretResponse}
    post:
      x-scopes:
        - admin
        - internal
        - developer
      tags:
        - users
      summary: Create secret for System User
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        201:
          description: Secret Created Successfully
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.okta.client.api.application.CreateOAuthSecretResponse}
  /simon/api/v2/users/{id}/secrets/{secretId}/activate:
    post:
      x-scopes:
        - admin
        - internal
        - developer
      tags:
        - users
      summary: Activate a Client Secret for System User
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Secret Activated Successfully
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.okta.client.api.application.GetOAuthSecretResponse}
  /simon/api/v2/users/{id}/secrets/{secretId}/deactivate:
    post:
      x-scopes:
        - admin
        - internal
        - developer
      tags:
        - users
      summary: Deactivate a Client Secret for System User
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Secret Deactivated Successfully
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.okta.client.api.application.GetOAuthSecretResponse}
  /simon/api/v2/users/{id}/activate:
    x-scopes: [ admin ]
    post:
      tags:
        - users
      summary: Activates a user in Okta
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.ActivateUserRequest}
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.UserView}
        404:
          description: User not exists
  /simon/api/v2/users/{id}/deactivate:
    x-scopes:
      - admin
      - simon-system-user
      - fa-manager
    post:
      tags:
        - users
      summary: Deactivate user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.UserView}
  /simon/api/v2/users/okta-sync:
    x-scopes:
      - admin
    post:
      tags:
        - users
      summary: sync users from mongo to okta
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.common.api.request.OktaSyncRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.OktaSyncResponse}

  /simon/api/v2/users/impersonate/{id}:
    get:
      x-scopes:
        - admin
        - internal
        - fa-manager
        - fa
        - system-system-user
      summary: Get a user impersonation
      parameters:
        - name: id
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UUID}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.domain.UserImpersonation}
      tags:
        - user-impersonation
  /simon/api/v2/users/impersonate:
    post:
      x-scopes:
        - admin
        - internal
        - fa-manager
        - fa
        - system-system-user
      summary: Insert a User Impersonation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.InsertUserImpersonationRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.domain.UserImpersonation}
      tags:
        - user-impersonation
  /simon/api/v2/users/impersonate/approve:
    post:
      x-scopes:
        - admin
        - internal
        - fa-manager
        - fa
        - system-system-user
      summary: Approve a User Impersonation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.ApproveUserImpersonationRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.domain.UserImpersonation}
      tags:
        - user-impersonation
  /simon/api/v2/users/impersonate/complete:
    post:
      x-scopes:
        - admin
        - internal
        - fa-manager
        - fa
        - system-system-user
      summary: Complete a User Impersonation
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.domain.UserImpersonation}
      tags:
        - user-impersonation
  /simon/api/v2/users/impersonate/force-complete:
    post:
      x-scopes:
        - admin
        - internal
      summary: Admin or ReadOnlyAdmin completes the User Impersonation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.ForceCompleteImpersonationRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.domain.UserImpersonation}
      tags:
        - user-impersonation
  /simon/api/v2/users/impersonate/status:
    get:
      x-scopes:
        - admin
        - internal
        - fa-manager
        - fa
        - system-system-user
      summary: Get UserImpersonation status
      parameters:
        - name: impersonatedUserId
          in: query
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.UserId}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.domain.ImpersonationStatusResponse}
      tags:
        - user-impersonation
  /simon/api/v2/users/impersonate/approvers/{networkId}:
    get:
      x-scopes:
        - admin
      summary: Retrieve one UserImpersonationApprovers by networkId
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.NetworkName}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.domain.UserImpersonationApprovers}
      tags:
        - user-impersonation
  /simon/api/v2/users/impersonate/approvers:
    put:
      x-scopes:
        - admin
      summary: Insert a new UserImpersonationApprovers or update an existing UserImpersonationApprovers
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.UpsertUserImpersonationApproversRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.domain.UserImpersonationApprovers}
      tags:
        - user-impersonation

  /simon/api/v2/users/updated:
    post:
      x-scopes:
        - admin
        - simon-system-user
      summary: Updates sent from EventBridge for users collection
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.domain.KafkaUserMessageResponse}

  /simon/api/v2/users/bump-versions:
    x-scopes:
      - admin
    post:
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.common.api.request.BumpUsersVersionRequest}
      summary: Bumps all users from network or user ids provided in payload
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.common.api.response.BumpUsersVersionResponse}

  /simon/api/v2/users/passport/initialize:
    post:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - fa
      tags:
        - users
      summary: Initialize passport verification flow
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.PassportVerificationRequest}
      responses:
        204:
          description: Flow initialized

  /simon/api/v2/users/passport/confirm:
    post:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - fa
      tags:
        - users
      summary: Confirm passport verification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.PassportConfirmation}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  default:
                    type: string

  /simon/api/v2/users/passport:
    delete:
      x-scopes:
        - admin
        - simon-system-user
        - fa-manager
        - fa
      tags:
        - users
      summary: Remove user passport
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  default:
                    type: string

  /simon/api/v2/master-user:
    x-scopes: [ ]
    get:
      x-kong-plugin-rate-limiting:
        config:
          minute: 5
          hour: 500
          policy: local
      x-disabled-plugins:
        - openId
      tags:
        - users
      summary: Get a master user
      parameters:
        - name: email
          in: query
          required: true
          schema:
            $ref: ${io.simon.openapi.definitions.CommonDefinitions.EmailAddress}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.response.MasterUser}
    post:
      x-kong-plugin-rate-limiting:
        config:
          minute: 5
          hour: 500
          policy: local
      x-disabled-plugins:
        - openId
      tags:
        - users
      summary: Get a master user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.OpenApiDefinitions.MasterUserQuery}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.response.MasterUser}
  /simon/api/v1/access-tokens/client-credentials/enhance:
    x-kong-route-defaults:
      regex_priority: 1000
      headers:
        'X-Simon-${STAGE}-Token':
          - ${ENHANCE_ACCESS_TOKEN_INLINE_HOOK_SECRET}
    x-disabled-plugins:
      - openId
      - request-validator
    post:
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.ClientCredentialInlineHookRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.response.InlineHookResponse}
      tags:
        - useracl
        - access-tokens
  /simon/api/v1/access-tokens/authorization-code/enhance:
    x-kong-route-defaults:
      regex_priority: 1000
      headers:
        'X-Simon-${STAGE}-Token':
          - ${ENHANCE_ACCESS_TOKEN_INLINE_HOOK_SECRET}
    x-disabled-plugins:
      - openId
      - request-validator
    post:
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.AuthorizationCodeInlineHookRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.response.InlineHookResponse}
      tags:
        - useracl
        - access-tokens
  /simon/api/v1/access-tokens/saml-bearer/enhance:
    x-kong-route-defaults:
      regex_priority: 1000
      headers:
        'X-Simon-${STAGE}-Token':
          - ${ENHANCE_ACCESS_TOKEN_INLINE_HOOK_SECRET}
    x-disabled-plugins:
      - openId
      - request-validator
    post:
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ${com.simonmarkets.users.api.request.SamlBearerHookRequest}
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.response.InlineHookResponse}
      tags:
        - useracl
        - access-tokens
  /simon/api/v1/access-tokens/{id}/dry-run:
    get:
      x-scopes:
        - admin
      tags:
        - useracl
        - access-tokens
      summary: Does a dry run of the enhance access token flow for the given user
      responses:
        200:
          description: Success
          content:
            application/json:
              schema:
                $ref: ${com.simonmarkets.users.api.response.InlineHookResponse}
