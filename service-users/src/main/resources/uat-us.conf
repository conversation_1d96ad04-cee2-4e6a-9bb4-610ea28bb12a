include classpath("service-users-common.conf")

simon-base-path = "https://origin-a.uat.icapitalnetwork.com/simon/api"
system-user-id = "0oaevl54gplnFvyx70h7" # todo (abhi): wrong

user-sync-system-user-id = "0oa1pqy4c2zavA5Sb0h8" # todo (abhi): wrong

dynamo-db-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      audience = "simonmarkets.com"
      clock-offset-in-seconds = 0
      proxy-host = "internal-uat-squid-elb-323764645.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }
}

http-client-config {
  auth: {
    type = OAuth
    client-id = "0oaevl54gplnFvyx70h7" # todo (abhi): wrong
    client-secret-file = ""
    site = "sm:applicationconfig-issuer-uri"
    authorization-path = ""
    token-path = "/v1/token"
    scope = "read_product_data"
    token-type = {
      type = BearerToken
    }
    client-secret = "sm:applicationconfig-oktaclientsecret"
  }
  proxy {
    port: 3128
    address: "internal-uat-squid-elb-323764645.us-east-1.elb.amazonaws.com"
  }
}

#TODO: add http client config for mapping service

acl-cache-config {
  enabled = true
  config {
    service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
    signin-region = "us-east-1"
  }
}

acl-repository-config {
  service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
  signin-region = "us-east-1"
}

okta-config {
  client-config {
    okta-org-url = "https://auth.uat.simonmarkets.com"
    okta-auth-token = "sm:applicationconfig-okta-api-token"
    cache-ttl = 30.seconds
    proxy {
      port: 3128
      host: "internal-uat-squid-elb-323764645.us-east-1.elb.amazonaws.com"
    }
  }

  service-config {
    create {
      activate = true,
      setPassword = true,
      expirePassword = false
    }

    profile {
      network-name = "network"
      skip-secondary-emails = ["@simonmarkets.com"]
      secondary-email = "<EMAIL>"
    }
  }

  enabled = true
}

okta-sync-config {
  service-entitlements = ["admin"]
  cache-ttl = 15 minutes
  multi-factor-config {
    sms-group-id = "00g1kisw957yzLOEJ0h8" # todo (abhi): wrong
    totp-group-id = "00g1kit12koGDognL0h8" # todo (abhi): wrong
    email-group-id = "00g1kiszwnitLMIsG0h8" # todo (abhi): wrong
    security-question-group-id = "00g1kiszgccQX8oY00h8" # todo (abhi): wrong
    google-auth-group-id = "00g1kiszvdukp3hNr0h8" # todo (abhi): wrong
    voice-call-group-id = "00g1kit0r35z23Kxt0h8" # todo (abhi): wrong
    okta-verify-group-id = "00g1kisv2rhSljbkN0h8" # todo (abhi): wrong
    sms-voice-call-group-id = "00g1lp0bcnr6JRf8Y0h8" # todo (abhi): wrong
    enabled = true
  }
}

mongo-trigger-config {
  database-collections = [
    {
      database = "pipg"
      collection = "users"
    }
  ]
}

enhance-access-token-config {
  app-expiry-config {
    "0oa1ggsd0jae9ZSRT0h8" = 60.minutes # todo (abhi): wrong
    "0oa1lmw4iq2gmYcIk0h8" = 60.minutes # todo (abhi): wrong
    "0oa1n4xz7wwbdIJwA0h8" = 14.hours # todo (abhi): wrong
    "0oa1era5g3hppXRCs0h8" = 14.hours # todo (abhi): wrong
  }
  system-admin-id = "0oaevl54gplnFvyx70h7" # todo (abhi): wrong
  pilot-role-key = "pilot"
  inline-hook-auth {
    header-name = "X-Simon-Uat-Token"
    header-value = "sm:gitlab_internal/kong-uat-us-enhance-access-token-inline-hook-secret"
    timeout = 15.seconds
  }
  undefined-sub-claim: "undefined"
  network-token-lifetimes: []
}

encryption {
  endpoint = "https://origin-a.uat.icapitalnetwork.com/kms/api/v1",
  environment = "uat-us",
  simon-base-url = "https://origin-a.uat.icapitalnetwork.com",
  hashing-key-json = "sm:applicationkey-hashing"
}
