package com.simonmarkets.users.api.routes

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.server.{Directive, Directive1, Route}
import akka.http.scaladsl.testkit.{RouteTestTimeout, ScalatestRouteTest}
import akka.testkit.TestDuration
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.Capabilities
import com.simonmarkets.capabilities.EndpointScopes.FaScope
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.TraceId
import com.simonmarkets.okta.domain.SimonId
import com.simonmarkets.resteasy.DirectivesWithCirce
import com.simonmarkets.useracl.UserAclAuthorizedDirective
import com.simonmarkets.users.api.JsonAssertions
import com.simonmarkets.users.api.codec.AccessTokenCodecs
import com.simonmarkets.users.api.request.ClientCredentialInlineHookRequest
import com.simonmarkets.users.api.response.InlineHookResponse
import com.simonmarkets.users.config.InlineHookConfig
import com.simonmarkets.users.decodeFileUnsafe
import com.simonmarkets.users.domain.{SystemUser, UserNotFound}
import com.simonmarkets.users.service.AccessTokenService
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{Matchers, WordSpec}
import zio.{Duration, ZIO}

import scala.concurrent.duration.DurationInt

class AccessTokensRouteSpec
  extends WordSpec
    with Matchers
    with ScalatestRouteTest
    with MockitoSugar
    with DirectivesWithCirce
    with AccessTokenCodecs
    with JsonAssertions {

  private val subject = "user"

  private val AdminUserACL = mock[UserACL]
  private val InlineHookHeaderName = "X-Simon-Test-Token"
  private val InlineHookHeaderValue = "CorrectValue"
  private val ValidInlineHookHeader = RawHeader(InlineHookHeaderName, InlineHookHeaderValue)
  private val InvalidInlineHookHeader = RawHeader(InlineHookHeaderName, "random-value")
  private val timeoutDuration = 500.millis
  private val inlineHookConfig = InlineHookConfig(InlineHookHeaderName, InlineHookHeaderValue, timeoutDuration)
  val userACLDirective: UserAclAuthorizedDirective = mock[UserAclAuthorizedDirective]

  private val unauthenticated = new Directive1[TraceId] {
    override def tapply(f: Tuple1[TraceId] => Route): Route = f.apply(Tuple1(TraceId.randomize))
  }

  val tokenService: AccessTokenService = mock[AccessTokenService]

  private val clientCredentialRequest = decodeFileUnsafe[ClientCredentialInlineHookRequest]("clientCredentialsHookRequest.json")

  def routes: Route = AccessTokenRoutes(tokenService, userACLDirective, inlineHookConfig).routes

  private val inlineHookResponse = "{\"commands\":[{\"type\":\"com.okta.access.patch\",\"value\":[{\"op\":\"replace\",\"path\":\"/claims/scopes\",\"value\":[\"fa\"]},{\"op\":\"add\",\"path\":\"/claims/trace\",\"value\":\"XcGNaPSTlRlFeydJ3y0k4gAAD6g\"},{\"op\":\"add\",\"path\":\"/claims/icnId\",\"value\":\"icnId\"},{\"op\":\"add\",\"path\":\"/claims/wlp\",\"value\":\"wlp\"},{\"op\":\"replace\",\"path\":\"/token/lifetime/expiration\",\"value\":1000}]}]}"

  private val traceId = TraceId(clientCredentialRequest.data.context.request.id)

  private val icnId = Some("icnId")

  private val wlp = Some("wlp")

  private val systemOktaData = SystemUser(
    icnId,
    wlp,
    Set(FaScope),
    Some(1000.seconds),
    traceId
  )

  private val authorized = new Directive[(TraceId, UserACL)] {
    override def tapply(f: ((TraceId, UserACL)) => Route): Route = f.apply((traceId, AdminUserACL))
  }

  private implicit val timeout: RouteTestTimeout = RouteTestTimeout(15.seconds.dilated)

  "POST simon/api/v1/access-tokens/client-credentials/enhance" should {
    "return scopes and token expiration for a client credential user" in {
      when(tokenService.clientCredentialEnrichment(clientCredentialRequest)(traceId)).thenReturn(ZIO.succeed(systemOktaData))
      when(userACLDirective.unauthenticated).thenReturn(unauthenticated)
      when(userACLDirective.authorized(Capabilities.Admin)).thenReturn(authorized)
      Post(s"/simon/api/v1/access-tokens/client-credentials/enhance", clientCredentialRequest) ~> ValidInlineHookHeader ~> routes ~> check {
        status shouldBe StatusCodes.OK
        val response = responseAs[String]
        assertJsonStringsAreEqual(response, inlineHookResponse)
      }
    }
    "return not found when a user is not found in the system" in {
      when(tokenService.clientCredentialEnrichment(clientCredentialRequest)(traceId)).thenReturn(ZIO.fail(UserNotFound(SimonId(subject))))
      when(userACLDirective.unauthenticated).thenReturn(unauthenticated)
      when(userACLDirective.authorized(Capabilities.Admin)).thenReturn(authorized)
      Post(s"/simon/api/v1/access-tokens/client-credentials/enhance", clientCredentialRequest) ~> ValidInlineHookHeader ~> routes ~> check {
        status shouldBe StatusCodes.NotFound
        responseAs[String] shouldBe "{\"messages\":[\"User not found SimonId(user)\"]}"
      }
    }
    "return forbidden when the X-Simon-<env>-Token header is not set" in {
      when(tokenService.clientCredentialEnrichment(clientCredentialRequest)(traceId)).thenReturn(ZIO.succeed(systemOktaData))
      when(userACLDirective.unauthenticated).thenReturn(unauthenticated)
      when(userACLDirective.authorized(Capabilities.Admin)).thenReturn(authorized)
      Post(s"/simon/api/v1/access-tokens/client-credentials/enhance", clientCredentialRequest) ~> routes ~> check {
        status shouldBe StatusCodes.Forbidden
        responseAs[String] shouldBe "{\"messages\":[\"Unable to validate the auth token\"]}"
      }
    }
    "return forbidden when the X-Simon-<env>-Token header value is not as expected" in {
      when(tokenService.clientCredentialEnrichment(clientCredentialRequest)(traceId)).thenReturn(ZIO.succeed(systemOktaData))
      when(userACLDirective.unauthenticated).thenReturn(unauthenticated)
      when(userACLDirective.authorized(Capabilities.Admin)).thenReturn(authorized)
      Post(s"/simon/api/v1/access-tokens/client-credentials/enhance", clientCredentialRequest) ~> InvalidInlineHookHeader ~> routes ~> check {
        status shouldBe StatusCodes.Forbidden
        responseAs[String] shouldBe "{\"messages\":[\"Unable to validate the auth token\"]}"
      }
    }
    "return Nil changes when timeout is reached" in {
      val slow = ZIO.sleep(Duration.Infinity).as(systemOktaData)
      when(tokenService.clientCredentialEnrichment(clientCredentialRequest)(traceId)).thenReturn(slow)
      Post(s"/simon/api/v1/access-tokens/client-credentials/enhance", clientCredentialRequest) ~> ValidInlineHookHeader ~!> routes ~> check {
        status shouldBe StatusCodes.OK
        responseAs[InlineHookResponse] shouldBe InlineHookResponse(Nil)
      }
    }
  }

  "GET simon/api/v1/access-tokens/:userId/dry-run" should {
    "return scopes, token expiration and userId for requested user only if the calling user is an admin" in {
      when(tokenService.getScopesAndTokenExpiration(SimonId(subject))(traceId)).thenReturn(ZIO.succeed(systemOktaData))
      when(userACLDirective.unauthenticated).thenReturn(unauthenticated)
      when(userACLDirective.authorized(Capabilities.Admin)).thenReturn(authorized)
      Get(s"/simon/api/v1/access-tokens/$subject/dry-run") ~> routes ~> check {
        status shouldBe StatusCodes.OK
        val response = responseAs[String]
        response shouldBe inlineHookResponse
      }
    }
    "return user not found when user is not found in the system" in {
      when(tokenService.getScopesAndTokenExpiration(SimonId(subject))(traceId)).thenReturn(ZIO.fail(UserNotFound(SimonId(subject))))
      when(userACLDirective.unauthenticated).thenReturn(unauthenticated)
      when(userACLDirective.authorized(Capabilities.Admin)).thenReturn(authorized)
      Get(s"/simon/api/v1/access-tokens/$subject/dry-run") ~> routes ~> check {
        status shouldBe StatusCodes.NotFound
        responseAs[String] shouldBe "{\"messages\":[\"User not found SimonId(user)\"]}"
      }
    }
    "return forbidden when the user is not an admin" in {
      when(userACLDirective.unauthenticated).thenReturn(unauthenticated)
      when(userACLDirective.authorized(Capabilities.Admin)).thenReturn(complete(HttpError.forbidden("User is not an Admin").httpResponse))
      Get(s"/simon/api/v1/access-tokens/$subject/dry-run") ~> routes ~> check {
        status shouldBe StatusCodes.Forbidden
        responseAs[String] shouldBe "{\"messages\":[\"User is not an Admin\"]}"
      }
    }
  }

}
