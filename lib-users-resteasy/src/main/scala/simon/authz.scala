package simon

import com.goldmansachs.marquee.pipg.UserRole
import simon.Id.NetworkId

package object authz {

  /*
   * Entitlements
   */
  def entitlementKey(action: String, dynamicRole: String, isRegS: Boolean = false): String =
    if (isRegS) s"$action:$dynamicRole:regS"
    else s"$action:$dynamicRole"

  def entitlementKeys(action: String, dynamicRoles: Set[String], isRegS: Boolean = false): Set[String] =
    dynamicRoles.map(entitlementKey(action, _, isRegS))

  /*
   * Dynamic roles
   */
  def UserDynamicRole(userId: String) = s"guid:$userId"

  def FANumberDynamicRole(faNumber: String, networkId: NetworkId) = s"faNumber:$faNumber:$networkId"

  def LocationDynamicRole(location: String, networkId: NetworkId) = s"location:$location:$networkId"

  def RoleDynamicRole(role: UserRole) = s"role:${role.name}"

  def NetworkDynamicRole(networkId: NetworkId) = s"pipg:network:$networkId"

  def RoleInNetworkDynamicRole(role: UserRole, networkId: NetworkId) = s"pipg:role:${role.name}:$networkId"

  def CertificationDynamicRole(certification: String) = s"pipg:certification:$certification"

  def OldPurviewNetworkDynamicRole(networkId: NetworkId) = s"pipg:purviewNetwork:$networkId"

  def NewPurviewProductNetworkDynamicRole(networkId: NetworkId,  issuerSymbol: String, role: UserRole) = s"role:${role.name}:purview:product:$networkId:$issuerSymbol"

  def NewPurviewProductNetworkDynamicRoleWithRole(networkId: NetworkId, issuerSymbol: String, role: UserRole) =
    s"purview:product:$networkId:$issuerSymbol:role:${role.name}"

  implicit class UserRoleOps(val x: UserRole) extends AnyVal {

    def dr: String = RoleDynamicRole(x)

    def dr(networkId: NetworkId): String = RoleInNetworkDynamicRole(x, networkId)
  }
}
