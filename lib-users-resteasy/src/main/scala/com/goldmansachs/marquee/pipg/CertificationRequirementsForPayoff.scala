package com.goldmansachs.marquee.pipg

import com.goldmansachs.marquee.pipg.NetworkOpenApiDefinitions.{CertificationAlternatives, LearnTrackId}
import com.simonmarkets.shared.ProductType
import io.simon.openapi.annotation.ClassReference
import io.simon.openapi.annotation.Field._

@deprecated("Deprecated in favor of CertificationAlternativesForProduct", "88.0.0")
case class CertificationRequirementsForPayoff(
    @Required
    @Pattern("^[a-zA-Z0-9_ ]*$")
    payoffType: String,

    @Required
    @Pattern("^create$")
    action: String,

    @Required
    @TypeArgRef(LearnTrackId)
    requiredTrackIds: Seq[String])

case class UserCertificationStatusForPayoff(
    payoffType: String,
    action: String,
    completedTracks: List[String],
    remainingTracks: List[String])

@deprecated("Deprecated in favor of UserMultiCertificationStatusMap", "88.0.0")
case class UserCertificationStatusMap(payoffToCertificationStatus: Map[String, UserCertificationStatusForPayoff])

case class CertificationAlternativesForProduct(
    @Required
    @Pattern("^[a-zA-Z0-9_ ]*$")
    productId: String,

    @Required
    @Pattern("^[- _a-zA-Z0-9.,&()#]*$")
    productName: String,

    @Required
    @Ref(CertificationAlternatives)
    certificationAlternatives: Seq[Seq[String]],

    @Ref(ClassReference(classOf[CompensationType]))
    compensationType: Option[CompensationType] = None,

    @Ref(UnderliersList)
    underliers: Option[Seq[String]] = None
)

case class ProductCertificationRequirements(
    @Required
    @Ref(ProductType.Ref)
    productType: ProductType,

    @Ref(ClassReference(classOf[Seq[CertificationAlternativesForProduct]]))
    certificationRequirements: Seq[CertificationAlternativesForProduct],

    certificationProducts: Option[Seq[String]] = None,

    attributeBasedCertificationRequirements: Option[Seq[AttributeBasedCertificationRequirement]] = None,
)

case class AttributeBasedCertificationRequirement(
    attributeValue: Option[String],
    @Ref(ProductAttribute.Ref)
    attributeType: ProductAttribute,
    certificationAlternatives: Seq[Seq[String]],
)

case class UserMultiCertificationStatusForPayoff(
    payoffType: String,
    action: String,
    completedTracks: List[String],
    remainingTracks: List[String])

case class UserMultiCertificationStatusMap(payoffToCertificationStatus: Map[String, UserCertificationStatusForPayoff])
