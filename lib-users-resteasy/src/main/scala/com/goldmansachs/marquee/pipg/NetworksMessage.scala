package com.goldmansachs.marquee.pipg

import com.goldmansachs.marquee.pipg.AnnuityEAppProvider.{AnnuityNetLink, Url}
import com.goldmansachs.marquee.pipg.Network.Action
import com.gs.marquee.foundation.util.AuditRequestContext
import com.gs.marquee.simon.http.Operation.Result.BadRequest
import com.gs.marquee.simon.http.{Operation, RequestContext, UserIdentity}
import com.gs.marquee.util.{DateTimes, NonEmptyList}
import com.simonmarkets.asset.Region
import com.simonmarkets.networks.enums.{CrmProvider, DomicileCode}
import com.simonmarkets.networks.{ExternalId, SmaStrategyAndUnderliers}
import com.simonmarkets.quantcommon.date.Enums.{BizDayConvention, BusinessDayConvention, Calendar}
import com.simonmarkets.quantcommon.date.RDate
import com.simonmarkets.quantcommon.date.calendars.OpenGammaDateFns._
import com.simonmarkets.shared.{EAppProvider, MaskedId}
import com.simonmarkets.util.{EnumEntry, ProductEnums, SafeEnums}
import com.typesafe.scalalogging.LazyLogging
import io.simon.openapi.annotation.Field._
import io.simon.openapi.annotation.{ClassReference, OpenApiType, Reference}
import io.simon.openapi.definitions.CommonDefinitions
import io.simon.openapi.definitions.CommonDefinitions.{ArbitraryMessage, CustomRole, NetworkName, EmailAddress => EmailAddressDef}
import org.joda.time.{DateTime, LocalDate}
import simon.Id.{ExternalNetworkId, NetworkId}

import scala.annotation.nowarn
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future}

sealed trait NetworksMsg

object NetworksMsg {

  sealed trait Audit extends RequestContext with UserIdentity

  sealed trait Find extends NetworksMsg

  object Find {

    trait Filter extends NetworksMsg

    case object All extends Filter

    case class Some(ids: Set[NetworkId]) extends Filter

    trait One extends NetworksMsg

    case class ByNetworkId(id: NetworkId) extends One

    case class ByMaskedId(maskedId: String, target: Option[String]) extends One
  }

  case class GetChangeLog(id: NetworkId) extends NetworksMsg

  case class GetSnapshot(id: NetworkId) extends NetworksMsg

  case class UpdateLocations(
      @Ignore
      id: NetworkId,
      locations: Set[NetworkLocation],
      @Ignore
      userId: String,
      @Ignore
      requestContext: AuditRequestContext
  ) extends NetworksMsg with Audit {
    def valid[T](f: UpdateLocations => Operation[T]): Operation[T] = {
      Network.buildLocationHierarchy(this.locations) match {
        case Right(_) => f(this)
        case Left(err) => BadRequest(err).op
      }
    }
  }

  case class Expire(id: String)

}

case class GetAccountMappingsRequest(userId: String) extends NetworksMsg with UserIdentity

case class AccountMappingsReply(network: String, mappings: List[String])

case class SaveNetworksRequest(
    networks: List[Network],
    requestContext: AuditRequestContext) extends NetworksMsg with RequestContext

case class GetNetworksRequest(
    networkType: String,
    requestContext: AuditRequestContext) extends NetworksMsg with RequestContext

case class GetCustomRoleCapabilitiesRequest(requestContext: AuditRequestContext) extends NetworksMsg with RequestContext

case class GetViewableNetworksRequest(fields: Option[Set[String]] = None) extends NetworksMsg



case class GetViewableNetworksResponseElem(@Ref(NetworkName) id: NetworkId, networkName: String, locations: Set[NetworkLocation])

case class AuctionNetwork(
    id: NetworkId,
    networkTypes: Option[List[String]] = None,
    to: Option[List[String]] = None,
    cc: Option[List[String]] = None,
    active: Boolean = true)

//TODO ST remove after onboarding tool changes
case class IdHubOrganization(
    @Required
    id: Int,

    @Required
    @Description("an idhub organization name")
    @Pattern("^[-\\s\\(\\)\\\"\\'A-Za-z0-9_:,/$!.&]*$")
    @MaxLength(256)
    @MinLength(1)
    name: String)

case class IssuerPurview(
    @Required
    @Type(OpenApiType.String)
    @Ref(NetworkName)
    network: NetworkId,

    @Required
    @TypeArgRef(CommonDefinitions.Symbol)
    issuers: Set[String],

    @Type(OpenApiType.String)
    @Ref(NetworkName)
    wholesaler: Option[NetworkId] = None,

    @TypeArgRef(CommonDefinitions.Symbol)
    purviewedDomainsUpdated: Option[Set[PurviewedDomain]] = None)

case class CustomRoleDefinition(
    @Required
    @Ref(CustomRole)
    role: String,

    @Required
    @TypeArgRef(CommonDefinitions.DomainCapability)
    capabilities: Set[String])

@EnumValues("Accounts", "OrderAccounts", "Users", "EditRfqs", "AwardRfqs", "SubmitIdeaRfqs", "ApproveIdeaRfqs", "ViewRfqs",
  "ViewSmaAccounts", "EditSmaAccounts", "ViewSmaAccountMaintenanceTasks", "EditSmaAccountMaintenanceTasks", "SIEquitiesOrders", "SIRatesOrders")
sealed trait PurviewedDomain extends EnumEntry

object PurviewedDomain extends SafeEnums[PurviewedDomain] {

  //holding accounts
  case object Accounts extends PurviewedDomain
  case object OrderAccounts extends PurviewedDomain
  case object Users extends PurviewedDomain
  case object EditRfqs extends PurviewedDomain
  case object AwardRfqs extends PurviewedDomain
  case object SubmitIdeaRfqs extends PurviewedDomain
  case object ApproveIdeaRfqs extends PurviewedDomain
  case object ViewRfqs extends PurviewedDomain
  case object ViewSmaAccounts extends PurviewedDomain
  case object EditSmaAccounts extends PurviewedDomain
  case object ViewSmaAccountMaintenanceTasks extends PurviewedDomain
  case object EditSmaAccountMaintenanceTasks extends PurviewedDomain
  case object EnumNotFound extends PurviewedDomain
  case object SIEquitiesOrders extends PurviewedDomain
  case object SIRatesOrders extends PurviewedDomain

  override def Values: Seq[PurviewedDomain] = Accounts :: OrderAccounts :: Users :: EditRfqs :: AwardRfqs ::
    SubmitIdeaRfqs :: ApproveIdeaRfqs :: ViewRfqs :: ViewSmaAccounts :: EditSmaAccounts ::
    ViewSmaAccountMaintenanceTasks :: EditSmaAccountMaintenanceTasks :: EnumNotFound :: ViewSmaAccountMaintenanceTasks ::
    EditSmaAccountMaintenanceTasks :: EnumNotFound :: SIEquitiesOrders :: SIRatesOrders :: Nil
}

@EnumValues("Issuer", "Wholesaler", "Distributor", "Internal")
sealed trait NetworkCategory extends EnumEntry

object NetworkCategory extends ProductEnums[NetworkCategory] {

  case object Issuer      extends NetworkCategory
  case object Wholesaler  extends NetworkCategory
  case object Distributor extends NetworkCategory
  case object Internal extends NetworkCategory
  case object Custodian extends NetworkCategory

  implicit val ordering: Ordering[NetworkCategory] = Ordering.by[NetworkCategory, Int] {
    case Issuer       => 0
    case Wholesaler   => 1
    case Distributor  => 2
    case Internal     => 3
    case Custodian    => 4
  }

  def fromTypes(types: Traversable[NetworkType]): Option[NetworkCategory] = {
    types.toList.sorted.headOption map { _.category }
  }

  override def Values: Seq[NetworkCategory] = Seq(Issuer, Wholesaler, Distributor, Internal)
}

@EnumValues("ADMIN", "HEDGE_PROVIDER", "BROKER_DEALER", "PRIVATE_BANK", "ISSUER", "RIA", "WHOLESALER", "CARRIER", "E_APP",
  "ILLUSTRATION_VENDOR", "VENDOR", "IMO", "WIREHOUSE", "TAMP", "ETF_SPONSOR", "SMA_MANAGER", "INTERNAL", "OTHER")
sealed abstract class NetworkType(
    val category: NetworkCategory,
    override val productPrefix: String) extends EnumEntry with Serializable {

  def name: String = productPrefix
}

object NetworkType extends SafeEnums[NetworkType] {
  case object Admin         extends NetworkType(NetworkCategory.Internal, "ADMIN")
  case object Issuer        extends NetworkType(NetworkCategory.Issuer      , "ISSUER")
  case object HedgeProvider extends NetworkType(NetworkCategory.Issuer      , "HEDGE_PROVIDER")
  case object Wholesaler    extends NetworkType(NetworkCategory.Wholesaler  , "WHOLESALER")
  case object RIA           extends NetworkType(NetworkCategory.Distributor , "RIA")
  case object BrokerDealer  extends NetworkType(NetworkCategory.Distributor , "BROKER_DEALER")
  case object PrivateBank   extends NetworkType(NetworkCategory.Distributor , "PRIVATE_BANK")
  case object Carrier       extends NetworkType(NetworkCategory.Distributor , "CARRIER")
  case object EApp          extends NetworkType(NetworkCategory.Distributor , "E_APP")
  case object IllustrationVendor   extends NetworkType(NetworkCategory.Distributor , "ILLUSTRATION_VENDOR")
  case object Imo           extends NetworkType(NetworkCategory.Distributor , "IMO")
  case object Wirehouse     extends NetworkType(NetworkCategory.Distributor , "WIREHOUSE")
  case object Tamp          extends NetworkType(NetworkCategory.Distributor , "TAMP")
  case object ETFSponsor    extends NetworkType(NetworkCategory.Distributor , "ETF_SPONSOR")
  case object SMAManager    extends NetworkType(NetworkCategory.Distributor , "SMA_MANAGER")
  case object Internal      extends NetworkType(NetworkCategory.Distributor , "INTERNAL")
  case object Custodian     extends NetworkType(NetworkCategory.Custodian , "CUSTODIAN")
  case object Other         extends NetworkType(NetworkCategory.Distributor , "OTHER")
  case object Vendor        extends NetworkType(NetworkCategory.Distributor , "VENDOR")
  case object EnumNotFound  extends NetworkType(NetworkCategory.Internal, "ENUM_NOT_FOUND")

  implicit val ordering: Ordering[NetworkType] = Ordering.by(_.category)

  def Values: Seq[NetworkType] = Seq(Admin, Issuer, HedgeProvider, Wholesaler, RIA, BrokerDealer, PrivateBank, Carrier,
    EApp, IllustrationVendor, Imo, Wirehouse, Tamp, ETFSponsor, SMAManager, Custodian, Internal, Other, Vendor, EnumNotFound)

  def fromStringList(strings: List[String]): Option[List[NetworkType]] = {
    val results = strings.map(NetworkType(_)).filterNot(_ == NetworkType.EnumNotFound)
    if (results.isEmpty) None else Some(results)
  }

  def fromCategory(category: NetworkCategory): Seq[NetworkType] = category match {
    case NetworkCategory.Issuer => Seq(NetworkType.Issuer, NetworkType.HedgeProvider)
    case NetworkCategory.Wholesaler => Seq(NetworkType.Wholesaler)
    case NetworkCategory.Distributor => Seq(NetworkType.RIA, NetworkType.BrokerDealer, NetworkType.PrivateBank,
      NetworkType.Carrier, NetworkType.EApp, NetworkType.IllustrationVendor,
      NetworkType.Imo, NetworkType.Wirehouse, NetworkType.Tamp, NetworkType.ETFSponsor, NetworkType.SMAManager,
      NetworkType.Internal, NetworkType.Vendor, NetworkType.Other,
    )
    case NetworkCategory.Internal => Seq(NetworkType.Admin)
    case NetworkCategory.Custodian => Seq(NetworkType.Custodian)
  }
}

@EnumValues("Fee", "Commission")
sealed trait CompensationType extends EnumEntry

object CompensationType extends ProductEnums[CompensationType] {

  case object Fee extends CompensationType
  case object Commission extends CompensationType

  override def Values: Seq[CompensationType] = Seq(Fee, Commission)
}

@Ref(CommonDefinitions.Symbol)
case object Underlier extends Reference

@Type(OpenApiType.Array)
@TypeArgRef(Underlier)
case object UnderliersList extends Reference

case class ExternalAlias(
    @Ref(ArbitraryMessage)
    name: String,

    @Description("Regex")
    @Pattern("^\\S{1,60}$")
    pattern: String) {

  @Ignore
  private val regex = pattern.r

  def validate(id: String): Either[String, String] = {
    id match {
      case regex(_*) => Right(id)
      case _         => Left(s"external $name id='$id' doesn't match the following pattern: '$pattern'")
    }
  }
}

case class NetworkLocation(
    @Required @Ref(CommonDefinitions.Location) name: String,
    @Ref(CommonDefinitions.Location) parent: Option[String],
    @Required @TypeArgRef(CommonDefinitions.Location) children: Set[String])

case class LocationNode(name: String, children: Set[LocationNode])

case class AnnuityEAppProvider(
    @Required
    @Ref(ClassReference(classOf[EAppProvider]))
    providerName: EAppProvider,

    @Required
    params: Map[String, String]
){
  val asUserAnnuityEAppProvider: Option[UserAnnuityEAppProvider] = providerName match {
    case EAppProvider.Firelight =>
      params.get(Url).map(url => UserAnnuityEAppProvider(providerName, Some(url)))
    case EAppProvider.Ebix =>
      params.get(AnnuityNetLink).map(url => UserAnnuityEAppProvider(providerName, Some(url)))
    case EAppProvider.RJPipeline =>
      params.get(Url).map(url => UserAnnuityEAppProvider(providerName, Some(url)))
    case EAppProvider.FIDx =>
      Some(UserAnnuityEAppProvider(providerName, None))
  }
}

object AnnuityEAppProvider {
  // keys for params
  val OrgId = "orgId"
  val Secret = "secret"
  val AnnuityNetId = "annuityNetId"
  val AnnuityNetLink = "annuityNetLink"
  val Url = "url"
  val UserRoleCode = "userRoleCode"
  val UserRoleCodeTc = "userRoleCodeTc"
}

case class UserAnnuityEAppProvider(
    @Required
    @Ref(ClassReference(classOf[EAppProvider]))
    providerName: EAppProvider,

    @Required
    @TypeArgRef(CommonDefinitions.Symbol)
    link: Option[String]
)

case class PartnerUrl(
    @Required
    partnerName: String,

    @Required
    urls: Set[URL]
)

case class URL(
    @Required
    urlType: String,
    @Required
    url: String
)

case class SSOPrefix(
    @Required
    @Type(OpenApiType.String)
    baseUrl: String,
    @Required
    @Type(OpenApiType.String)
    redirectionKey: String,
    simonBase: Option[String],
    isRedirectionKeyEncoded: Option[Boolean] = None,
    ssoSystemName: Option[String] = None
)

case class EmbeddingInfo(
    @Required
    @Type(OpenApiType.String)
    hostApplicationUrl: String,

    @Type(OpenApiType.String)
    hostApplicationName: Option[String] = None
)

case class DistributionList(etfs: List[String], etfSponsor: List[String], etfHomeOffice: List[String])

case class ContactInfo(
    email: Option[String],
    phone: Option[String],
    @TypeArgRef(EmailAddressDef) distributionLists: Option[DistributionList] = None
)

@EnumValues("HomeOffice", "HomeSupport", "SIMON")
sealed trait ContactType extends EnumEntry

object ContactType extends ProductEnums[ContactType] {

  case object HomeOffice extends ContactType
  case object SIMON extends ContactType
  case object HomeSupport extends ContactType

  override def Values: Seq[ContactType] = Seq(HomeOffice, HomeSupport, SIMON)
}

case class ContactInfo2(
    name: Option[String],
    url: Option[String],
    urlDisplayText: Option[String],
    email: Option[String],
    phone: Option[String],
    contactType: Option[ContactType]
)

case class NetworkIdAndName(id: NetworkId, name: String)

case class LearnTrack(
    @Required
    @Ref(NetworkOpenApiDefinitions.LearnTrackId)
    trackId: String,

    @Required
    isActive: Boolean
)

case class Network(
    @Type(OpenApiType.String)
    id: NetworkId,
    networkName: String,
    purviews: Option[Set[Purview]] = None,
    //TODO ST remove this after onboarding tool changes
    idHubOrganization: IdHubOrganization,
    approverSet: Map[String, List[List[String]]] = Map.empty,
    ioiApproverSet: Map[String, List[List[String]]] = Map.empty,
    accountMappings: Option[List[String]] = None,
    networkTypes: Option[List[NetworkType]] = None,
    to: Option[List[String]] = None,
    cc: Option[List[String]] = None,
    purviewNetworks: Option[Set[IssuerPurview]] = None,
    salesFeeRuleIds: List[String] = Nil,
    capabilities: Map[String, List[String]] = Map.empty,
    payoffEntitlements: Map[String, Map[String, List[String]]] = Map.empty,
    payoffEntitlementsV2: Map[String, Map[String, Set[Action]]] = Map.empty,
    dynamicRoles: Set[String] = Set.empty,
    distributorAlias: Option[ExternalAlias] = None,
    omsAlias: Option[String] = None,
    version: Int = 0,
    customRolesConfig: Set[CustomRoleDefinition] = Set.empty,
    maskedIds: Set[MaskedId] = Set.empty,
    booksCloseConfig: List[BooksCloseConfig] = List.empty,
    booksCloseCustomConfig: List[BooksCloseConfig] = List.empty,
    booksSendConfig: Option[List[BooksCloseConfig]] = None,
    prospectusDeadlineConfig: Option[List[ProspectusDeadlineConfig]] = None,
    dtccId: Option[String] = None,
    locations: Set[NetworkLocation] = Set.empty,
    entitlements: Set[String] = Set.empty,
    annuityEAppProvider: Option[AnnuityEAppProvider] = None,
    ssoPrefix: Option[SSOPrefix] = None,
    contactInfo: Option[ContactInfo] = None,
    learnTracksV2: Seq[LearnTrack] = Seq.empty,
    learnContent: Seq[String] = Seq.empty,
    endUserShareableContent: Seq[String] = Seq.empty,
    idpId: Option[String] = None,
    siCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    annuityCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    definedOutcomeETFCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    certificationProducts: Option[Seq[String]] = None,
    externalId: Option[ExternalNetworkId] = None,
    externalIds: Set[ExternalId] = Set.empty,
    group: Option[String] = None,
    partnerUrls: Set[PartnerUrl] = Set.empty,
    crmProviders: List[CrmProvider] = List.empty,
    smaStrategiesAndUnderliers: Set[SmaStrategyAndUnderliers] = Set.empty,
    smaRestrictedIssuers: Set[String] = Set.empty,
    altCertificationRequirements: Option[Seq[CertificationAlternativesForProduct]] = None,
    uiViewCardOverrides: Map[String, List[String]] = Map.empty,
    uiViewCardOverridesExpiryDate: Map[String, String] = Map.empty,
    encryptionClientKey: Option[String] = None,
    sessionInactivityTimeout: Option[Int] = None,
    domiciles: Option[Set[DomicileCode]] = None,
    productTypeCertificationRequirements: Option[Seq[ProductCertificationRequirements]] = None,
) extends Snapshotable {

  val learnTracks: Seq[String] = learnTracksV2.map(_.trackId)

  val info: Network.Info = Network.Info(id, networkName, annuityEAppProvider.flatMap(_.asUserAnnuityEAppProvider), ssoPrefix, crmProviders)

  lazy val locationHierarchy: Option[LocationNode] =
    Network.buildLocationHierarchy(locations) match {
      case Left(_) => None
      case Right(root) => root
    }

  /**
   * This is a performance optimization.
   * This method is called a lot of times and it takes a long time if we calculate hashcode for the whole object */
  override def hashCode(): Int = id.hashCode()
}

case class Purview(
        networkId: String,
        purviewEntities: Set[PurviewEntity]
  )

case class PurviewEntity(
        key: String, //networkId if wholesaler, or issuer key, or custodian code
        purviewType: Option[NetworkCategory], //custodian or issuer or wholesaler (enum)
        domains: Option[Set[PurviewedDomain]] = None
  )

object Network {

  implicit object IncVer extends IncrementVersion[Network] {
    override def apply(x: Network): Network = x.copy(version = x.version + 1)
  }

  implicit object ExtId extends ExtractId[Network] {
    type Id = simon.Id.NetworkId
    override def id(x: Network): Id = x.id
  }

  case class Info(
      @Type(OpenApiType.String)
      id: NetworkId,
      name: String,
      annuityEAppProvider: Option[UserAnnuityEAppProvider] = None,
      ssoPrefix: Option[SSOPrefix]  = None,
      crmProviders: List[CrmProvider] = List.empty)

  case class Action(
      @Required
      @Pattern("^.*$")
      action: String,

      // optional (can be empty)
      contractParams: Map[String, Set[String]] = Map.empty
  )

  object Action {
    implicit val ordering: Ordering[Action] = Ordering.by[Network.Action, String](_.action)
  }

  object Info {

    val empty: Info = test("")

    def test(x: String): Info = Info(NetworkId(x), x)

    def test(x: NetworkId): Info = Info(x, NetworkId unwrap x)

    implicit val ordering: Ordering[Info] = Ordering.by[Info, String](_.name)
  }

  object DynamicRoles {

    def network(id: NetworkId): Set[String] = Set(s"pipg:network:$id")

    def purview(ids: Set[IssuerPurview]): Set[String] = {
      def userKey(networkId: NetworkId) = s"purview:user:$networkId"

      ids.foldLeft(Set.empty[String])((acc, issuerPurview) => {
        val userRole = userKey(issuerPurview.network)
        acc + userRole
      })
    }

    // FIXME: For backward compatibility during migration
    // For testing we have the ability to turn it off
    def oldPurview: Set[String] = Set.empty


    def capabilities(caps: Map[String, Iterable[String]]): Set[String] = {
      val res = caps flatMap {
        case (cap, actions) => actions map { action => s"pipg:$action:$cap" }
      }

      res.toSet
    }

    def payoff(entitlements: Map[String, Map[String, Set[Action]]]): Set[String] = entitlements.flatMap {
      case (issuer, payoffEnts) => payoffEnts flatMap {
        case (payoff, ents) =>
          ents map { action =>
            s"pipg:${action.action}InvestmentType:$payoff:$issuer"
          }
      }
    }.toSet

    def apply(network: Network): Set[String] = apply(
      networkId = network.id,
      purviewNetworks = network.purviewNetworks getOrElse Set.empty,
      networkCapabilities = network.capabilities mapValues { _.toSet },
      payoffEntitlements = network.payoffEntitlementsV2)

    def apply(
               networkId: NetworkId,
               purviewNetworks: Set[IssuerPurview],
               networkCapabilities: Map[String, Set[String]],
               payoffEntitlements: Map[String, Map[String, Set[Network.Action]]]): Set[String] = {

        purview(purviewNetworks) ++
        oldPurview ++
        network(networkId) ++
        capabilities(networkCapabilities) ++
        payoff(payoffEntitlements)
    }
  }

  def test(networkId: NetworkId, dynamicRoles: Set[String]): Network = Network(
    id = networkId,
    networkName = NetworkId unwrap networkId,
    purviews = None,
    idHubOrganization = IdHubOrganization(0, "0 idHubOrg"),
    dynamicRoles = dynamicRoles,
    maskedIds = Set.empty)

  def buildLocationHierarchy(locations: Set[NetworkLocation]): Either[String, Option[LocationNode]] = {
    if (locations.isEmpty) {
      Right(None)
    } else {
      try {
        val namesToLocations = locations.map(location => location.name -> location).toMap
        for {
          _ <- if (namesToLocations.size < locations.size) Left("Conflicting location definitions") else Right(())
          roots = locations.filter(_.parent.isEmpty)
          _ <- if (roots.size != 1) Left("Exactly one root location is required") else Right(())
          (rootNode, visitedLocs) = buildLocationHierarchyHelper(roots.head, namesToLocations, Set.empty[String])
          _ <- if (visitedLocs.size != namesToLocations.size) Left(s"Number of locations processed: ${visitedLocs.size} does not match number of inputs: ${namesToLocations.size}") else Right(())
          res <- Right(Some(rootNode))
        } yield res
      } catch {
        case _: StackOverflowError => Left("Stack overflow - most likely due to tree depth")
        case e: LocationValidationException => Left(e.message)
      }
    }
  }

  case class LocationValidationException(message: String) extends Exception

  private def buildLocationHierarchyHelper(currentLocation: NetworkLocation, namesToLocations: Map[String, NetworkLocation], visited: Set[String]): (LocationNode, Set[String]) = {
    if (visited.contains(currentLocation.name))
      throw LocationValidationException(s"Location ${currentLocation.name} was already visited")
    if (currentLocation.parent.nonEmpty && !visited.contains(currentLocation.parent.get))
      throw LocationValidationException(s"Location ${currentLocation.name}'s parent ${currentLocation.parent.get} does not exist")
    if (currentLocation.parent.nonEmpty && !namesToLocations(currentLocation.parent.get).children.contains(currentLocation.name))
      throw LocationValidationException(s"Location ${currentLocation.name}'s parent ${currentLocation.parent.get} does not claim it as a child")

    val visitedPlusCurrent = visited + currentLocation.name
    val (childNodes, allVisited) = currentLocation.children.foldLeft((Set.empty[LocationNode], visitedPlusCurrent)) {
      case ((nodeAccumulator, visitedLocAccumulator), childName) =>
        val childData = namesToLocations.getOrElse(childName, throw LocationValidationException(s"No location data presented for ${currentLocation.name}'s child: $childName"))
        val (childNode, visitedPlusCurrentPlusChildren) = buildLocationHierarchyHelper(childData, namesToLocations, visitedLocAccumulator)
        (nodeAccumulator + childNode, visitedPlusCurrentPlusChildren)
    }

    (LocationNode(currentLocation.name, childNodes), allVisited)
  }

  /** Offering Close Behaviour. Provides network level configuration options
   * which is used to update corresponding Offering
   * - `booksClose.status`
   * - `booksClose.softCloseDateTime`
   * - `booksClose.booksCloseDateTime`
   * fields in expected time.
   *
   * @param softOffset How long in advance should Offerings shared with this network be soft-closed (not allowed to trade for FA anymore)
   * @param booksOffset How long in advance should Offerings shared with this network be books-closed (not allowed to trade for FAManagers anymore)
   */
  // TODO: the original requirement to have both offsets optional has changed, now it's either both offsets are set or none; missing offset is set to 0
  // TODO: this means the books close automation code, db schema, formats, parsers - everything can be simplified a little bit
  final case class OfferingCloseBehaviour(
      softOffset: Option[Long],
      booksOffset: Option[Long]) {

    def hasDefinitions: Boolean = softOffset.nonEmpty && booksOffset.nonEmpty

    def map[T](
        s: Long => T,
        b: Long => T,
        both: (Long, Long) => T): Option[T] = {

      (softOffset, booksOffset) match {
        case (Some(soft), None)        => Some(s(soft))
        case (None, Some(books))       => Some(b(books))
        case (Some(soft), Some(books)) => Some(both(soft, books))
        case (None, None)              => None
      }
    }
  }

  object OfferingCloseBehaviour {

    val Empty: OfferingCloseBehaviour = OfferingCloseBehaviour(None, None)

    def soft(x: Long): OfferingCloseBehaviour = OfferingCloseBehaviour(Some(x), None)

    def books(x: Long): OfferingCloseBehaviour = OfferingCloseBehaviour(None, Some(x))

    def apply(soft: Long, books: Long): OfferingCloseBehaviour = OfferingCloseBehaviour(Some(soft), Some(books))

    def apply(soft: FiniteDuration, books: FiniteDuration): OfferingCloseBehaviour = OfferingCloseBehaviour(Some(soft.toSeconds), Some(books.toSeconds))
  }

  implicit class NetworkOps(val x: Network) extends AnyVal {

    def userPurviewNetworks: Set[NetworkId] = x.purviewNetworks.map(_.map(_.network)) getOrElse Set.empty

    def withDynamicRolesUpdated: Network = x.copy(dynamicRoles = Network.DynamicRoles(x))

    def category: Option[NetworkCategory] = x.networkTypes flatMap NetworkCategory.fromTypes
  }
}

case class NetworkViewId(@Ref(NetworkName)id: NetworkId)

// used to interact via http/json
@nowarn("cat=deprecation")
case class NetworkView(
    id: NetworkId,
    networkName: String,
    purviews: Option[Set[Purview]] = None,
    idHubOrganization: IdHubOrganization,
    accountMappings: Option[List[String]] = None,
    category: Option[NetworkCategory],
    networkTypes: Option[List[NetworkType]] = None,
    to: Option[List[String]] = None,
    cc: Option[List[String]] = None,
    approvers: Map[String, List[List[UserIdEmail]]] = Map.empty, // user emails
    ioiApprovers: Map[String, List[List[UserIdEmail]]] = Map.empty, // user emails
    payoffEntitlements: Map[String, Map[String, Set[Network.Action]]] = Map.empty,
    networkCapabilities: Map[String, Set[String]] = Map.empty,
    purviewNetworks: Option[Set[IssuerPurview]] = None,
    salesFeeRuleIds: List[String] = Nil,
    distributorAlias: Option[ExternalAlias] = None,
    omsAlias: Option[String] = None,
    version: Int = 0,
    customRolesConfig: Set[CustomRoleDefinition] = Set.empty,
    maskedIds : Set[MaskedId] = Set.empty,
    booksCloseConfig: List[BooksCloseConfig] = List.empty,
    prospectusDeadlineConfig: Option[List[ProspectusDeadlineConfig]] = None,
    dtccId : Option[String] = None,
    locations: Set[NetworkLocation] = Set.empty,
    annuityEAppProvider: Option[AnnuityEAppProvider] = None,
    ssoPrefix: Option[SSOPrefix]  = None,
    contactInfo: Option[ContactInfo] = None,
    learnTracks: Seq[String] = Seq.empty,
    learnTracksV2: Seq[LearnTrack] = Seq.empty,
    learnContent: Seq[String] = Seq.empty,
    endUserShareableContent: Seq[String] = Seq.empty,
    siCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    annuityCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    definedOutcomeETFCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    certificationProducts: Option[Seq[String]] = None,
    externalId: Option[ExternalNetworkId] = None,
    externalIds: Set[ExternalId] = Set.empty,
    group: Option[String] = None,
    partnerUrls: Set[PartnerUrl] = Set.empty,
    crmProviders: List[CrmProvider] = List.empty
)

object NetworkView {

  def fromNetwork(
      network: Network,
      loadEmails: Set[String] => Future[Map[String, String]])(implicit ec: ExecutionContext): Future[NetworkView] = {

    def enrichApproversWithEmail(ids: Map[String, List[List[String]]]): Future[Map[String, List[List[UserIdEmail]]]] = {
      val idSet = ids.values.flatten.flatten.toSet
      for {emails <- loadEmails(idSet)} yield {
        ids mapValues { _ map { _ map { id => UserIdEmail(id, emails.getOrElse(id, ""))}}}
      }
    }

    for {
      approvers <- enrichApproversWithEmail(network.approverSet)
      ioiApprovers <- enrichApproversWithEmail(network.ioiApproverSet)
    } yield NetworkView(
      id = network.id,
      networkName = network.networkName,
      idHubOrganization = network.idHubOrganization,
      accountMappings = network.accountMappings,
      category = network.networkTypes flatMap NetworkCategory.fromTypes,
      networkTypes = network.networkTypes,
      salesFeeRuleIds = network.salesFeeRuleIds,
      approvers = approvers,
      ioiApprovers = ioiApprovers,
      to = network.to,
      cc = network.cc,
      payoffEntitlements = network.payoffEntitlementsV2,
      networkCapabilities = network.capabilities.mapValues(_.toSet),
      purviewNetworks = network.purviewNetworks,
      distributorAlias = network.distributorAlias,
      omsAlias = network.omsAlias,
      version = network.version,
      customRolesConfig = network.customRolesConfig,
      maskedIds = network.maskedIds,
      booksCloseConfig = network.booksCloseConfig,
      prospectusDeadlineConfig = network.prospectusDeadlineConfig,
      dtccId = network.dtccId,
      locations = network.locations,
      annuityEAppProvider = network.annuityEAppProvider,
      ssoPrefix = network.ssoPrefix,
      contactInfo = network.contactInfo,
      learnTracks = network.learnTracks,
      learnTracksV2 = network.learnTracksV2,
      learnContent = network.learnContent,
      endUserShareableContent = network.endUserShareableContent,
      siCertificationRequirements = network.siCertificationRequirements,
      annuityCertificationRequirements = network.annuityCertificationRequirements,
      definedOutcomeETFCertificationRequirements = network.definedOutcomeETFCertificationRequirements,
      certificationProducts = network.certificationProducts,
      externalId = network.externalId,
      externalIds = network.externalIds,
      group = network.group,
      partnerUrls = network.partnerUrls,
      crmProviders = network.crmProviders
    )
  }
}

case class ApproverLists(lists: Seq[NonEmptyList[String]]) {

  def ids: Iterable[String] = lists flatMap { _.toList }
}

object ApproverLists {

  def apply(x: String): ApproverLists = ApproverLists(Vector(NonEmptyList(x)))

  def apply(x: NonEmptyList[String]): ApproverLists = ApproverLists(Vector(x))
}

case class ApproverMap(map: Map[String, ApproverLists]) {

  def ids(contractType: String, contractTypeWrapper: String): Iterable[String] = {
    map.get(s"$contractType:$contractTypeWrapper").toIterable flatMap { _.ids }
  }
}

object ApproverMap {

  val empty = new ApproverMap(Map.empty)

  def fromRaw(raw: Map[String, List[List[String]]]): ApproverMap = ApproverMap {
    for { (payoff, approvalLists) <- raw } yield {
      val approvalNonEmptyLists = for {
        approvalList          <- approvalLists
        approvalNonEmptyList  <- NonEmptyList.opt(approvalList)
      } yield approvalNonEmptyList

      payoff -> ApproverLists(approvalNonEmptyLists)
    }
  }

  def apply(xs: (String, ApproverLists)*): ApproverMap = ApproverMap(xs.toMap)

  def apply(payoff: String, lists: ApproverLists): ApproverMap = ApproverMap(Map(payoff -> lists))
}




case class ProspectusDeadlineConfig(tier: Int, config: DeadlineConfig)

case class DeadlineConfig(dayOffset: Int, hourOffset: Int, minuteOffset: Int)

//re-using this class to determine when books should be sent out to Bloomberg
case class BooksCloseConfig(
    @Required
    @Ref(ClassReference(classOf[Region]))
    region: Region,
    @Required tradeDateOffset: Int,
    @Required hourOfDay: Int,
    @Required minuteOfHour: Int
)

object BooksCloseConfig {
  val Americas: BooksCloseConfig = BooksCloseConfig(Region.Americas, 0, 14, 0)
  val Europe: BooksCloseConfig = BooksCloseConfig(Region.Europe, 0, 10, 0)
  val Asia: BooksCloseConfig = BooksCloseConfig(Region.Asia, -1, 21, 0)
  val Global: BooksCloseConfig = Americas.copy(region = Region.Global)
  val EM: BooksCloseConfig = Americas.copy(region = Region.EM)

  val defaults = List(Americas, Europe, Asia, Global, EM)

  val defaultsMap: Map[Region, BooksCloseConfig] = Map(
    Region.Americas -> Americas,
    Region.Europe -> Europe,
    Region.Asia -> Asia
  ).withDefault(_ => Americas)

  implicit class BooksCloseConfigOps(configs: List[BooksCloseConfig]) extends LazyLogging {

    implicit val localDateOrd: Ordering[DateTime] = Ordering.fromLessThan(_ isBefore _)

    def booksCloseDateFrom(tradeDate: LocalDate, regions: Set[Region]): Option[DateTime] =
      regions.map(region => booksCloseDateFrom(tradeDate, region)).min

    private def booksCloseDateFrom(tradeDate: LocalDate, region: Region): Option[DateTime] = {
      val config = configs.find(_.region == region).getOrElse(BooksCloseConfig.defaultsMap(region))
      val offset = RDate(s"${config.tradeDateOffset.toString}b") //b-index means offset in business days
      val bizDayConv: BizDayConvention = BusinessDayConvention.Previous
      val calendar = List(Calendar.NYSE)

      add(tradeDate, offset, calendar, bizDayConv) match {
        case Right(value) => Some(value.toDateTimeAtStartOfDay(DateTimes.NYTimeZone)
          .withTime(config.hourOfDay, config.minuteOfHour, 0, 0))
        case Left(error) =>
          logger.error("Error while books close calculation", error)
          None
      }
    }
  }

}

object NetworkOpenApiDefinitions {
  @Type(OpenApiType.Array)
  @MinItems(1)
  @MaxItems(100)
  @TypeArgRef(Approvers)
  case object ApproverList extends Reference

  @Type(OpenApiType.Array)
  @TypeArgRef(ClassReference(classOf[UserIdEmail]))
  case object Approvers extends Reference

  @Type(OpenApiType.Map)
  @TypeArgRef(PayOffEntitlements)
  case object PayOffEntitlementsMap extends Reference

  @Type(OpenApiType.Map)
  @TypeArgRef(PayOffEntitlementList)
  case object PayOffEntitlements extends Reference

  @Type(OpenApiType.Array)
  @TypeArgRef(ClassReference(classOf[Network.Action]))
  case object PayOffEntitlementList extends Reference

  @Type(OpenApiType.String)
  @Pattern("^.*$")
  case object PayOffEntitlement extends Reference

  @Type(OpenApiType.Array)
  @TypeArgRef(NetworkCapability)
  case object NetworkCapabilities extends Reference

  @Type(OpenApiType.String)
  @Pattern("^.*$")
  case object NetworkCapability extends Reference

  @Type(OpenApiType.String)
  @Pattern("^[-\\\\sA-Za-z0-9_:,/$!.]*$")
  @MaxLength(256)
  @MinLength(0)
  case object AccountName extends Reference

  @Pattern("^[-a-zA-Z0-9]*$")
  @Type(OpenApiType.String)
  case object LearnTrackId extends Reference

  @Type(OpenApiType.Array)
  @TypeArgRef(LearnTrackId)
  case object LearnTrackIdList extends Reference

  @Type(OpenApiType.Array)
  @TypeArgRef(LearnTrackIdList)
  case object CertificationAlternatives extends Reference

  @Pattern("^[- _a-zA-Z0-9.,&()#]*$")
  @Type(OpenApiType.String)
  case object LearnContentId extends Reference

  @EnumValues("id", "networkName", "locations")
  case object NetworkViewsFieldsQueryParam extends Reference

  @Pattern("^[a-zA-Z0-9]*$")
  @Type(OpenApiType.String)
  @MaxLength(32)
  case object Id extends Reference

  @Pattern("^[a-zA-Z0-9]{1,30}$")
  @Type(OpenApiType.String)
  @MinLength(1)
  @MaxLength(30)
  case object Cusip extends Reference
}
