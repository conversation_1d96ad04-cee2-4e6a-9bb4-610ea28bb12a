package com.goldmansachs.marquee.pipg

import com.goldmansachs.marquee.pipg.User.DistributorInfo
import com.gs.marquee.simon.http.Operation
import com.gs.marquee.simon.http.Operation._
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.ExternalId
import com.simonmarkets.shared.MaskedId
import com.simonmarkets.users.common.Context
import io.simon.openapi.annotation.Field._
import io.simon.openapi.annotation.OpenApiType
import simon.Id.NetworkId

import java.time.{LocalDateTime, ZoneId}

import scala.concurrent.{ExecutionContext, Future}


case class User(
    id: String,
    @Type(OpenApiType.String)
    networkId: NetworkId,
    createdAt: LocalDateTime,
    createdBy: String,
    updatedAt: LocalDateTime,
    updatedBy: String,
    emailSentAt: Option[LocalDateTime],
    emailSentBy: Option[String],
    lastVisitedAt: Option[LocalDateTime],
    email: String,
    firstName: String,
    lastName: String,
    distributorId: Option[String],
    omsId: Option[String],
    tradewebEligible: Boolean = false,
    regSEligible: Boolean = false,
    isActive: Boolean = true,
    roles: Set[UserRole],
    entitlements: Set[String] = Set.empty,
    dynamicRoles: Set[String] = Set.empty,
    locations: Set[String] = Set.empty,
    faNumbers: Set[String] = Set.empty,
    custodianFaNumbers: Set[CustodianFaNumber] = Set.empty,
    customRoles: Set[String] = Set.empty,
    maskedIds: Set[MaskedId] = Set.empty,
    licenses: Set[License] = Set.empty,
    idpId: Option[String] = None,
    distributorInfo: Option[DistributorInfo] = None,
    accountInContext: Option[String] = None,
    context: Option[Context] = None,
    cusips: Set[String] = Set.empty,
    idpLoginId: String,
    version: Int = 0,
    loginMode: String = "SSOAndUsernamePassword",
    userType: String = "Human",
    externalIds: Seq[ExternalId] = Seq.empty,
    purviewLicenses: Set[License] = Set.empty,
    purviewNsccCodes: Set[String] = Set.empty,
    firmId: Option[String] = None,
    whiteLabelPartnerId: Option[String] = None,
    secondaryEmail: Option[String] = None,
    iCapitalUserId: Option[String] = None,
    userSyncedAt: Option[LocalDateTime] = None,
    landingPage: Option[String] = None,
    groups: Map[String, Set[String]] = Map.empty,
    icnGroups: Set[String] = Set.empty,
    icnRoles: Set[String] = Set.empty,
    passport: Map[String, Int] = Map.empty,
) extends Snapshotable


object User extends TraceLogging {

  implicit val traceId = TraceId("user")

  implicit object IncVer extends IncrementVersion[User] {
    override def apply(x: User): User = x.copy(version = x.version + 1)
  }

  implicit object ExtId extends ExtractId[User] {
    type Id = String

    override def id(x: User): Id = x.id
  }

  def test(
      id: String,
      networkId: NetworkId,
      firstName: String,
      lastName: String = "",
      email: String = "",
      roles: Set[UserRole] = Set.empty,
      dynamicRoles: Set[String] = Set.empty,
      context: Option[Context] = None,
      maskedIds: Set[MaskedId] = Set.empty): User = {

    val now = LocalDateTime.now(ZoneId.systemDefault())

    User(
      id = id,
      networkId = networkId,
      firstName = firstName,
      lastName = lastName,
      email = email,
      createdAt = now,
      createdBy = "test",
      updatedAt = now,
      updatedBy = "test",
      emailSentAt = None,
      emailSentBy = None,
      lastVisitedAt = None,
      distributorId = None,
      omsId = None,
      roles = roles,
      customRoles = Set.empty,
      entitlements = Set.empty,
      dynamicRoles = dynamicRoles,
      maskedIds = maskedIds,
      context = context,
      idpLoginId = s"test-$email"
    )
  }

  case class DistributorInfo(
      role: Option[String],
      branch: Option[String],
      subgroup: Option[String],
      isHomeOffice: Option[Boolean]
  )

  object DynamicRoles {

    def guid(x: User): Set[String] =
      if (x.regSEligible) Set(s"guid:${x.id}", s"guid:${x.id}:regS")
      else Set(s"guid:${x.id}")

    def tradewebEligible(x: User): Set[String] = if (x.tradewebEligible) Set(s"pipg:view:tradewebEligible") else Set()

    def network(x: User): Set[String] = Set(s"pipg:network:${x.networkId}")

    def faNumbers(x: User): Set[String] = x.faNumbers.flatMap { num =>
      if (x.regSEligible) Set(s"faNumber:$num:${x.networkId}", s"faNumber:$num:${x.networkId}:regS")
      else Set(s"faNumber:$num:${x.networkId}")
    }

    def locations(x: User): Set[String] = x.locations.flatMap { location =>
      Set(s"location:$location:${x.networkId}") ++ {
        if (x.regSEligible) Set(s"location:$location:${x.networkId}:regS")
        else Set()
      }
    }

    def roles(x: User): Set[String] = x.roles flatMap { role => Seq(s"role:$role", s"pipg:role:$role") }

    def roleInNetwork(x: User): Set[String] = x.roles.flatMap { role =>
      if (x.regSEligible) Set(s"pipg:role:${role.name}:${x.networkId}", s"pipg:role:${role.name}:${x.networkId}:regS")
      else Set(s"pipg:role:${role.name}:${x.networkId}")
    }

    def apply(x: User): Set[String] = guid(x) ++ network(x) ++ roles(x) ++ roleInNetwork(x) ++ tradewebEligible(x) ++ faNumbers(x) ++ locations(x)
  }

  object Entitlements {

    def guid(a: String, x: User): String = s"$a:guid:${x.id}"

    def network(a: String, x: User): String = s"$a:pipg:network:${x.networkId}"

    def role(a: String, role: UserRole): String = s"$a:role:${role.name}"

    def roleInNetwork(a: String, role: UserRole, x: User): String = s"$a:pipg:role:${role.name}:${x.networkId}"

    def oldPurviewNetwork(a: String, x: User): String = s"$a:pipg:purviewNetwork:${x.networkId}"

    def newPurviewUser(a: String, x: User): String = s"$a:purview:user:${x.networkId}"

    def apply(x: User): Set[String] = {
      import UserProfileActions._
      import UserRole._

      Set(
        role(view, EqPIPGGSAdmin),
        guid(view, x),
        network(view, x),
        oldPurviewNetwork(view, x),
        newPurviewUser(view, x),

        role(viewLearningCenterData, EqPIPGGSAdmin),
        guid(viewLearningCenterData, x),
        roleInNetwork(viewLearningCenterData, EqPIPGFAManager, x),
        oldPurviewNetwork(viewLearningCenterData, x),
        newPurviewUser(viewLearningCenterData, x),

        role(broadcastNotificationTo, EqPIPGGSAdmin),
        roleInNetwork(broadcastNotificationTo, EqPIPGFAManager, x),
        roleInNetwork(broadcastNotificationTo, Wholesaler, x),
        oldPurviewNetwork(broadcastNotificationTo, x),
        newPurviewUser(broadcastNotificationTo, x)
      )
    }
  }

  case class Secured(
      id: String,
      networkId: NetworkId,
      createdAt: LocalDateTime,
      createdBy: String,
      updatedAt: LocalDateTime,
      updatedBy: String,
      emailSentAt: Option[LocalDateTime],
      emailSentBy: Option[String],
      lastVisitedAt: Option[LocalDateTime],
      email: String,
      firstName: String,
      lastName: String,
      externalIds: Map[String, String],
      tradewebEligible: Boolean,
      regSEligible: Boolean,
      roles: Set[UserRole],
      isActive: Boolean,
      locations: Set[String],
      faNumbers: Set[String],
      custodianFaNumbers: Set[CustodianFaNumber],
      customRoles: Set[String],
      licenses: Set[License],
      maskedIds: Set[MaskedId],
      idpId: Option[String],
      distributorInfo: Option[DistributorInfo],
      accountInContext: Option[String],
      context: Option[Context],
      cusips: Set[String],
      loginMode: String,
      userType: String,
      landingPage: Option[String]) {

    def isAdmin: Boolean = User.isAdmin(roles)

    def isManager: Boolean = User.isManager(roles)

    def isIssuer: Boolean = User.isIssuer(roles)

    def isWholesaler: Boolean = User.isWholesaler(roles)
  }

  def fullName(firstName: String, lastName: String): String = s"$firstName $lastName".trim

  def reversedName(firstName: String, lastName: String): String = s"$lastName $firstName".trim

  def isAdmin(roles: Set[UserRole]): Boolean = roles.contains(UserRole.EqPIPGGSAdmin)

  def isManager(roles: Set[UserRole]): Boolean = roles.contains(UserRole.EqPIPGFAManager)

  def isIssuer(roles: Set[UserRole]): Boolean = roles.contains(UserRole.Issuer)

  def isWholesaler(roles: Set[UserRole]): Boolean = roles.contains(UserRole.Wholesaler)

  implicit class UserOps(val x: User) extends AnyVal {

    def active: Boolean = x.isActive && x.roles.nonEmpty

    def isAdmin: Boolean = User.isAdmin(x.roles)

    def fullName: String = User.fullName(x.firstName, x.lastName)

    def nameReversed: String = User.reversedName(x.firstName, x.lastName)

    def withDynamicRolesUpdated: User = x.copy(dynamicRoles = User.DynamicRoles(x))

    def withEntitlementsUpdated: User = x.copy(entitlements = User.Entitlements(x))

    def withoutFaNumbers: User = x.copy(faNumbers = Set.empty)

    def withoutCustodianFaNumbers: User = x.copy(custodianFaNumbers = Set.empty)

    def secured(
        replaceId: String => Future[Option[String]],
        networkProvider: NetworkId => Operation[Network])(implicit ec: ExecutionContext): Future[User.Secured] = {

      for {
        replacedCreatedBy <- replaceId(x.createdBy)
        replacedUpdatedBy <- replaceId(x.updatedBy)
        replacedEmailSentBy <- x.emailSentBy.fold[Future[Option[String]]](Future.successful(None))(replaceId)
        extIds <- externalIds(x, networkProvider)
      } yield User.Secured(
        x.id,
        x.networkId,
        x.createdAt,
        replacedCreatedBy getOrElse s"guid:${x.createdBy}",
        x.updatedAt,
        replacedUpdatedBy getOrElse s"guid:${x.updatedBy}",
        x.emailSentAt,
        replacedEmailSentBy orElse x.emailSentBy,
        x.lastVisitedAt,
        x.email,
        x.firstName,
        x.lastName,
        extIds,
        x.tradewebEligible,
        x.regSEligible,
        x.roles,
        x.isActive,
        x.locations,
        x.faNumbers,
        x.custodianFaNumbers,
        x.customRoles,
        x.licenses,
        x.maskedIds,
        x.idpId,
        x.distributorInfo,
        x.accountInContext,
        x.context,
        x.cusips,
        x.loginMode,
        x.userType,
        x.landingPage)
    }

    def hasRole(role: UserRole): Boolean = x.roles contains role

    def inactive: User = x.copy(roles = Set.empty, isActive = false)

    private def externalIds(
        u: User,
        networkProvider: NetworkId => Operation[Network])
      (implicit ec: ExecutionContext): Future[Map[String, String]] = {

      val ids = for {
        network <- networkProvider(x.networkId)
        omsId <- externalIds(x.omsId, network.omsAlias.flatMap(OmsAlias.getByName))
        distributorId <- externalIds(x.distributorId, network.distributorAlias)
      } yield omsId ++ distributorId

      ids.future.flatMap {
        case Result.Ok(v) => Future.successful(v)
        case f: Result.Failure =>
          log.warn(s"Unable to retrieve a network by id='${u.networkId} to calculate externalIds', userId='${u.id}'. failureMessages='${f.messages}'")
          Future.successful(Map.empty)
      }
    }

    private def externalIds(
        id: Option[String],
        alias: Option[ExternalAlias]): Operation[Map[String, String]] = {

      (id, alias) match {
        case (Some(idVal), Some(aliasVal)) => Map(aliasVal.name -> idVal).ok.op
        case (_, _) => Map.empty[String, String].ok.op
      }
    }
  }
}