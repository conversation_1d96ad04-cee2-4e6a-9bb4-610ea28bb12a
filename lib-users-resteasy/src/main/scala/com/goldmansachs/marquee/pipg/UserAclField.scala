package com.goldmansachs.marquee.pipg

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues(
  "userId",
  "networkId",
  "lastVisitedAt",
  "email",
  "firstName",
  "lastName",
  "roles",
  "distributorId",
  "omsId",
  "tradewebEligible",
  "regSEligible",
  "isActive",
  "userPurviewIds",
  "issuerPurviewIds",
  "purviews",
  "approverMap",
  "dynamicRoles",
  "locations",
  "faNumbers",
  "custodianFaNumbers",
  "customRoles",
  "capabilities",
  "payoffEntitlements",
  "payoffEntitlementsV2",
  "networkInfo",
  "networkLocationHierarchy",
  "maskedIds",
  "ioiApproverSet",
  "networkTypes",
  "learnTracks",
  "learnTracksV2",
  "learnContent",
  "endUserShareableContent",
  "licenses",
  "siCertificationRequirements",
  "annuityCertificationRequirements",
  "definedOutcomeETFCertificationRequirements",
  "certificationProducts",
  "accountInContext",
  "context",
  "cusips",
  "group",
  "networkCapabilities",
  "payoffCertificationRequirementsList",
  "videoTracksEntitlements",
  "spCertificationRequirements",
  "altCertificationRequirements",
  "externalIds",
  "purviewLicenses",
  "purviewNsccCodes",
  "firmId",
  "whiteLabelPartnerId",
  "secondaryEmail",
  "iCapitalUserId",
  "groups",
  "icnGroups",
  "icnRoles",
  "passport",
  "productTypeCertificationRequirements"
)
sealed trait UserAclField extends EnumEntry

object UserAclField extends ProductEnums[UserAclField] {

  case object userId extends UserAclField

  case object networkId extends UserAclField

  case object lastVisitedAt extends UserAclField

  case object email extends UserAclField

  case object firstName extends UserAclField

  case object lastName extends UserAclField

  case object roles extends UserAclField

  case object distributorId extends UserAclField

  case object omsId extends UserAclField

  case object tradewebEligible extends UserAclField

  case object videoTracksEntitlements extends UserAclField

  case object regSEligible extends UserAclField

  case object isActive extends UserAclField

  case object userPurviewIds extends UserAclField

  case object issuerPurviewIds extends UserAclField

  case object purviews extends UserAclField

  case object approverMap extends UserAclField

  case object dynamicRoles extends UserAclField

  case object locations extends UserAclField

  case object faNumbers extends UserAclField

  case object custodianFaNumbers extends UserAclField

  case object customRoles extends UserAclField

  case object capabilities extends UserAclField

  case object payoffEntitlements extends UserAclField

  case object payoffEntitlementsV2 extends UserAclField

  case object networkInfo extends UserAclField

  case object networkLocationHierarchy extends UserAclField

  case object maskedIds extends UserAclField

  case object ioiApproverSet extends UserAclField

  case object networkTypes extends UserAclField

  case object learnTracks extends UserAclField

  case object learnTracksV2 extends UserAclField

  case object learnContent extends UserAclField

  case object endUserShareableContent extends UserAclField

  case object licenses extends UserAclField

  case object siCertificationRequirements extends UserAclField

  case object spCertificationRequirements extends UserAclField

  case object annuityCertificationRequirements extends UserAclField

  case object definedOutcomeETFCertificationRequirements extends UserAclField

  case object certificationProducts extends UserAclField

  case object payoffCertificationRequirementsList extends UserAclField

  case object accountInContext extends UserAclField

  case object context extends UserAclField

  case object cusips extends UserAclField

  case object group extends UserAclField

  case object networkCapabilities extends UserAclField

  case object smaStrategiesAndUnderliers extends UserAclField

  case object smaRestrictedIssuers extends UserAclField

  case object externalIds extends UserAclField

  case object altCertificationRequirements extends UserAclField

  case object purviewLicenses extends UserAclField

  case object purviewNsccCodes extends UserAclField

  case object firmId extends UserAclField

  case object whiteLabelPartnerId extends UserAclField

  case object secondaryEmail extends UserAclField

  case object iCapitalUserId extends UserAclField

  case object groups extends UserAclField

  case object icnGroups extends UserAclField

  case object icnRoles extends UserAclField

  case object passport extends UserAclField

  case object productTypeCertificationRequirements extends UserAclField

  override def Values: Seq[UserAclField] =
    Seq(
      userId,
      networkId,
      lastVisitedAt,
      email,
      firstName,
      lastName,
      roles,
      distributorId,
      omsId,
      tradewebEligible,
      regSEligible,
      isActive,
      userPurviewIds,
      issuerPurviewIds,
      purviews,
      approverMap,
      dynamicRoles,
      locations,
      faNumbers,
      custodianFaNumbers,
      customRoles,
      capabilities,
      payoffEntitlements,
      payoffEntitlementsV2,
      networkInfo,
      networkLocationHierarchy,
      maskedIds,
      ioiApproverSet,
      networkTypes,
      learnTracks,
      learnTracksV2,
      learnContent,
      endUserShareableContent,
      licenses,
      siCertificationRequirements,
      payoffCertificationRequirementsList,
      videoTracksEntitlements,
      spCertificationRequirements,
      annuityCertificationRequirements,
      definedOutcomeETFCertificationRequirements,
      certificationProducts,
      accountInContext,
      context,
      cusips,
      group,
      networkCapabilities,
      smaStrategiesAndUnderliers,
      smaRestrictedIssuers,
      altCertificationRequirements,
      externalIds,
      purviewLicenses,
      purviewNsccCodes,
      firmId,
      whiteLabelPartnerId,
      secondaryEmail,
      iCapitalUserId,
      groups,
      icnGroups,
      icnRoles,
      passport,
      productTypeCertificationRequirements
    )
}

