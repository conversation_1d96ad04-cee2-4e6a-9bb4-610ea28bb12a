package com.goldmansachs.marquee.pipg

import scala.util.matching.Regex

private[pipg] abstract class ReCompanion[T](re: Regex, maxLength: Int)(f: String => T) {

  object Valid {

    def apply(x: String): Option[T] = x match {
      case Valid(value) => Some(f(value))
      case _            => None
    }

    def either(x: Option[String], left: String => String): Either[String, Option[T]] =
      x match {
        case Some(Valid(value)) => Right(Some(f(value)))
        case Some(invalid)      => Left(left(invalid))
        case None               => Right(None)
      }

    def unapply(x: String): Option[String] = {
      x match {
        case re(value) if value.length <= maxLength => Some(x)
        case _                                      => None
      }
    }

    def unapply(x: Option[String]): Option[Option[String]] = {
      x map {
        case re(value) if value.length <= maxLength => Some(value)
        case _                                      => None
      }
    }

    def unapplySeq(x: String): Option[Seq[String]] = {
      x match {
        case re(value) if value.length <= maxLength => Some(Seq(x))
        case _                                      => None
      }
    }
  }
}
