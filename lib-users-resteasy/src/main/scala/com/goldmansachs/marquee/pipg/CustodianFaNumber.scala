package com.goldmansachs.marquee.pipg

import com.simonmarkets.util.{EnumEntry, SafeEnums}
import io.simon.openapi.annotation.ClassReference
import io.simon.openapi.annotation.Field.{EnumValues, Ref, Required}

/**
 * This is a case class that links a custodian to a FA Number, producing a unique primary key.
 *
  * @param custodian = the custodian of this FA Number
  * @param id = the actual FA Number
  */
final case class CustodianFaNumber(
    @Required
    @Ref(ClassReference(classOf[Custodian]))
    custodian: Custodian,

    @Required
    id: String
)

@EnumValues(
  "Cetera",
  "Pershing",
  "PAS",
  "NFS",
  "IWS",
  "Schwab",
  "TDAmeritrade",
  "FirstClearing",
  "WellsFargo",
  "RBC",
  "Folio",
  "Other"
)
sealed trait Custodian extends EnumEntry

object Custodian extends SafeEnums[Custodian] {
  case object Cetera extends Custodian
  case object Pershing extends Custodian
  case object PAS extends Custodian
  case object NFS extends Custodian
  case object I<PERSON> extends Custodian
  case object <PERSON><PERSON><PERSON> extends Custodian
  case object TDAmeritrade extends Custodian
  case object FirstClearing extends Custodian
  case object WellsFargo extends Custodian
  case object RBC extends Custodian
  case object Folio extends Custodian
  case object Other extends Custodian
  case object EnumNotFound extends Custodian

  override def Values: Seq[Custodian] = Seq(
    Cetera,
    Pershing,
    PAS,
    NFS,
    IWS,
    Schwab,
    TDAmeritrade,
    FirstClearing,
    WellsFargo,
    RBC,
    Folio,
    EnumNotFound
  )
}
