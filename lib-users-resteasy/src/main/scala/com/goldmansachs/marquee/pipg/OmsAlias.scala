package com.goldmansachs.marquee.pipg

//TODO ST Remove after onboarding tool changes
object OmsAlias {

  val TWD = ExternalAlias("twd", "^([0-9a-zA-Z]+![_\\-0-9a-zA-Z]+)$")
  val Firelight = ExternalAlias("firelight", "^[\\w\\- .&*\\\\]{1,64}$")

  val aliases     : Set[ExternalAlias]         = Set(TWD, Firelight)
  val aliasesNames: Set[String]                = aliases.map(_.name)

  def filterOmsAliases(ids: Map[String, String]): Map[String, String] = {
    ids.filterKeys(aliasesNames.contains)
  }

  def excludeOmsAliases(ids: Map[String, String]): Map[String, String] = {
    ids.filterKeys(!aliasesNames.contains(_))
  }

  def getByName(name: String): Option[ExternalAlias] = aliases.find(name == _.name)
}