package com.goldmansachs.marquee.pipg

import com.simonmarkets.networks.{ExternalId, SmaStrategyAndUnderliers}
import com.simonmarkets.resteasy.requests.UserPrincipal
import com.simonmarkets.shared.{MaskedId, ProductType}
import com.simonmarkets.users.common.Context
import simon.Id.NetworkId
import simon.authz.NewPurviewProductNetworkDynamicRole

import java.time.LocalDateTime

import scala.annotation.nowarn


/** Record of user and network fields
 *
 * @param userId       user id
 * @param roles        user roles
 * @param dynamicRoles all roles user has including user level dynamic roles
 *                     as well as corresponding network dynamic roles
 */
@nowarn("cat=deprecation")
case class UserACL(
    userId: String,
    networkId: NetworkId,
    lastVisitedAt: Option[LocalDateTime],
    email: String,
    firstName: String,
    lastName: String,
    roles: Set[UserRole] = Set.empty,
    distributorId: Option[String],
    omsId: Option[String],
    tradewebEligible: Boolean,
    regSEligible: Boolean,
    isActive: Option[Boolean],
    userPurviewIds: Set[NetworkId] = Set.empty,
    issuerPurviewIds: Set[IssuerPurview] = Set.empty,
    purviews: Option[Set[Purview]] = None,
    @deprecated("Please use ioiApproverSet")
    approverMap: ApproverMap = ApproverMap.empty,
    dynamicRoles: Set[String] = Set.empty,
    locations: Set[String] = Set.empty,
    faNumbers: Set[String] = Set.empty,
    custodianFaNumbers: Set[CustodianFaNumber] = Set.empty,
    customRoles: Set[String] = Set.empty,
    capabilities: Set[String] = Set.empty,
    payoffEntitlements: Map[String, Map[String, List[String]]] = Map.empty,
    payoffEntitlementsV2: Map[String, Map[String, Set[Network.Action]]] = Map.empty,
    networkInfo: Network.Info = Network.Info.empty,
    networkLocationHierarchy: Option[LocationNode] = None,
    maskedIds: Set[MaskedId] = Set.empty,
    @deprecated("Please use siCertificationRequirements")
    payoffCertificationRequirementsList: Seq[CertificationRequirementsForPayoff] = Seq.empty,
    @deprecated("This field has been deprecated in favor of learnTracksV2, learnContent")
    videoTracksEntitlements: Map[String, List[String]] = Map.empty,
    ioiApproverSet: Map[String, List[List[String]]] = Map.empty,
    networkTypes: Option[List[NetworkType]] = None,
    @deprecated("This field has been deprecated in favor of learnTracksV2", "86.0.0")
    learnTracks: Seq[String] = Seq.empty,
    learnTracksV2: Seq[LearnTrack] = Seq.empty,
    learnContent: Seq[String] = Seq.empty,
    endUserShareableContent: Seq[String] = Seq.empty,
    @deprecated("This field has been deprecated in favor of siCertificationRequirements", "88.0.0")
    spCertificationRequirements: Seq[CertificationRequirementsForPayoff] = Seq.empty,
    licenses: Set[License] = Set.empty,
    siCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    annuityCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    definedOutcomeETFCertificationRequirements: Seq[CertificationAlternativesForProduct] = Seq.empty,
    certificationProducts: Option[Seq[String]] = None,
    accountInContext: Option[String] = None,
    context: Option[Context] = None,
    cusips: Set[String] = Set.empty,
    group: Option[String] = None,
    networkCapabilities: Option[Map[String, List[String]]] = None,
    smaStrategiesAndUnderliers: Option[Set[SmaStrategyAndUnderliers]] = None,
    smaRestrictedIssuers: Option[Set[String]] = None,
    altCertificationRequirements: Option[Seq[CertificationAlternativesForProduct]] = None,
    externalIds: Option[Seq[ExternalId]] = None,
    purviewLicenses: Option[Set[License]] = None,
    purviewNsccCodes: Option[Set[String]] = None,
    firmId: Option[String] = None,
    whiteLabelPartnerId: Option[String] = None,
    secondaryEmail: Option[String] = None,
    iCapitalUserId: Option[String] = None,
    groups: Option[Map[String, Set[String]]] = None,
    icnGroups: Option[Set[String]] = None,
    icnRoles: Option[Set[String]] = None,
    passport: Option[Map[String, Int]] = None,
    productTypeCertificationRequirements: Option[Seq[ProductCertificationRequirements]] = None,
) extends UserPrincipal {

  lazy val allAccessibleLocations: Set[String] = {
    networkLocationHierarchy.map { root =>
      def children(node: LocationNode, parentIsAdded: Boolean = false): Set[String] = {
        if (locations.contains(node.name) || parentIsAdded)
          Set(node.name) ++ node.children.flatMap(children(_, parentIsAdded = true))
        else
          node.children.flatMap(children(_))
      }

      children(root)
    }.getOrElse(locations)
  }
}

object UserACL {

  def apply(u: User): UserACL = UserACL(
    userId = u.id,
    networkId = u.networkId,
    lastVisitedAt = u.lastVisitedAt,
    email = u.email,
    firstName = u.firstName,
    lastName = u.lastName,
    roles = u.roles,
    distributorId = u.distributorId,
    omsId = u.omsId,
    tradewebEligible = u.tradewebEligible,
    regSEligible = u.regSEligible,
    isActive = Some(u.isActive),
    dynamicRoles = u.dynamicRoles,
    locations = u.locations,
    faNumbers = u.faNumbers,
    custodianFaNumbers = u.custodianFaNumbers,
    customRoles = u.customRoles,
    networkInfo = Network.Info.empty,
    maskedIds = u.maskedIds,
    licenses = u.licenses,
    accountInContext = u.accountInContext,
    context = u.context,
    cusips = u.cusips,
    externalIds = noneIfEmpty(u.externalIds),
    purviewLicenses = noneIfEmpty(u.purviewLicenses),
    purviewNsccCodes = noneIfEmpty(u.purviewNsccCodes),
    firmId = u.firmId,
    whiteLabelPartnerId = u.whiteLabelPartnerId,
    secondaryEmail = u.secondaryEmail,
    iCapitalUserId = u.iCapitalUserId,
    groups = noneIfEmpty(u.groups),
    icnGroups = noneIfEmpty(u.icnGroups),
    icnRoles = noneIfEmpty(u.icnRoles),
    passport = noneIfEmpty(u.passport)
  )

  private def resolveCapabilities(customRoles: Set[String], isActive: Boolean, n: Network) = {
    if (isActive)
      n.customRolesConfig
        .collect { case roleDefinition if customRoles.contains(roleDefinition.role) => roleDefinition.capabilities }
        .flatten
    else Set.empty[String]
  }

  private def filterCertificationRequirementsByType(
      requirements: Option[Seq[ProductCertificationRequirements]],
      productType: ProductType
  ): Seq[CertificationAlternativesForProduct] = {
    requirements match {
      case None => Seq.empty
      case Some(requirements) => requirements.filter(_.productType == productType).flatMap(_.certificationRequirements)
    }
  }

  private def generatedApproveDynamicRoles(user: User, network: Network): Set[String] = {
    if (user.roles.contains(UserRole.EqPIPGFAManager)) {
      val res = network.purviewNetworks.map { networkPurview =>
        def productKey(networkId: NetworkId, issuer: String) = s"purview:product:$networkId:$issuer:role:${UserRole.EqPIPGFAManager.name}"

        networkPurview.foldLeft(Set.empty[String])((acc, issuerPurview) => {
          val productRoles = issuerPurview.issuers.map(issuer => productKey(issuerPurview.network, issuer))
          acc ++ productRoles
        })
      }
      res.getOrElse(Set.empty)
    } else Set.empty
  }

  private def generatedPurviewDynamicRoles(user: User, network: Network): Set[String] = {
    (user.roles & UserRole.purviewRoles).flatMap { role =>
      val res = network.purviewNetworks.map { networkPurview =>
        networkPurview.foldLeft(Set.empty[String])((acc, issuerPurview) => {
          val productRoles = issuerPurview.issuers.map(issuer => NewPurviewProductNetworkDynamicRole(issuerPurview.network, issuer, role))
          acc ++ productRoles
        })
      }
      res.getOrElse(Set.empty)

    }
  }

  private def noneIfEmpty[C <: Traversable[_]](col: C): Option[C] = if (col.isEmpty) None else Some(col)

  def apply(u: User, n: Network): UserACL = {
    require(u.networkId == n.id)

    UserACL(
      userId = u.id,
      networkId = u.networkId,
      lastVisitedAt = u.lastVisitedAt,
      email = u.email,
      firstName = u.firstName,
      lastName = u.lastName,
      roles = u.roles,
      distributorId = u.distributorId,
      omsId = u.omsId,
      tradewebEligible = u.tradewebEligible,
      regSEligible = u.regSEligible,
      isActive = Some(u.isActive),
      approverMap = ApproverMap.fromRaw(n.approverSet),
      userPurviewIds = n.purviewNetworks.map(pn => pn.map(_.network)) getOrElse Set.empty,
      issuerPurviewIds = n.purviewNetworks getOrElse Set.empty,
      purviews = n.purviews,
      dynamicRoles = u.dynamicRoles ++ n.dynamicRoles ++ generatedApproveDynamicRoles(u, n) ++ generatedPurviewDynamicRoles(u, n),
      locations = u.locations,
      faNumbers = u.faNumbers,
      custodianFaNumbers = u.custodianFaNumbers,
      customRoles = u.customRoles,
      capabilities = resolveCapabilities(u.customRoles, u.isActive, n),
      payoffEntitlements = n.payoffEntitlements,
      payoffEntitlementsV2 = n.payoffEntitlementsV2,
      networkInfo = n.info,
      networkLocationHierarchy = n.locationHierarchy,
      maskedIds = u.maskedIds,
      ioiApproverSet = n.ioiApproverSet,
      networkTypes = n.networkTypes.map(_.map(networkType => {
        //TODO: APPSERV-62935 remove conversion. Temp hack that converts Admin networkType to SMAManager networkType
        if (networkType == NetworkType.Admin) NetworkType.SMAManager else networkType
      })),
      learnTracks = n.learnTracks,
      learnTracksV2 = n.learnTracksV2,
      learnContent = n.learnContent,
      endUserShareableContent = n.endUserShareableContent,
      licenses = u.licenses,
      siCertificationRequirements = filterCertificationRequirementsByType(n.productTypeCertificationRequirements, ProductType.StructuredProduct),
      annuityCertificationRequirements = filterCertificationRequirementsByType(n.productTypeCertificationRequirements, ProductType.Annuities),
      definedOutcomeETFCertificationRequirements = filterCertificationRequirementsByType(n.productTypeCertificationRequirements, ProductType.TargetReturnETF),
      certificationProducts = n.certificationProducts,
      accountInContext = u.accountInContext,
      context = u.context,
      cusips = u.cusips,
      group = n.group,
      networkCapabilities = Some(n.capabilities),
      smaStrategiesAndUnderliers = Some(n.smaStrategiesAndUnderliers),
      smaRestrictedIssuers = Some(n.smaRestrictedIssuers),
      altCertificationRequirements = noneIfEmpty(filterCertificationRequirementsByType(n.productTypeCertificationRequirements, ProductType.AlternativeInvestment)),
      externalIds = Some(u.externalIds),
      purviewLicenses = noneIfEmpty(u.purviewLicenses),
      purviewNsccCodes = noneIfEmpty(u.purviewNsccCodes),
      firmId = u.firmId,
      whiteLabelPartnerId = u.whiteLabelPartnerId,
      secondaryEmail = u.secondaryEmail,
      iCapitalUserId = u.iCapitalUserId,
      groups = noneIfEmpty(u.groups),
      icnGroups = noneIfEmpty(u.icnGroups),
      icnRoles = noneIfEmpty(u.icnRoles),
      passport = noneIfEmpty(u.passport),
      productTypeCertificationRequirements = n.productTypeCertificationRequirements
    )
  }

  implicit class UserACLOps(val x: UserACL) extends AnyVal {

    def active: Boolean = x.roles.nonEmpty

    def isAdmin: Boolean = x.roles.contains(UserRole.EqPIPGGSAdmin)

    def isManager: Boolean = x.roles.contains(UserRole.EqPIPGFAManager)

    def fullName: String = s"${x.firstName} ${x.lastName}".trim

    def nameReversed: String = s"${x.lastName} ${x.firstName}".trim

    def hasRole(role: UserRole): Boolean = x.roles.contains(role)

    def hasAdmin: Boolean = x.hasRole(UserRole.EqPIPGGSAdmin)

    def hasFinancialAdviserManager: Boolean = x.hasRole(UserRole.EqPIPGFAManager)

    def hasFinancialAdviser: Boolean = x.hasRole(UserRole.EqPIPGFA)

    def hasPrivateBank: Boolean = x.hasRole(UserRole.EqPIPGPB)

    def hasDeveloper: Boolean = x.hasRole(UserRole.EqPIPGDeveloper)

    def hasSecondaryTrader: Boolean = x.hasRole(UserRole.EqPIPGSecondaryTrader)

    def hasAuctionMonitor: Boolean = x.hasRole(UserRole.EqPIPGAuctionMonitor)

    def hasSystem: Boolean = x.hasRole(UserRole.EqPIPGSIMONSystemUser)

    def hasTradewebSystem: Boolean = x.hasRole(UserRole.EqPIPGTwdSystemUser)

    def hasDataAdmin: Boolean = x.hasRole(UserRole.EqPIPGDataAdmin)

    def hasHealthCheck: Boolean = x.hasRole(UserRole.EqPIPGHealthcheck)

    def hasIssuer: Boolean = x.hasRole(UserRole.Issuer)

    def hasWholesaler: Boolean = x.hasRole(UserRole.Wholesaler)

    def canViewMultipleNetworks: Boolean = hasWholesaler || hasIssuer || hasAdmin

    def hasUserPurview(networkId: NetworkId): Boolean = x.userPurviewIds.contains(networkId)

    def hasRIANetworkType: Boolean = x.networkTypes.exists(types => types.contains(NetworkType.RIA))

    def hasIMONetworkType: Boolean = x.networkTypes.exists(types => types.contains(NetworkType.Imo))

    def canBuildAnyPayoff: Boolean = canDoPayOffAction("build")

    def canBacktestAnyPayOff: Boolean = canDoPayOffAction("backtest")

    def canEditAnyPayOff: Boolean = canDoPayOffAction("edit")

    def canDetailsAnyPayOff: Boolean = canDoPayOffAction("details")

    private def canDoPayOffAction(action: String): Boolean =
      x.payoffEntitlementsV2.values.flatMap(_.values).flatten.exists(_.action == action) ||
        x.payoffEntitlements.values.flatMap(_.values).flatten.exists(_ == action)

    def hasIssuerPurview(networkId: NetworkId, issuerSymbol: String): Boolean = {
      x.issuerPurviewIds.find(_.network == networkId).exists(ip => ip.issuers.contains(issuerSymbol))
    }

    def issuerPurviewNetworks(issuerSymbol: String): Set[NetworkId] = {
      x.issuerPurviewIds.foldLeft(Set.empty[NetworkId]) { (acc, ip) =>
        if (ip.issuers.contains(issuerSymbol)) acc + ip.network else acc
      }
    }

    def issuerPurviewNetworks(issuerSymbolOpt: Option[String]): Set[NetworkId] = {
      issuerSymbolOpt map issuerPurviewNetworks getOrElse Set.empty
    }

    def locationsTree: Set[String] = {
      x.locations //TODO(Shay): Parse subtrees for each root once Locations Service is implemented
    }
  }
}
