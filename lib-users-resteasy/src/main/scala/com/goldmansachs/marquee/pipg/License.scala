package com.goldmansachs.marquee.pipg

import com.goldmansachs.marquee.pipg.LicenseType.{Annuities, Securities}
import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.ClassReference
import io.simon.openapi.annotation.Field._

@EnumValues("Securities", "Annuities")
sealed trait LicenseType extends EnumEntry {
  def name: String = productPrefix
}

object LicenseType extends ProductEnums[LicenseType] {

  case object Securities extends LicenseType
  case object Annuities extends LicenseType

  override def Values: Seq[LicenseType] = List(
    Securities,
    Annuities
  )
}

@EnumValues("CRD", "NPN", "StateProducer")
sealed trait LicenseName extends EnumEntry {
  def name: String = productPrefix
  def licenseType: LicenseType
}

object LicenseName extends ProductEnums[LicenseName] {

  case object CRD extends LicenseName { val licenseType: LicenseType = Securities }
  case object NPN extends LicenseName { val licenseType: LicenseType = Annuities }
  case object StateProducer extends LicenseName { val licenseType: LicenseType = Annuities }

  override def Values: Seq[LicenseName] = List(
    CRD,
    NPN,
    StateProducer
  )
}

case class License(

    @Required
    @Ref(ClassReference(classOf[LicenseName]))
    name: LicenseName,

    @Required
    @Pattern("^[\\w-]{1,128}$")
    number: String,

    @Required
    @Ref(ClassReference(classOf[LicenseType]))
    `type`: LicenseType,

    @Ref(ClassReference(classOf[AcordState]))
    state: Option[AcordState]
)

object License {
  def NPN(id: String): License = License(LicenseName.NPN, id, LicenseName.NPN.licenseType, None)
  def CRD(id: String): License = License(LicenseName.CRD, id, LicenseName.CRD.licenseType, None)
  def StateProducer(id: String, state: AcordState): License =
    License(LicenseName.StateProducer, id, LicenseName.StateProducer.licenseType, Some(state))
}


@EnumValues("AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE",
  "DC", "FL", "GA", "GU", "HI", "ID", "IL", "IN",
  "IA", "KS", "KY", "LA", "ME", "MD", "MA", "MI",
  "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
  "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA",
  "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT",
  "VI", "VA", "WA", "WV", "WI", "WY")
sealed trait AcordState extends EnumEntry {
  def name: String = productPrefix
  def code: Int
  def fullName: String
}

object AcordState extends ProductEnums[AcordState] {

  case object AL extends AcordState { val code = 1; val fullName = "Alabama" }
  case object AK extends AcordState { val code = 2; val fullName = "Alaska" }
  case object AZ extends AcordState { val code = 4; val fullName = "Arizona" }
  case object AR extends AcordState { val code = 5; val fullName = "Arkansas" }
  case object CA extends AcordState { val code = 6; val fullName = "California" }
  case object CO extends AcordState { val code = 7; val fullName = "Colorado" }
  case object CT extends AcordState { val code = 8; val fullName = "Connecticut" }
  case object DE extends AcordState { val code = 9; val fullName = "Delaware" }
  case object DC extends AcordState { val code = 10; val fullName = "District of Columbia" }
  case object FL extends AcordState { val code = 12; val fullName = "Florida" }
  case object GA extends AcordState { val code = 13; val fullName = "Georgia"}
  case object GU extends AcordState { val code = 14; val fullName = "Guam"}
  case object HI extends AcordState { val code = 15; val fullName = "Hawaii" }
  case object ID extends AcordState { val code = 16; val fullName = "Idaho" }
  case object IL extends AcordState { val code = 17; val fullName = "Illinois" }
  case object IN extends AcordState { val code = 18; val fullName = "Indiana" }
  case object IA extends AcordState { val code = 19; val fullName = "Iowa" }
  case object KS extends AcordState { val code = 20; val fullName = "Kansas" }
  case object KY extends AcordState { val code = 21; val fullName = "Kentucky" }
  case object LA extends AcordState { val code = 22; val fullName = "Louisiana" }
  case object ME extends AcordState { val code = 23; val fullName = "Maine" }
  case object MD extends AcordState { val code = 25; val fullName = "Maryland" }
  case object MA extends AcordState { val code = 26; val fullName = "Massachusetts" }
  case object MI extends AcordState { val code = 27; val fullName = "Michigan" }
  case object MN extends AcordState { val code = 28; val fullName = "Minnesota" }
  case object MS extends AcordState { val code = 29; val fullName = "Mississippi" }
  case object MO extends AcordState { val code = 30; val fullName = "Missouri" }
  case object MT extends AcordState { val code = 31; val fullName = "Montana" }
  case object NE extends AcordState { val code = 32; val fullName = "Nebraska" }
  case object NV extends AcordState { val code = 33; val fullName = "Nevada" }
  case object NH extends AcordState { val code = 34; val fullName = "New Hampshire" }
  case object NJ extends AcordState { val code = 35; val fullName = "New Jersey" }
  case object NM extends AcordState { val code = 36; val fullName = "New Mexico" }
  case object NY extends AcordState { val code = 37; val fullName = "New York" }
  case object NC extends AcordState { val code = 38; val fullName = "North Carolina" }
  case object ND extends AcordState { val code = 39; val fullName = "North Dakota" }
  case object OH extends AcordState { val code = 41; val fullName = "Ohio" }
  case object OK extends AcordState { val code = 42; val fullName = "Oklahoma" }
  case object OR extends AcordState { val code = 43; val fullName = "Oregon" }
  case object PA extends AcordState { val code = 45; val fullName = "Pennsylvania" }
  case object PR extends AcordState { val code = 46; val fullName = "Puerto Rico" }
  case object RI extends AcordState { val code = 47; val fullName = "Rhode Island" }
  case object SC extends AcordState { val code = 48; val fullName = "South Carolina" }
  case object SD extends AcordState { val code = 49; val fullName = "South Dakota" }
  case object TN extends AcordState { val code = 50; val fullName = "Tennessee" }
  case object TX extends AcordState { val code = 51; val fullName = "Texas" }
  case object UT extends AcordState { val code = 52; val fullName = "Utah" }
  case object VT extends AcordState { val code = 53; val fullName = "Vermont" }
  case object VI extends AcordState { val code = 54; val fullName = "Virgin Islands" }
  case object VA extends AcordState { val code = 55; val fullName = "Virginia" }
  case object WA extends AcordState { val code = 56; val fullName = "Washington" }
  case object WV extends AcordState { val code = 57; val fullName = "West Virginia" }
  case object WI extends AcordState { val code = 58; val fullName = "Wisconsin" }
  case object WY extends AcordState { val code = 59; val fullName = "Wyoming" }

  lazy val Values: List[AcordState] = List(
    AL,
    AK,
    AZ,
    AR,
    CA,
    CO,
    CT,
    DE,
    DC,
    FL,
    GA,
    GU,
    HI,
    ID,
    IL,
    IN,
    IA,
    KS,
    KY,
    LA,
    ME,
    MD,
    MA,
    MI,
    MN,
    MS,
    MO,
    MT,
    NE,
    NV,
    NH,
    NJ,
    NM,
    NY,
    NC,
    ND,
    OH,
    OK,
    OR,
    PA,
    PR,
    RI,
    SC,
    SD,
    TN,
    TX,
    UT,
    VT,
    VI,
    VA,
    WA,
    WV,
    WI,
    WY
  )

  lazy val AcordNameToAccordMap:Map[String, AcordState] =
    Values.foldLeft(Map[String, AcordState]()) { (map, state) =>
      map + (state.fullName -> state)
    }
}