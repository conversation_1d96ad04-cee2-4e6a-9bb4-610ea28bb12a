package com.goldmansachs.marquee.pipg

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues

import scala.collection.TraversableOnce

/** List of available roles
  *
  * - EqPIPGDeveloper
  * - EqPIPGFA
  * - EqPIPGFAManager
  * - EqPIPGGSAdmin
  * - EqPIPGPB
  * - DataUpdateUser
  * - EqPIPGTwdSystemUser
  * - EqPIPGDataAdmin
  * - EqPIPGHealthcheck
  * - EqPIPGAuctionAdmin
  * - EqPIPGAuctionMonitor
  * - EqPIPGHedgeProvider
  * - EqPIPGSIMONSystemUser
  * - EqPIPGSecondaryTrader
  * - Issuer
  * - Wholesaler
  */
@EnumValues(
  "EqPIPGDeveloper",
  "EqPIPGFA",
  "EqPIPGFAManager",
  "EqPIPGGSAdmin",
  "EqPIPGPB",
  "EqPIPGSecondaryTrader",
  "Issuer",
  "Wholesaler",
  "DataUpdateUser",
  "EqPIPGTwdSystemUser",
  "EqPIPGDataAdmin",
  "EqPIPGHealthcheck",
  "EqPIPGAuctionAdmin",
  "EqPIPGAuctionMonitor",
  "EqPIPGHedgeProvider",
  "EqPIPGSIMONSystemUser")
sealed trait UserRole extends EnumEntry {

  def primary: Boolean = false

  def name: String = productPrefix
}

trait Primary { this: UserRole =>

  override def primary: Boolean = true
}

trait HasLongName { this: UserRole =>

  def longName: String
}

object UserRole extends ProductEnums[UserRole] {

  case object EqPIPGGSAdmin         extends UserRole with Primary with HasLongName { val longName = "Admin" }

  case object EqPIPGFAManager       extends UserRole with Primary with HasLongName { val longName = "Manager" }

  case object EqPIPGFA              extends UserRole with Primary with HasLongName { val longName = "Financial Advisor" }

  case object EqPIPGPB              extends UserRole with Primary with HasLongName { val longName = "Private Bank" }

  case object EqPIPGSecondaryTrader extends UserRole with Primary with HasLongName { val longName = "Secondary Trader" }

  case object Issuer                extends UserRole with Primary with HasLongName { val longName = "Issuer" }

  case object Wholesaler            extends UserRole with Primary with HasLongName { val longName = "Wholesaler" }

  case object EqPIPGDeveloper       extends UserRole

  case object DataUpdateUser        extends UserRole

  case object EqPIPGTwdSystemUser   extends UserRole

  case object EqPIPGDataAdmin       extends UserRole

  case object EqPIPGHealthcheck     extends UserRole

  case object EqPIPGAuctionAdmin    extends UserRole with HasLongName { val longName = "Auction Admin" }

  case object EqPIPGAuctionMonitor  extends UserRole

  case object EqPIPGHedgeProvider   extends UserRole

  case object EqPIPGSIMONSystemUser extends UserRole

  val Values: Seq[UserRole] = Seq(
    EqPIPGDeveloper,
    EqPIPGFA,
    EqPIPGFAManager,
    EqPIPGGSAdmin,
    EqPIPGPB,
    EqPIPGSecondaryTrader,
    Issuer,
    Wholesaler,
    DataUpdateUser,
    EqPIPGTwdSystemUser,
    EqPIPGDataAdmin,
    EqPIPGHealthcheck,
    EqPIPGAuctionAdmin,
    EqPIPGAuctionMonitor,
    EqPIPGHedgeProvider,
    EqPIPGSIMONSystemUser)

  val PrimaryOrdered: List[UserRole with Primary with HasLongName] = List(
    EqPIPGGSAdmin,
    EqPIPGFAManager,
    EqPIPGFA,
    Issuer,
    Wholesaler,
    EqPIPGPB,
    EqPIPGSecondaryTrader)

  val purviewRoles: Set[UserRole] = Set(
    EqPIPGFAManager,
    Issuer,
    Wholesaler
  )

  def sequence(from: TraversableOnce[String]): Option[Set[UserRole]] = {
    val roles = {from.toIterator flatMap UserRole.unapply}.toSet
    if (roles.isEmpty) None else Some(roles)
  }
}