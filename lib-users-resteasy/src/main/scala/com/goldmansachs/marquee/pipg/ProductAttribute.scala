package com.goldmansachs.marquee.pipg

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait ProductAttribute extends EnumEntry
object ProductAttribute extends ProductEnums[ProductAttribute] {
  case object Default extends ProductAttribute
  case object Category extends ProductAttribute
  case object Strategy extends ProductAttribute
  case object ProductId extends ProductAttribute

  override def Values: Seq[ProductAttribute] = List(Default, Category, Strategy, ProductId)

  @EnumValues("Default", "Category", "Strategy", "ProductId")
  case object Ref extends Reference
}
