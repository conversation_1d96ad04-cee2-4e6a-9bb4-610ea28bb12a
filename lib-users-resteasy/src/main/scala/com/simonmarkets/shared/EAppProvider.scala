package com.simonmarkets.shared

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues

@EnumValues("Firelight", "Ebix", "RJPipeline", "FIDx")
sealed trait EAppProvider extends EnumEntry

object EAppProvider extends ProductEnums[EAppProvider] {

  case object Firelight extends EAppProvider
  case object Ebix extends EAppProvider
  case object <PERSON>JPipeline extends EAppProvider
  case object FIDx extends EAppProvider

  override def Values: Seq[EAppProvider] = Seq(Firelight, Ebix, RJPipeline, FIDx)
}
