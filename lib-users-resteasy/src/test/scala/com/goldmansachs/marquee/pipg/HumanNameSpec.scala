package com.goldmansachs.marquee.pipg

import org.scalatest.{Matchers, WordSpec}


class HumanNameSpec extends WordSpec with Matchers {

  "HumanName" can {

    "resolve valid names" in {
      HumanName.Valid("<PERSON>").get.value shouldBe "Maria"
      HumanName.Valid("<PERSON>").get.value shouldBe "<PERSON>"
      HumanName.Valid("<PERSON>").get.value shouldBe "<PERSON>"
      HumanName.Valid("<PERSON>").get.value shouldBe "Müller"
      HumanName.Valid("<PERSON><PERSON>").get.value shouldBe "Heß"
      HumanName.Valid("Václav").get.value shouldBe "Václav"
      HumanName.Valid("Vojt<PERSON><PERSON>").get.value shouldBe "Vojtěch"
    }

    "resolve valid test names" in {
      HumanName.Valid("marquee-test-pipg-user1-pb1").get.value shouldBe "marquee-test-pipg-user1-pb1"
      HumanName.Valid("marquee-test-pipg-fa2-bd1").get.value shouldBe "marquee-test-pipg-fa2-bd1"
    }

    "not resolve empty string" in { HumanName.Valid("") should not be 'defined }

    "not resolve invalid" in {
      HumanName.Valid("#$%") should not be 'defined
    }
  }
}
