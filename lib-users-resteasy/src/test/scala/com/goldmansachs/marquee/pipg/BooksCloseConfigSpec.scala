package com.goldmansachs.marquee.pipg

import com.gs.marquee.util.DateTimes
import com.simonmarkets.asset.Region
import org.joda.time.LocalDate
import org.scalatest.{Matchers, WordSpec}

class BooksCloseConfigSpec extends WordSpec with Matchers {

  val Americas = BooksCloseConfig(Region.Americas, 0, 14, 0)
  val Europe = BooksCloseConfig(Region.Europe, 0, 10, 0)
  val Asia = BooksCloseConfig(Region.Asia, -1, 0, 0)
  val list = List(Americas, Europe, Asia)
  val region = Set(Region.Asia, Region.Americas)

  "BooksCloseForm" should {
    "return last business day before weekends when trade date is next day to weekend" in {
      val tradeDateAfterWeekends = new LocalDate("2020-5-18") //Monday
      val lastDayBeforeWeekends = Some(
        new LocalDate("2020-5-15").
          toDateTimeAtStartOfDay(DateTimes.NYTimeZone).
          withTime(0, 0, 0, 0))
      BooksCloseConfig.BooksCloseConfigOps(list).booksCloseDateFrom(tradeDateAfterWeekends, region) shouldBe lastDayBeforeWeekends
    }

    "return last business day before holidays when trade date is on holiday" in {
      val tradeDateOnHoliday = new LocalDate("2020-5-25") //Memorial Day
      val lastDayBeforeHolidays = Some(
        new LocalDate("2020-5-22").
          toDateTimeAtStartOfDay(DateTimes.NYTimeZone).
          withTime(0, 0, 0, 0))
      BooksCloseConfig.BooksCloseConfigOps(list).booksCloseDateFrom(tradeDateOnHoliday, region) shouldBe lastDayBeforeHolidays
    }

    "return last business day before holidays when trade date is next day to holiday" in {
      val tradeDateAfterHoliday = new LocalDate("2020-5-26") //Next to Memorial Day
      val lastDayBeforeHolidays = Some(
        new LocalDate("2020-5-22").
          toDateTimeAtStartOfDay(DateTimes.NYTimeZone).
          withTime(0, 0, 0, 0))
      BooksCloseConfig.BooksCloseConfigOps(list).booksCloseDateFrom(tradeDateAfterHoliday, region) shouldBe lastDayBeforeHolidays
    }
  }

}
