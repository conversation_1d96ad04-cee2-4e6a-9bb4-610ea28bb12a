package com.simonmarkets.users

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.User
import com.goldmansachs.marquee.pipg.service.user.UsersMsg.{Insert, Update}
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.{FutureHttpClient, HttpClientConfig}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.resteasy.authn.config.SourceConfig
import com.typesafe.config.Config
import io.circe.{Encoder, Json}
import pureconfig.generic.FieldCoproductHint
import pureconfig.generic.auto._

import scala.concurrent.{ExecutionContext, Future}

case class OAuthClientConfig(
    httpClient: HttpClientConfig,
    apiPrefix: String
)

object OAuthClientConfig {

  implicit val sourceConfigHint: FieldCoproductHint[SourceConfig] = new FieldCoproductHint[SourceConfig](key = "type")

  def apply(config: Config): OAuthClientConfig = pureconfig.loadConfigOrThrow[OAuthClientConfig](config)
}

object HttpOAuthClient {
  def apply(config: OAuthClientConfig)
    (implicit ec: ExecutionContext, mat: Materializer, as: ActorSystem): HttpOAuthClient = {
    val client = new FutureHttpClient(Http(), config.httpClient)
    new HttpOAuthClient(client, config.apiPrefix)
  }
}

trait OAuthClient {

}

class HttpOAuthClient(client: FutureHttpClient, path: String)(implicit ec: ExecutionContext, mat: Materializer)
  extends OAuthClient with JsonCodecs with TraceLogging {

}
