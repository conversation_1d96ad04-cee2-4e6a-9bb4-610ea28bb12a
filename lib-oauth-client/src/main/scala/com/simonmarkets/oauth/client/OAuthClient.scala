package com.simonmarkets.users

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.{FutureHttpClient, HttpClientConfig}
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.resteasy.authn.config.SourceConfig
import com.typesafe.config.Config
import pureconfig.generic.FieldCoproductHint

import scala.concurrent.ExecutionContext

case class OAuthClientConfig(
    httpClient: HttpClientConfig,
    apiPrefix: String
)

object OAuthClientConfig {

  implicit val sourceConfigHint: FieldCoproductHint[SourceConfig] = new FieldCoproductHint[SourceConfig](key = "type")

  def apply(config: Config): OAuthClientConfig = pureconfig.loadConfigOrThrow[OAuthClientConfig](config)
}


trait OAuthClient {

}

class HttpOAuthClient(client: FutureHttpClient, path: String)(implicit ec: ExecutionContext, mat: Materializer)
  extends OAuthClient with JsonCodecs with TraceLogging {

}
