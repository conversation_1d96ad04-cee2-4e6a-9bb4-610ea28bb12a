package com.simonmarkets.oauth

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.TraceLogging
import com.simonmarkets.users.{HttpOAuthClient, OAuthClient, OAuthClientConfig}

import scala.concurrent.ExecutionContext


object AddeparOAuthClient {
  def apply(config: OAuthClientConfig)
    (implicit ec: ExecutionContext, mat: Materializer, as: ActorSystem): AddeparOAuthClient = {
    val client = new FutureHttpClient(Http(), config.httpClient)
    new AddeparOAuthClient(client, config.apiPrefix)
  }
}

class AddeparOAuthClient(client: FutureHttpClient, path: String)(implicit ec: ExecutionContext, mat: Materializer)
  extends OAuthClient with JsonCodecs with TraceLogging {

}
