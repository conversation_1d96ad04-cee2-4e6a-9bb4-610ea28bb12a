package com.simonmarkets.sales.fee.rules.common.repository

import com.mongodb.client.model.{FindOneAndReplaceOptions, ReturnDocument}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.networks.common.{ChangelogItem, EntitySnapshot}
import com.simonmarkets.sales.fee.rules.common.SalesFeeRule
import com.simonmarkets.sales.fee.rules.common.api.FeeRuleNotFoundException
import com.simonmarkets.sales.fee.rules.common.encoders.{SalesFeeRuleFormat, SalesFeeRuleSnapshotFormat}
import org.mongodb.scala.bson.collection.immutable.Document
import org.mongodb.scala.model.Filters._
import org.mongodb.scala.model.Sorts.descending
import org.mongodb.scala.model.Updates.{combine, set}
import org.mongodb.scala.{ClientSession, MongoClient, MongoCollection, ReadConcern, ReadPreference, TransactionOptions, WriteConcern, _}

import java.time.LocalDateTime
import java.util.UUID

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class MongoSalesFeeRuleRepository(
  salesFeeRuleCollection: MongoCollection[Document],
  snapCollection: MongoCollection[Document],
  mongoClient: MongoClient) extends SalesFeeRuleRepository with TraceLogging {

  override def findAll: Future[Iterable[SalesFeeRule]] = {
    salesFeeRuleCollection.find(equal(SalesFeeRuleFormat.Fields.Deleted, false))
      .toFuture()
      .map(_.map(documentToSalesFeeRule))
  }

  override def findById(id: String): Future[Option[SalesFeeRule]] = {
    salesFeeRuleCollection.find(equal(SalesFeeRuleFormat.Fields.SalesFeeRuleId, id))
      .limit(1)
      .headOption
      .map(_.map(documentToSalesFeeRule))
  }

  override def findAllById(ids: Seq[String]): Future[List[SalesFeeRule]] =
    {
      salesFeeRuleCollection.find(and(
        in(SalesFeeRuleFormat.Fields.SalesFeeRuleId, ids: _*),
          equal(SalesFeeRuleFormat.Fields.Deleted, false)
      ))
        .toFuture()
        .map(_.map(documentToSalesFeeRule).toList)
    }

  override def delete(id: String): Future[Boolean] = {
    salesFeeRuleCollection.updateOne(and(
      equal(SalesFeeRuleFormat.Fields.SalesFeeRuleId, id),
      equal(SalesFeeRuleFormat.Fields.Deleted, false)),
      combine(
        set("deleted", true)
      )).toFuture().map(r => r.getModifiedCount == 1)
  }

  override def insertAndSnap(feeRule: SalesFeeRule,
      userId: String, comment: Option[String], retries: Int)(implicit
      traceId: TraceId): Future[SalesFeeRule] = {
    for {
      clientSession <- mongoClient.startSession().toFuture
      _ = clientSession.startTransaction(transactionOptions)
      entity <- salesFeeRuleCollection.insertOne(clientSession, salesFeeRuleToDocument(feeRule)).toFuture().map(_ => feeRule)
      _ = log.info("...finished inserting entity", entity.id)
      snap <- insertSnapshot(clientSession, userId, comment, feeRule)
      _ = log.info("...finished inserting snapshot", snap.id)
      committed <- clientSession.commitTransaction().toSingle.toFuture
      _ = log.info(s"...$committed")
    } yield entity
  }

  override def updateAndSnap(feeRule: SalesFeeRule, userId: String, comment: Option[String], retries: Int)(implicit traceId: TraceId): Future[SalesFeeRule] = {
    val ruleToUpdate = feeRule.increaseVersion

    for {
      clientSession <- mongoClient.startSession().toFuture
      _ = clientSession.startTransaction(transactionOptions)
      entity <- salesFeeRuleCollection.findOneAndReplace(
        clientSession,
        and(equal(SalesFeeRuleFormat.Fields.SalesFeeRuleId, feeRule.id), equal(SalesFeeRuleFormat.Fields.Version, feeRule.version)),
        salesFeeRuleToDocument(ruleToUpdate),
        new FindOneAndReplaceOptions().returnDocument(ReturnDocument.AFTER)
      ).toFuture.map(doc => Option(doc) match {
        case None => throw FeeRuleNotFoundException(s"Fee rule does not exist, id = ${feeRule.id}")
        case Some(doc) => documentToSalesFeeRule(doc)
      })
      _ = log.info("...finished updating entity", entity.id)
      snap <- insertSnapshot(clientSession, userId, comment, ruleToUpdate)
      _ = log.info("...finished inserting snapshot", snap.id)
      committed <- clientSession.commitTransaction().toSingle.toFuture
      _ = log.info(s"...$committed")
    } yield entity
  }

  private def insertSnapshot(clientSession: ClientSession, userId: String, comment: Option[String], feeRule: SalesFeeRule): Future[EntitySnapshot[SalesFeeRule]] = {
    val snapshot = EntitySnapshot(
      id = UUID.randomUUID.toString,
      userId = userId,
      modificationDate = LocalDateTime.now(),
      comment = comment,
      entity = feeRule
    )
    snapCollection.insertOne(clientSession, SalesFeeRuleSnapshotFormat.write(snapshot)).toFuture().map(_ => snapshot)
  }

  override def getChangelogs(ruleId: String): Future[List[ChangelogItem]] = {
    snapCollection.find(and(equal("entity.id", ruleId)))
      .sort(descending(SalesFeeRuleSnapshotFormat.Fields.ModificationDate))
      .toFuture
      .map(_.map(SalesFeeRuleSnapshotFormat.readChangelogItem).toList)
  }

  override def getSnapshotEntity(snapshotId: String): Future[Option[SalesFeeRule]] = {
    snapCollection.find(and(equal(SalesFeeRuleSnapshotFormat.Fields.SnapshotId, snapshotId)))
      .limit(1)
      .headOption()
      .map(_.map( s => SalesFeeRuleSnapshotFormat.read(s).entity))
  }

  private def documentToSalesFeeRule(doc: Document) = SalesFeeRuleFormat.read(doc)

  private def salesFeeRuleToDocument(feeRule: SalesFeeRule) = SalesFeeRuleFormat.write(feeRule)

  private def transactionOptions = TransactionOptions.builder()
    .readPreference(ReadPreference.primary())
    .readConcern(ReadConcern.SNAPSHOT)
    .writeConcern(WriteConcern.MAJORITY)
    .build()
}
