package com.simonmarkets.sales.fee.rules.common.encoders

import com.simonmarkets.networks.common.encoders.BsonFormat
import com.simonmarkets.networks.common.{ChangelogItem, EntitySnapshot}
import com.simonmarkets.sales.fee.rules.common.SalesFeeRule
import org.mongodb.scala.bson.collection.Document

import java.time.{LocalDateTime, ZoneId}
import java.util.Date

object SalesFeeRuleSnapshotFormat extends BsonFormat[EntitySnapshot[SalesFeeRule]] {

  object Fields {
    val SnapshotId = "id"
    val UserId = "userId"
    val ModificationDate = "modificationDate"
    val Comment = "comment"
    val Entity = "entity"
  }

  override def write(v: EntitySnapshot[SalesFeeRule]): Document = {
    import Fields._
    Document(
      SnapshotId -> v.id,
      UserId -> v.userId,
      ModificationDate -> Date.from(v.modificationDate.atZone(ZoneId.systemDefault()).toInstant),
      Comment -> v.comment,
      Entity -> SalesFeeRuleFormat.write(v.entity)
    )
  }

  override def read(d: Document): EntitySnapshot[SalesFeeRule] = {
    import Fields._
    EntitySnapshot(
      id = d.getString(SnapshotId),
      userId = d.getString(UserId),
      modificationDate = LocalDateTime.ofInstant(d.getDate(ModificationDate).toInstant, ZoneId.systemDefault()),
      comment = Option(d.getString(Comment)),
      entity = d.get(Entity).map( d => SalesFeeRuleFormat.read(d.asDocument())).get
    )
  }

  def readChangelogItem(d: Document): ChangelogItem = {
    import Fields._
    ChangelogItem(
      id = d.getString(SnapshotId),
      userId = d.getString(UserId),
      modificationDate = LocalDateTime.ofInstant(d.getDate(ModificationDate).toInstant, ZoneId.systemDefault()),
      comment = Option(d.getString(Comment))
    )
  }
}
