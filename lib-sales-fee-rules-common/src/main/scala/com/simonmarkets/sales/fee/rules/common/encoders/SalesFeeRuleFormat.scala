package com.simonmarkets.sales.fee.rules.common.encoders

import com.simonmarkets.networks.common.encoders.BsonFormat
import com.simonmarkets.sales.fee.rules.common.{FeeSchedule, FeeType, SalesFeeRule}
import org.mongodb.scala.bson.BsonDecimal128
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.bson.collection.mutable.{Document => MutableDoc}

import scala.collection.JavaConverters._

object SalesFeeRuleFormat extends BsonFormat[SalesFeeRule] {

  object Fields {
    val SalesFeeRuleId = "id"
    val Name = "name"
    val Version = "version"
    val Deleted = "deleted"
    val FeesType = "feeType"
    val IssuerKeys = "issuerKeys"
    val ContractTypes = "contractTypes"
    val ContractWrappers = "contractTypeWrappers"
    val Fees = "fees"
    val Callable = "callable"
    val FullyProtected = "fullyProtected"
    val HasSinglePayment = "hasSinglePayment"
    val NonCallPeriodInMonths = "nonCallPeriodInMonths"
  }

  override def write(feeRule: SalesFeeRule): Document = {
    import Fields._

    val feeRuleDocument: MutableDoc = MutableDoc(
      SalesFeeRuleId -> feeRule.id,
      Name -> feeRule.name,
      Version -> feeRule.version,
      Deleted -> feeRule.deleted,
      FeesType -> feeRule.feeType.name,
      IssuerKeys -> feeRule.issuerKeys,
      ContractTypes -> feeRule.contractTypes,
      ContractWrappers -> feeRule.contractTypeWrappers,
      Fees -> feeRule.fees.data.map(kv => (kv._1.toString, kv._2.toDouble)).toList
    )
    feeRule.callable.foreach(callable => feeRuleDocument.update(Callable, callable))
    feeRule.fullyProtected.foreach(fp => feeRuleDocument.update(FullyProtected, fp))
    feeRule.hasSinglePayment.foreach(hasSP => feeRuleDocument.update(HasSinglePayment, hasSP))
    feeRule.nonCallPeriodInMonths.foreach(ncMonth => feeRuleDocument.update(NonCallPeriodInMonths, ncMonth))
    Document(feeRuleDocument.toBsonDocument)
  }

  override def read(d: Document): SalesFeeRule = {
    import Fields._

    SalesFeeRule(
      id = d.getString(SalesFeeRuleId),
      name = d.getString(Name),
      version = d.get(Version).get.asNumber().intValue(),
      deleted = d.getBoolean(Deleted),
      feeType = FeeType(d.getString(FeesType)),
      issuerKeys = d.getList(IssuerKeys, classOf[String]).asScala.toList,
      contractTypes = d.getList(ContractTypes, classOf[String]).asScala.toList,
      contractTypeWrappers = d.getList(ContractWrappers, classOf[String]).asScala.toList,
      fees = FeeSchedule(d(Fees).asDocument().entrySet().asScala.map(kv => (kv.getKey.toInt, BigDecimal(kv.getValue.asDouble().doubleValue()))).toMap),
      callable = bsonValueToBoolean(Callable, d),
      fullyProtected = bsonValueToBoolean(FullyProtected, d),
      hasSinglePayment = bsonValueToBoolean(HasSinglePayment, d),
      nonCallPeriodInMonths = bsonValueToBigDecimal(NonCallPeriodInMonths, d)
    )
  }

  def bsonValueToBoolean(fieldName: String, document: Document): Option[Boolean] = {
    if (document.contains(fieldName)) Some(document.getBoolean(fieldName)) else None
  }

  def bsonValueToBigDecimal(fieldName: String, document: Document): Option[BigDecimal] = {
    if (document.contains(fieldName)) {
      document.get[BsonDecimal128](fieldName).map(v => v.getValue.bigDecimalValue())
    } else None
  }

  def bsonValueToOptionBigDecimal(fieldName: String, document: Document): Option[BigDecimal] = {
    if (document.contains(fieldName)) Some(BigDecimal(document.getDouble(fieldName))) else None
  }
}
