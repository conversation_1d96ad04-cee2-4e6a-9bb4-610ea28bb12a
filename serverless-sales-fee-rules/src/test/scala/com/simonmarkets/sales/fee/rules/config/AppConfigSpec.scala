package com.simonmarkets.sales.fee.rules.config

import com.simonmarkets.utils.testkit.config.ConfigFileMatchers
import org.scalatest.{Matchers, WordSpec}
import pureconfig.generic.auto._


class AppConfigSpec extends WordSpec with ConfigFileMatchers with Matchers {
  "AppConfig" should {
    "have valid configs in all environments" in {
      all(Set(
        "alpha.conf",
        "local.conf",
//        "okteto.conf",
        "prod.conf",
        "qa.conf",
      )) should beConfig[AppConfig]
    }
  }
}

