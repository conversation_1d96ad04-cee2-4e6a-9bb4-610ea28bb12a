# SIMON EKS service deployment using Gitlab CI/CD pipeline and Argocd

## Main Design Goals
  1. __EKS service's manifests CD automation__
  2. __Integrate with our current Gitlab CI/CD pipelines__
  3. __To enforce syncing between current cluster state and target state in the Git repoisitory(GitOps)__ 
  4. __Give developers some insights of the settings of services running inside eks cluster__
  5. __Allow developers to change some settings of application/service/cronjob in the Git repositories.__ 

## Design & Architecture
![](/docs/SIMON_EKS_Service_CD_Design.png)

## Directory structure

```console
.
├── Makefile
├── README.md
├── charts
│   ├── argocd-application
│   ├── argocd-project
│   ├── argocd-repo-creds
│   ├── argocd-repository
│   ├── service-alb
│   ├── service-cron
│   └── service-elb
├── docs
└── examples
    ├── applications
    ├── projects
    ├── repositories
    └── ...
```

* `Makefile` is used to generate all Argocd required manifests and templates.
* `charts` contains all HELM charts for SIMON microservices and Argocd resources.
* `docs` contains all document related files.
* `examples`:
  - `projects` contains all Argocd project manifests, which are one to one map to SIMON microservice projects.
  - `applicatoins` contains all Argocd application manifests which are mapped to SIMON microservice applications.
  - `repositories` contains all Argocd reposiotry manifests, which are mapped to SIMON gitlab repositories.

## Usage (DevOps)
**simon-argocd git submodule**

1. Create simon-argocd git submodule in each project repo
    ```console
    mkdir -p .argocd && cd ./.argocd
    git <NAME_EMAIL>:simonmarkets/app-deploy/simon-argocd.git
    ```   
2. Update simon-argocd git submodule
    ```console
    git submodule update --init --recursive --remote
    ```
NB: In order to make git submodule work with Gitlab CI/CD, you need to change `url` in .gitmodules file to relative url like `../../repo path`


**eks-config values.yaml files**

Create below eks-config directories and files in each service project repo

NB: The values in service/cronjob specific values file will override the same values in top level values file. 

Use cases: (Developer)

1. Each environment will have it's own set of settings.(Not recommeneded, because it will open a backdoor for developers or other users to use different settings in different environments, error-prone to human mistakes, and bypass CI/CD and QA processes.)

  ```console
    .
    ├── eks-config
    │   ├── values.yaml # Top level service values file
    │   └── cron-values.yaml # Top level cronjob values file
    ├── <service_name folder>
    │   ├── values.yaml # Optional service specific values file
    │   ├── values.alpha.yaml # Optional Alpha environment specific values file 
    │   ├── values.qa.yaml # Optional QA environment specific values file 
    │   └── values.prod.yaml # Optional PROD environment specific values file 
    ├── <cronjob_name folder>
    │   ├── values.yaml # Optional service specific values file
    │   ├── values.alpha.yaml # Optional Alpha environment specific values file 
    │   ├── values.qa.yaml # Optional QA environment specific values file 
    │   └── values.prod.yaml # Optional PROD environment specific values file 
    └── ...
    
  ```

Example values.yaml files:

* Top level application/service values.yaml file
  ```yaml
    ##
    # AWS eks cluster settings
    ##

    # AWS eks IRSA settings
    serviceAccount:
      created: false # While false, this will NOT generate a service account.
      name: ""
      iamARN: ""

    # SIMON service settings
    service:
      name: demo-app
      scheme: https
      port: 443
      cpu: "250m"
      memory: "512Mi"
      healthCheck:
        # Default: /simon/api/v1/healthcheck
        path: /simon/api/healthcheck
        initialDelay: 30
        interval: 5
        timeout: 15
        failureThreshold: 3
        
    # Exposed service's loadbalancer settings
    loadbalancer:
      connectionIdleTimeout: 300

    # AWS eks deployment settings
    deploy:
      replicas: 2

    # Enable proxy settings (default: false)
    proxyEnabled: true
  ```

* Top level cronjob service values.yaml file
  ```yaml
  ##
  # SIMON eks cronjob and cluster settings
  ##
 
  # AWS eks IRSA settings  
  serviceAccount:
    created: false # While false, this will NOT generate a service account.
    name: ""
    iamARN: ""

  # SIMON service settings
  service: 
    name: "demo-app"
    cpu: "250m"
    memory: "512Mi" 

  # AWS eks deployment settings
  deploy:
    # Default schedule "0 0 ? * MON-FRI"
    schedule: ""
    # Default false
    suspend: ""
    # Default Never
    restart: ""
    # Default Forbid
    concurrency: ""
    # Default 3600 secs
    activeDeadline: 3600
    # Java main class
    java_class: ""

  # Enable proxy settings (default: false)
  proxyEnabled: true
  ```

* Please Note: For service specific values files
```yaml
    serviceAccount:
      created: true # When true, service accounts will be generated on deploy.
      name: "Service-My-Exmaple" # Should follow the correct naming convetion
      iamARN: "arn:aws:iam::###########:role/service-my-example-service-<env>-role" # Should be supplied by the devops teams and follow the proper naming convention

```

## The ArgoCD readonly UI for Alpha and QA environments

They provide you the insights into your service structure, health information, and logs.

* Alpha: 
[https://internal-a0ec4dedb7fed4469b06fdd28dbe1810-*********.us-east-1.elb.amazonaws.com](https://internal-a0ec4dedb7fed4469b06fdd28dbe1810-*********.us-east-1.elb.amazonaws.com)

* QA:
[https://internal-abde9fb6c02c5440bb4561c99fd328d5-**********.us-east-1.elb.amazonaws.com](https://internal-abde9fb6c02c5440bb4561c99fd328d5-**********.us-east-1.elb.amazonaws.com)
