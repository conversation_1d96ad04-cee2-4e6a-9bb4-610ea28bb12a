package com.goldmansachs.marquee.pipg.service.user

import com.goldmansachs.marquee.pipg.{License, User}

import java.time.LocalDateTime

import scala.language.implicitConversions

sealed trait UsersProjection extends Product with Serializable { type T }

object UsersProjection {

  sealed trait Composite[TT] extends UsersProjection { type T = TT }

  sealed trait Simple[TT] extends UsersProjection { type T = TT }

  case object Entire extends UsersProjection { type T = User }

  case object Id extends Simple[String]

  case object NetworkId extends Simple[simon.Id.NetworkId]

  case object Email extends Simple[String]

  case object FirstName extends Simple[String]

  case object LastName extends Simple[String]

  case object LastVisitedAt extends Simple[Option[LocalDateTime]]

  case object DistributorId extends Simple[Option[String]]

  case object Licenses extends Simple[Set[License]]

  private[user] case class Composite2[T1, T2] (_1: Simple[T1], _2: Simple[T2]) extends Composite[(T1, T2)]

  private[user] case class Composite3[T1, T2, T3] (_1: Simple[T1], _2: Simple[T2], _3: Simple[T3]) extends Composite[(T1, T2, T3)]

  private[user] case class Composite4[T1, T2, T3, T4] (_1: Simple[T1], _2: Simple[T2], _3: Simple[T3], _4: Simple[T4]) extends Composite[(T1, T2, T3, T4)]

  private[user] case class Composite5[T1, T2, T3, T4, T5] (_1: Simple[T1], _2: Simple[T2], _3: Simple[T3], _4: Simple[T4], _5: Simple[T5]) extends Composite[(T1, T2, T3, T4, T5)]

  private[user] case class Composite6[T1, T2, T3, T4, T5, T6] (_1: Simple[T1], _2: Simple[T2], _3: Simple[T3], _4: Simple[T4], _5: Simple[T5], _6: Simple[T6]) extends Composite[(T1, T2, T3, T4, T5, T6)]

  def Composite[T1, T2](_1: Simple[T1], _2: Simple[T2]): Composite[(T1, T2)] = Composite2(_1, _2)

  def Composite[T1, T2, T3](_1: Simple[T1], _2: Simple[T2], _3: Simple[T3]): Composite[(T1, T2, T3)] = Composite3(_1, _2, _3)

  def Composite[T1, T2, T3, T4](_1: Simple[T1], _2: Simple[T2], _3: Simple[T3], _4: Simple[T4]): Composite[(T1, T2, T3, T4)] = Composite4(_1, _2, _3, _4)

  def Composite[T1, T2, T3, T4, T5](_1: Simple[T1], _2: Simple[T2], _3: Simple[T3], _4: Simple[T4], _5: Simple[T5]): Composite[(T1, T2, T3, T4, T5)] = Composite5(_1, _2, _3, _4, _5)

  def Composite[T1, T2, T3, T4, T5, T6](_1: Simple[T1], _2: Simple[T2], _3: Simple[T3], _4: Simple[T4], _5: Simple[T5], _6: Simple[T6]): Composite[(T1, T2, T3, T4, T5, T6)] = Composite6(_1, _2, _3, _4, _5, _6)

  implicit def tuple2ToComposite[T1, T2](x: (Simple[T1], Simple[T2])): Composite[(T1, T2)] = Composite(x._1, x._2)

  implicit def tuple3ToComposite[T1, T2, T3](x: (Simple[T1], Simple[T2], Simple[T3])): Composite[(T1, T2, T3)] = Composite(x._1, x._2, x._3)

  implicit def tuple4ToComposite[T1, T2, T3, T4](x: (Simple[T1], Simple[T2], Simple[T3], Simple[T4])): Composite[(T1, T2, T3, T4)] = Composite(x._1, x._2, x._3, x._4)

  implicit def tuple5ToComposite[T1, T2, T3, T4, T5](x: (Simple[T1], Simple[T2], Simple[T3], Simple[T4], Simple[T5])): Composite[(T1, T2, T3, T4, T5)] = Composite(x._1, x._2, x._3, x._4, x._5)

  implicit def tuple6ToComposite[T1, T2, T3, T4, T5, T6](x: (Simple[T1], Simple[T2], Simple[T3], Simple[T4], Simple[T5], Simple[T6])): Composite[(T1, T2, T3, T4, T5, T6)] = Composite(x._1, x._2, x._3, x._4, x._5, x._6)
}
