package com.goldmansachs.marquee.pipg.service.user

import com.goldmansachs.marquee.pipg.{UserACL, UserRole}
import com.gs.marquee.util.NonEmptyList


sealed trait UsersLookup

object UsersLookup {

  case object All extends UsersLookup

  case class Id(value: String) extends UsersLookup

  object Id {

    def apply(value: Option[String]): UsersLookup = value.fold[UsersLookup](UsersLookup.All)(UsersLookup.Id.apply)
  }

  case class Ids(values: NonEmptyList[String]) extends UsersLookup

  object Ids {


    def apply(x: String, xs: String*): UsersLookup = Ids((x +: xs).toSet)

    def apply(values: Iterable[String]): UsersLookup = {
      if (values.isEmpty) UsersLookup.All
      else if (values.size == 1) Id(values.head)
      else new Ids(NonEmptyList.unsafe(values.toSet))
    }

    def apply(value: Option[Iterable[String]]): UsersLookup = value.fold[UsersLookup](UsersLookup.All)(ids => UsersLookup.Ids(ids.toSet))
  }

  case class Npn(value: String) extends UsersLookup

  object Npn {
    def apply(value: Option[String]): UsersLookup = value.fold[UsersLookup](UsersLookup.All)(UsersLookup.Npn.apply)
  }

  case class Npns(values: NonEmptyList[String]) extends UsersLookup

  object Npns {
    def apply(x: String, xs: String*): UsersLookup = Npns((x +: xs).toSet)

    def apply(values: Iterable[String]): UsersLookup = {
      if(values.isEmpty) UsersLookup.All
      else if (values.size == 1) Npn(values.head)
      else new Npns(NonEmptyList.unsafe(values.toSet))
    }
  }

  case class NetworkId(value: simon.Id.NetworkId) extends UsersLookup

  object NetworkId {

    def apply(value: Option[simon.Id.NetworkId]): UsersLookup = value.fold[UsersLookup](UsersLookup.All)(UsersLookup.NetworkId.apply)
  }

  case class NetworkIds (values: NonEmptyList[simon.Id.NetworkId]) extends UsersLookup {
    override def equals(obj: Any): Boolean = canEqual(obj) && obj.asInstanceOf[NetworkIds].values == values
  }

  object NetworkIds {

    def apply(x: simon.Id.NetworkId, xs: simon.Id.NetworkId*): UsersLookup = NetworkIds((x +: xs).toSet)

    def apply(values: Iterable[simon.Id.NetworkId]): UsersLookup = {
      if (values.isEmpty) UsersLookup.All
      else if (values.size == 1) NetworkId(values.head)
      else new NetworkIds(NonEmptyList.unsafe(values.toSet))
    }

    def apply(value: Option[Iterable[simon.Id.NetworkId]]): UsersLookup = value.fold[UsersLookup](UsersLookup.All) { ids =>
      UsersLookup.NetworkIds(ids.toSeq)
    }
  }

  case class Roles(values: Set[UserRole]) extends UsersLookup

  object Role {

    def apply(x: UserRole): Roles = Roles(Set(x))
  }

  object Roles {

    def apply(x: UserRole, xs: UserRole*): UsersLookup = if (xs.isEmpty) UsersLookup.Role(x) else UsersLookup.Roles((x +: xs).toSet)
  }

  case class Email(value: String) extends UsersLookup

  case class Emails(values: NonEmptyList[String]) extends UsersLookup

  object Emails {
    def apply(x: String, xs: String*): UsersLookup = Emails((x +: xs).toSet)

    def apply(values: Iterable[String]): UsersLookup = {
      if(values.isEmpty) UsersLookup.All
      else if (values.size == 1) Email(values.head)
      else new Emails(NonEmptyList.unsafe(values.toSet))
    }
  }

  case class DynamicRoles(values: Set[String]) extends UsersLookup

  object DynamicRoles {

    def apply(x: String, xs: String*): UsersLookup = UsersLookup.DynamicRoles((x +: xs).toSet)
  }

  case class Entitlements(values: Set[String]) extends UsersLookup {

    def forAction(action: String): Entitlements = copy(values = values map { action + ":" + _ })
  }

  object Entitlements {

    def apply(authz: UserACL): Entitlements = Entitlements(authz.dynamicRoles)

    def apply(x: String, xs: String*): UsersLookup = UsersLookup.Entitlements((x +: xs).toSet)
  }

  case class NameContains(value: String) extends UsersLookup

  case class DistributorId(value: Set[String]) extends UsersLookup

  case class OmsId(value: Set[String]) extends UsersLookup

  object OmsId {
    def apply(x: String, xs: String*): UsersLookup = OmsId((x +: xs).toSet)

    def apply(value: String): OmsId = OmsId(Set(value))

    def apply(values: Iterable[String]): UsersLookup = {
      if(values.isEmpty) UsersLookup.All
      else if (values.size == 1) OmsId(values.head)
      else new OmsId(values.toSet)
    }

  }

  object DistributorId {
    def apply(x: String, xs: String*): UsersLookup = DistributorId((x +: xs).toSet)

    def apply(value: String): DistributorId = DistributorId(Set(value))

    def apply(values: Iterable[String]): UsersLookup = {
      if (values.isEmpty) UsersLookup.All
      else if (values.size == 1) DistributorId(values.head)
      else new DistributorId(values.toSet)
    }
  }

  case class MaskedId(id: String, target: Option[String]) extends UsersLookup

  case class And(lookups: UsersLookup*) extends UsersLookup

  case class Or(lookups: UsersLookup*) extends UsersLookup

  case class Not(lookup: UsersLookup) extends UsersLookup

  implicit class UsersLookupOps(val left: UsersLookup) extends AnyVal {

    def and(right: UsersLookup): UsersLookup =
      (left, right) match {
        case (All, All) => All
        case (All, right) => right
        case (left, All) => left
        case (left, right) => And(left, right)
      }

    def &(right: UsersLookup): UsersLookup = left and right

    def or(right: UsersLookup): UsersLookup =
      (left, right) match {
        case (All, All) => All
        case (All, _) => All
        case (_, All) => All
        case (left, right) => Or(left, right)
      }

    def |(right: UsersLookup): UsersLookup = left or right
  }

  def not(right: UsersLookup): UsersLookup = Not(right)
  def !(right: UsersLookup): UsersLookup = not(right)
}